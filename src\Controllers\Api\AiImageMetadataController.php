<?php

declare(strict_types=1);

namespace Brenzley\Controllers\Api;

use <PERSON><PERSON><PERSON>y\Core\Session;
use <PERSON><PERSON><PERSON>y\Services\ApiProviders\ApiService;
use Brenzley\Services\ApiProviders\ContentGeneratorFactory;
use B<PERSON>zley\Models\ApiSettingsModel;

/**
 * Controller for AI image metadata generation (captions and alt text)
 */
class AiImageMetadataController extends AiContentController
{
    private $contentGenerator = null;
    
    /**
     * Initialize content generator
     */
    private function initializeContentGenerator(): void
    {
        $apiSettingsModel = new ApiSettingsModel();
        $activeProvider = $apiSettingsModel->getActiveProvider();
        
        if ($activeProvider && $activeProvider['provider'] !== 'none') {
            $this->contentGenerator = ContentGeneratorFactory::createFromDatabase($activeProvider['provider']);
        }
    }
    /**
     * Stream alt texts for a batch of images with SSE-like JSON messages
     * Expected JSON body: { images: [{ media_id?: number, description: string }], parameters?: object }
     * Emits events per image with types: start, item, end, error
     */
    public function streamAltTexts(): void
    {
        if (!Session::isLoggedIn()) {
            header('Content-Type: text/event-stream');
            echo "data: " . json_encode(['type' => 'error', 'message' => 'Authentication required']) . "\n\n";
            flush();
            exit;
        }
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            header('Content-Type: text/event-stream');
            echo "data: " . json_encode(['type' => 'error', 'message' => 'Invalid request method']) . "\n\n";
            flush();
            exit;
        }
        header('Content-Type: text/event-stream');
        header('Cache-Control: no-cache');
        header('Connection: keep-alive');
        header('X-Accel-Buffering: no');
        if (ob_get_level()) ob_end_clean();

        $data = json_decode(file_get_contents('php://input'), true) ?: [];
        $images = $data['images'] ?? [];
        $parameters = $data['parameters'] ?? [];
        if (!is_array($images) || empty($images)) {
            echo "data: " . json_encode(['type' => 'end', 'content' => ['items' => []]]) . "\n\n";
            flush();
            exit;
        }

        echo "data: " . json_encode(['type' => 'start', 'message' => 'Starting alt text generation...']) . "\n\n";
        flush();

        // Prepare all descriptions for batch processing
        $validImages = [];
        $imageMap = [];
        
        foreach ($images as $idx => $img) {
            $desc = (string)($img['description'] ?? '');
            $mediaId = isset($img['media_id']) ? (int)$img['media_id'] : null;
            $frontendIndex = isset($img['index']) ? (int)$img['index'] : $idx;
            
            if ($desc === '') {
                echo "data: " . json_encode(['type' => 'item', 'index' => $frontendIndex, 'status' => 'skipped', 'reason' => 'no_description']) . "\n\n";
                flush();
                continue;
            }
            
            $validImages[] = [
                'index' => $frontendIndex,
                'description' => $desc,
                'media_id' => $mediaId
            ];
            $imageMap[$frontendIndex] = ['description' => $desc, 'media_id' => $mediaId];
        }
        
        if (empty($validImages)) {
            echo "data: " . json_encode(['type' => 'end', 'message' => 'No valid images to process']) . "\n\n";
            flush();
            exit;
        }
        
        try {
            // Generate all alt texts in a single API call
            $batchResult = $this->generateBatchAltTexts($validImages, $parameters);
            
            if ($batchResult['success']) {
                $altTexts = $batchResult['alt_texts'];
                
                // Process and save each result
                foreach ($validImages as $imgData) {
                    $frontendIndex = $imgData['index'];
                    $mediaId = $imgData['media_id'];
                    $alt = $altTexts[$frontendIndex] ?? $this->createFallbackAlt($imgData['description']);
                    
                    $metaId = null;
                    if ($mediaId) {
                        // Save to backend if media id exists
                        $metadata = $this->imageModel->findByMediaId($mediaId);
                        $activeProvider = $this->apiSettingsModel->getActiveProvider();
                        if ($metadata) {
                            $this->imageModel->update($metadata['id'], [
                                'alt_text' => $alt,
                                'provider' => $activeProvider['provider'] ?? 'unknown',
                                'model' => $activeProvider['model'] ?? 'unknown',
                                'parameters' => $parameters
                            ]);
                            $metaId = $metadata['id'];
                        } else {
                            $metaId = $this->imageModel->create([
                                'user_id' => Session::getUserId(),
                                'media_id' => $mediaId,
                                'alt_text' => $alt,
                                'provider' => $activeProvider['provider'] ?? 'unknown',
                                'model' => $activeProvider['model'] ?? 'unknown',
                                'parameters' => $parameters
                            ]);
                        }
                    }

                    echo "data: " . json_encode([
                        'type' => 'item',
                        'index' => $frontendIndex,
                        'status' => 'ok',
                        'alt_text' => $alt,
                        'media_id' => $mediaId,
                        'metadata_id' => $metaId,
                    ]) . "\n\n";
                    flush();
                }
            } else {
                // If batch generation fails, provide fallbacks
                foreach ($validImages as $imgData) {
                    $frontendIndex = $imgData['index'];
                    $alt = $this->createFallbackAlt($imgData['description']);
                    
                    echo "data: " . json_encode([
                        'type' => 'item',
                        'index' => $frontendIndex,
                        'status' => 'error',
                        'message' => $batchResult['message'] ?? 'Batch generation failed',
                        'alt_text' => $alt
                    ]) . "\n\n";
                    flush();
                }
            }
        } catch (\Throwable $e) {
            // If anything fails, provide fallbacks for all images
            foreach ($validImages as $imgData) {
                $frontendIndex = $imgData['index'];
                $alt = $this->createFallbackAlt($imgData['description']);
                
                echo "data: " . json_encode([
                    'type' => 'item',
                    'index' => $frontendIndex,
                    'status' => 'error',
                    'message' => $e->getMessage(),
                    'alt_text' => $alt
                ]) . "\n\n";
                flush();
            }
        }

        echo "data: " . json_encode(['type' => 'end', 'message' => 'Alt text generation completed']) . "\n\n";
        flush();
        exit;
    }

    /**
     * Generate alt texts for multiple images in a single API call with strict JSON format
     *
     * @param array $images Array of image data with index, description, media_id
     * @param array $parameters API parameters
     * @return array Result with success status and alt_texts array
     */
    private function generateBatchAltTexts(array $images, array $parameters = []): array
    {
        $apiService = new ApiService();
        
        // Build the strict prompt with multiple enforcement
        $imageList = [];
        foreach ($images as $img) {
            $imageList[] = [
                'index' => $img['index'],
                'description' => $img['description']
            ];
        }
        
        $prompt = "You are an expert at creating concise, descriptive alt text for images. You MUST follow the exact JSON format specified below.

CRITICAL INSTRUCTIONS - READ CAREFULLY:
1. You MUST respond with ONLY valid JSON - no other text, explanations, or formatting
2. You MUST include ALL images provided in the response
3. You MUST use the exact index numbers provided
4. You MUST follow the exact JSON structure shown below
5. Alt text should be concise (under 125 characters) and descriptive
6. Do NOT include phrases like 'image of', 'picture of', 'photo of' - be direct

REQUIRED JSON FORMAT (follow this EXACTLY):
{
  \"alt_texts\": {
    \"0\": \"descriptive alt text here\",
    \"1\": \"descriptive alt text here\",
    \"2\": \"descriptive alt text here\"
  }
}

IMPORTANT: The JSON format above is MANDATORY. Do not deviate from it. Use the exact index numbers provided below.

Generate alt text for these images:
";

        foreach ($imageList as $img) {
            $prompt .= "\nIndex {$img['index']}: {$img['description']}";
        }
        
        $prompt .= "\n\nRemember: Respond with ONLY the JSON object in the exact format shown above. Include all " . count($imageList) . " images. Use the exact index numbers. No additional text or formatting.";
        
        // Set parameters for consistent output
        $batchParameters = array_merge($parameters, [
            'temperature' => 0.2, // Low temperature for consistency
            'max_tokens' => 1000,  // Enough for multiple alt texts
        ]);
        
        // Create a direct API call bypassing the prompt manager
        if (!$this->contentGenerator) {
            $this->initializeContentGenerator();
        }
        
        if (!$this->contentGenerator) {
            return [
                'success' => false,
                'message' => 'No active AI provider configured'
            ];
        }
        
        $result = $this->contentGenerator->generateBlogPost($prompt, $batchParameters);
        
        if (!$result['success']) {
            return [
                'success' => false,
                'message' => $result['message'] ?? 'Failed to generate batch alt texts'
            ];
        }
        
        $content = trim($result['blog_post'] ?? '');
        
        // Parse the JSON response
        $parsedData = json_decode($content, true);
        
        if (json_last_error() !== JSON_ERROR_NONE || !isset($parsedData['alt_texts'])) {
            // Try to extract JSON from the response if it's wrapped in other text
            if (preg_match('/\{[^{}]*"alt_texts"[^{}]*\{[^}]*\}[^{}]*\}/s', $content, $matches)) {
                $parsedData = json_decode($matches[0], true);
            }
            
            if (json_last_error() !== JSON_ERROR_NONE || !isset($parsedData['alt_texts'])) {
                return [
                    'success' => false,
                    'message' => 'Invalid JSON response from AI provider'
                ];
            }
        }
        
        $altTexts = [];
        foreach ($images as $img) {
            $index = $img['index'];
            $indexStr = (string)$index;
            
            if (isset($parsedData['alt_texts'][$indexStr])) {
                $altTexts[$index] = trim($parsedData['alt_texts'][$indexStr]);
            } elseif (isset($parsedData['alt_texts'][$index])) {
                $altTexts[$index] = trim($parsedData['alt_texts'][$index]);
            } else {
                // Fallback if index is missing
                $altTexts[$index] = $this->createFallbackAlt($img['description']);
            }
        }
        
        return [
            'success' => true,
            'alt_texts' => $altTexts
        ];
    }
    
    /**
     * Create a fallback alt text from description
     *
     * @param string $description Image description
     * @return string Fallback alt text
     */
    private function createFallbackAlt(string $description): string
    {
        // Simple fallback: take first 100 characters and clean up
        $alt = trim($description);
        if (strlen($alt) > 100) {
            $alt = substr($alt, 0, 97) . '...';
        }
        
        // Remove common prefixes
        $alt = preg_replace('/^(image of|picture of|photo of|illustration of)\s+/i', '', $alt);
        
        return ucfirst($alt);
    }
    /**
     * Generate image caption
     */
    public function generateCaption(): void
    {
        // Check authentication
        if (!Session::isLoggedIn()) {
            $this->jsonResponse(['success' => false, 'message' => 'Authentication required'], 401);
            return;
        }

        // Validate request method
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->jsonResponse(['success' => false, 'message' => 'Invalid request method'], 405);
            return;
        }

        // Get request data
        $data = json_decode(file_get_contents('php://input'), true);

        if (!isset($data['image_description']) || empty($data['image_description'])) {
            $this->jsonResponse(['success' => false, 'message' => 'Image description is required'], 400);
            return;
        }

        if (!isset($data['media_id']) || empty($data['media_id'])) {
            $this->jsonResponse(['success' => false, 'message' => 'Media ID is required'], 400);
            return;
        }

        $imageDescription = $data['image_description'];
        $context = $data['context'] ?? '';
        $mediaId = (int)$data['media_id'];
        $parameters = $data['parameters'] ?? [];

        // Check if the media exists and belongs to the current user
        $mediaModel = new \Brenzley\Models\MediaModel();
        $media = $mediaModel->findById($mediaId);

        if (!$media) {
            $this->jsonResponse(['success' => false, 'message' => 'Media not found'], 404);
            return;
        }

        if ($media['user_id'] !== Session::getUserId()) {
            $this->jsonResponse(['success' => false, 'message' => 'Unauthorized access to media'], 403);
            return;
        }

        // Create API service
        $apiService = new ApiService();

        // Generate image caption
        $result = $apiService->generateImageCaption($imageDescription, $context, $parameters);

        if (!$result['success']) {
            $this->jsonResponse($result);
            return;
        }

        // Get existing metadata or create new
        $existingMetadata = $this->imageModel->findByMediaId($mediaId);

        if ($existingMetadata) {
            // Get active provider info
            $activeProvider = $this->apiSettingsModel->getActiveProvider();

            // Update existing metadata
            $updateData = [
                'caption' => $result['caption'],
                'provider' => $activeProvider['provider'] ?? 'unknown',
                'model' => $activeProvider['model'] ?? 'unknown',
                'parameters' => $parameters
            ];

            $success = $this->imageModel->update($existingMetadata['id'], $updateData);
            $metadataId = $existingMetadata['id'];
        } else {
            // Get active provider info
            $activeProvider = $this->apiSettingsModel->getActiveProvider();

            // Create new metadata
            $metadataData = [
                'user_id' => Session::getUserId(),
                'media_id' => $mediaId,
                'caption' => $result['caption'],
                'provider' => $activeProvider['provider'] ?? 'unknown',
                'model' => $activeProvider['model'] ?? 'unknown',
                'parameters' => $parameters
            ];

            $metadataId = $this->imageModel->create($metadataData);
            $success = $metadataId !== false;
        }

        if (!$success) {
            $this->jsonResponse([
                'success' => false,
                'message' => 'Failed to save image caption',
                'caption' => $result['caption']
            ], 500);
            return;
        }

        // Usage is now logged by the ApiService

        $this->jsonResponse([
            'success' => true,
            'caption' => $result['caption'],
            'metadata_id' => $metadataId
        ]);
    }


    /**
     * Generate alt text
     */
    public function generateAltText(): void
    {
        // Check authentication
        if (!Session::isLoggedIn()) {
            $this->jsonResponse(['success' => false, 'message' => 'Authentication required'], 401);
            return;
        }

        // Validate request method
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->jsonResponse(['success' => false, 'message' => 'Invalid request method'], 405);
            return;
        }

        // Get request data
        $data = json_decode(file_get_contents('php://input'), true);

        if (!isset($data['image_description']) || empty($data['image_description'])) {
            $this->jsonResponse(['success' => false, 'message' => 'Image description is required'], 400);
            return;
        }

        if (!isset($data['media_id']) || empty($data['media_id'])) {
            $this->jsonResponse(['success' => false, 'message' => 'Media ID is required'], 400);
            return;
        }

        $imageDescription = $data['image_description'];
        $mediaId = (int)$data['media_id'];
        $parameters = $data['parameters'] ?? [];

        // Check if the media exists and belongs to the current user
        $mediaModel = new \Brenzley\Models\MediaModel();
        $media = $mediaModel->findById($mediaId);

        if (!$media) {
            $this->jsonResponse(['success' => false, 'message' => 'Media not found'], 404);
            return;
        }

        if ($media['user_id'] !== Session::getUserId()) {
            $this->jsonResponse(['success' => false, 'message' => 'Unauthorized access to media'], 403);
            return;
        }

        // Create API service
        $apiService = new ApiService();

        // Generate alt text
        $result = $apiService->generateAltText($imageDescription, $parameters);

        if (!$result['success']) {
            $this->jsonResponse($result);
            return;
        }

        // Get existing metadata or create new
        $existingMetadata = $this->imageModel->findByMediaId($mediaId);

        if ($existingMetadata) {
            // Get active provider info
            $activeProvider = $this->apiSettingsModel->getActiveProvider();

            // Update existing metadata
            $updateData = [
                'alt_text' => $result['alt_text'],
                'provider' => $activeProvider['provider'] ?? 'unknown',
                'model' => $activeProvider['model'] ?? 'unknown',
                'parameters' => $parameters
            ];

            $success = $this->imageModel->update($existingMetadata['id'], $updateData);
            $metadataId = $existingMetadata['id'];
        } else {
            // Get active provider info
            $activeProvider = $this->apiSettingsModel->getActiveProvider();

            // Create new metadata
            $metadataData = [
                'user_id' => Session::getUserId(),
                'media_id' => $mediaId,
                'alt_text' => $result['alt_text'],
                'provider' => $activeProvider['provider'] ?? 'unknown',
                'model' => $activeProvider['model'] ?? 'unknown',
                'parameters' => $parameters
            ];

            $metadataId = $this->imageModel->create($metadataData);
            $success = $metadataId !== false;
        }

        if (!$success) {
            $this->jsonResponse([
                'success' => false,
                'message' => 'Failed to save alt text',
                'alt_text' => $result['alt_text']
            ], 500);
            return;
        }

        // Usage is now logged by the ApiService

        $this->jsonResponse([
            'success' => true,
            'alt_text' => $result['alt_text'],
            'metadata_id' => $metadataId
        ]);
    }

    /**
     * Get image metadata
     */
    public function get(): void
    {
        // Check authentication
        if (!Session::isLoggedIn()) {
            $this->jsonResponse(['success' => false, 'message' => 'Authentication required'], 401);
            return;
        }

        // Validate request method
        if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
            $this->jsonResponse(['success' => false, 'message' => 'Invalid request method'], 405);
            return;
        }

        // Get metadata ID from query parameters
        $metadataId = isset($_GET['id']) ? (int)$_GET['id'] : null;

        if (!$metadataId) {
            $this->jsonResponse(['success' => false, 'message' => 'Metadata ID is required'], 400);
            return;
        }

        // Get metadata
        $metadata = $this->imageModel->findById($metadataId);

        if (!$metadata) {
            $this->jsonResponse(['success' => false, 'message' => 'Metadata not found'], 404);
            return;
        }

        // Check if the metadata belongs to the current user
        if ($metadata['user_id'] !== Session::getUserId()) {
            $this->jsonResponse(['success' => false, 'message' => 'Unauthorized access to metadata'], 403);
            return;
        }

        $this->jsonResponse([
            'success' => true,
            'metadata' => $metadata
        ]);
    }

    /**
     * Get image metadata for media
     */
    public function getForMedia(): void
    {
        // Check authentication
        if (!Session::isLoggedIn()) {
            $this->jsonResponse(['success' => false, 'message' => 'Authentication required'], 401);
            return;
        }

        // Validate request method
        if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
            $this->jsonResponse(['success' => false, 'message' => 'Invalid request method'], 405);
            return;
        }

        // Get media ID from query parameters
        $mediaId = isset($_GET['media_id']) ? (int)$_GET['media_id'] : null;

        if (!$mediaId) {
            $this->jsonResponse(['success' => false, 'message' => 'Media ID is required'], 400);
            return;
        }

        // Check if the media exists and belongs to the current user
        $mediaModel = new \Brenzley\Models\MediaModel();
        $media = $mediaModel->findById($mediaId);

        if (!$media) {
            $this->jsonResponse(['success' => false, 'message' => 'Media not found'], 404);
            return;
        }

        if ($media['user_id'] !== Session::getUserId()) {
            $this->jsonResponse(['success' => false, 'message' => 'Unauthorized access to media'], 403);
            return;
        }

        // Get metadata
        $metadata = $this->imageModel->findByMediaId($mediaId);

        if (!$metadata) {
            $this->jsonResponse(['success' => false, 'message' => 'No metadata found for this media'], 404);
            return;
        }

        $this->jsonResponse([
            'success' => true,
            'metadata' => $metadata
        ]);
    }

    /**
     * Apply image metadata to media
     */
    public function applyToMedia(): void
    {
        // Check authentication
        if (!Session::isLoggedIn()) {
            $this->jsonResponse(['success' => false, 'message' => 'Authentication required'], 401);
            return;
        }

        // Validate request method
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->jsonResponse(['success' => false, 'message' => 'Invalid request method'], 405);
            return;
        }

        // Get request data
        $data = json_decode(file_get_contents('php://input'), true);

        if (!isset($data['metadata_id']) || empty($data['metadata_id'])) {
            $this->jsonResponse(['success' => false, 'message' => 'Metadata ID is required'], 400);
            return;
        }

        $metadataId = (int)$data['metadata_id'];

        // Get metadata
        $metadata = $this->imageModel->findById($metadataId);

        if (!$metadata) {
            $this->jsonResponse(['success' => false, 'message' => 'Metadata not found'], 404);
            return;
        }

        // Check if the metadata belongs to the current user
        if ($metadata['user_id'] !== Session::getUserId()) {
            $this->jsonResponse(['success' => false, 'message' => 'Unauthorized access to metadata'], 403);
            return;
        }

        // Apply metadata to media
        $success = $this->imageModel->applyToMedia($metadataId);

        if (!$success) {
            $this->jsonResponse(['success' => false, 'message' => 'Failed to apply metadata to media'], 500);
            return;
        }

        $this->jsonResponse([
            'success' => true,
            'message' => 'Metadata applied to media successfully'
        ]);
    }
}
