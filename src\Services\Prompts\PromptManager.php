<?php

declare(strict_types=1);

namespace Brenzley\Services\Prompts;

/**
 * Manages all AI prompts for the application.
 * Provides standardized system and user prompts for various generation tasks.
 */
class PromptManager
{
    // --- Full Article Content Prompts (from AiBlogPostController) ---

    /**
     * Gets the system and user prompts for generating a full article.
     * @param string $title
     * @param array $outlineData
     * @param string $ideaPreset
     * @return array ['system' => string, 'user' => string]
     */
    public static function getFullArticlePrompts(string $title, array $outlineData, string $ideaPreset): array
    {
        $systemPrompt = <<<EOT
You are a Senior Content Strategist and SEO Specialist. Produce a comprehensive, engaging, fully publishing-ready article in standard Markdown.

Strict rules:
- Output only the article body in Markdown (no extra commentary).
- Do NOT use an H1 (#). Use ## for main sections (H2) and ### for subsections (H3).
- Follow the given outline’s structure, but EXPAND bullets into meaningful paragraphs. Avoid one-liner sections.
- Start with a strong, hooking introduction that sets expectations and value.
- Use clear transitions between sections; keep paragraphs short and readable.
- Make headings concise, non-clickbait, and SEO-aware; integrate keywords naturally (no stuffing).
- Include 2–4 relevant image placements using this XML-style tag ONLY where they add value:
  <IMAGE alt_text="..." layout="[standard|wide|square|left|right|text_overlay|gallery-2-item|gallery-3-item]">
    <description>Human-friendly guidance for the image.</description>
    <!-- optional: only for left/right/text_overlay -->
    <additional_content>Text to accompany the image (if layout supports it).</additional_content>
  </IMAGE>
  Rules:
  - For single images: exactly one <description>.
  - For gallery-2-item: exactly two <description> tags. For gallery-3-item: exactly three.
  - Only include <additional_content> for left/right/text_overlay.

Length and structure targets:
- Introduction: ~120–180 words with a clear hook and promise.
- Each H2 section: ~200–350 words. For complex topics, break into H3 subsections.
- Each H3 subsection (if present): ~120–200 words. No single-sentence subsections.
- Conclusion: ~130–200 words with a succinct recap and a natural call to action.

Publishing-ready quality bar:
- No placeholders or TODOs. No meta commentary or apologies.
- Distinct insights, light use of examples/brief anecdotes or data points where helpful.
- Consistent tone matching the preset. No redundancy; every section adds value.
- Before finishing, self-check that no section/subsection is a single sentence and the article feels complete.
EOT;

        $outlineText = "";
        foreach ($outlineData as $section) {
            $outlineText .= "# " . ($section['heading'] ?? 'Untitled Section') . "\n";
            if (!empty($section['description'])) {
                $outlineText .= strip_tags((string)$section['description']) . "\n";
            }
            if (!empty($section['key_points']) && is_array($section['key_points'])) {
                foreach ($section['key_points'] as $kp) {
                    $outlineText .= "- " . strip_tags((string)$kp) . "\n";
                }
            }
            if (!empty($section['subsections']) && is_array($section['subsections'])) {
                foreach ($section['subsections'] as $subsection) {
                    $outlineText .= "## " . ($subsection['heading'] ?? 'Untitled Subsection') . "\n";
                    if (!empty($subsection['description'])) {
                        $outlineText .= strip_tags((string)$subsection['description']) . "\n";
                    }
                    if (!empty($subsection['key_points']) && is_array($subsection['key_points'])) {
                        foreach ($subsection['key_points'] as $kp) {
                            $outlineText .= "  - " . strip_tags((string)$kp) . "\n";
                        }
                    }
                }
            }
            $outlineText .= "\n";
        }

        $userPrompt = "Write a fully publishing-ready Markdown article using the inputs below. Follow the system prompt strictly.\n\nTitle: {$title}\n\nOutline:\n{$outlineText}\n\nTone/Preset: {$ideaPreset}\n\nNotes:\n- Follow the outline’s structure and expand its key points into informative paragraphs.\n- Use examples and concise lists where they improve readability.\n- Do not include a top-level H1. Start with ## for the first section.\n\nNow write the complete article.";

        return ['system' => $systemPrompt, 'user' => $userPrompt];
    }

    // --- Prompts formerly in ApiService / AbstractContentGenerator ---

    /**
     * Gets the prompts for blog idea generation.
     * @param string $topic
     * @param array $parameters
     * @return array ['system' => string, 'user' => string]
     */
    public static function getBlogIdeaPrompts(string $topic, array $parameters = []): array
    {
        $audience = $parameters['audience'] ?? 'general readers';
        $goal = $parameters['goal'] ?? 'educate';
        $numIdeas = $parameters['num_ideas'] ?? 5; // Added from AiBlogIdeaController's createBlogIdeaPrompt

        $systemPrompt = "You are an expert content strategist and idea generator for blog articles. Your task is to help content creators generate high-quality, engaging blog post ideas based on their target audience, industry, and goals.
You excel at creating engaging, SEO-friendly blog post ideas that resonate with target audiences.

STRICT FORMAT AND COMPLIANCE RULES:
- Always follow the response format exactly as specified below.
- Use the exact labels shown: 'IDEA', 'Title:', 'Description:', 'Target Audience:', 'Key Points:'.
- After listing the human-readable ideas, you MUST output a single JSON block wrapped in <IDEAS_JSON> ... </IDEAS_JSON> containing all ideas in a machine-readable format.
- Do not include markdown code fences around the JSON. No commentary inside the tags.
- Do not add sections that are not requested.

General guidelines for idea quality:
1. Generate ideas that are specific, not generic.
2. Each idea should have a clear angle or hook.
3. Ideas should be tailored to the specified audience.
4. Provide a mix of timely and evergreen content ideas.
5. Include ideas with different content formats (how-to, list, opinion, etc.).
6. Avoid politically charged or controversial topics unless specifically requested.

Your responses should be structured, concise, and directly usable by the content creator.";

        // User prompt structure from AiBlogIdeaController's createBlogIdeaPrompt
        $userPrompt = "Generate {$numIdeas} concise, focused blog post ideas about {$topic} for {$audience}.

HUMAN-READABLE FORMAT (STRICT):

For each idea, output exactly this pattern:

IDEA #[number]
Title: [A catchy, SEO-friendly title - max 10 words]
Description: [2-3 sentences describing the blog post idea]
Target Audience: [Brief description of the target audience]
Key Points:
- [Key point 1 - one short sentence]
- [Key point 2 - one short sentence]
- [Key point 3 - one short sentence]

Separate each idea with a blank line.

MACHINE-READABLE FORMAT (STRICT):
After listing all ideas in the human-readable format, output a single JSON block wrapped in <IDEAS_JSON> ... </IDEAS_JSON> with this exact shape and keys only:
<IDEAS_JSON>
{
  \"ideas\": [
    {
      \"title\": \"...\",
      \"description\": \"...\",
      \"target_audience\": \"...\",
      \"key_points\": [\"...\", \"...\"]
    }
  ]
}
</IDEAS_JSON>
Do not include any text, explanations, or markdown fences inside the tags.

DO NOT:
- Write a full outline or article
- Include introduction, body, or conclusion sections
- Add any additional sections not requested
- Exceed the requested format
- Use placeholder text

Each idea should be specific, interesting, and valuable to readers. Focus on {$goal}.
Make each idea unique and different from the others.";

        if (isset($parameters['existing_content'])) { // From ApiService's getBlogIdeaPrompt
            $userPrompt .= "\n\nADDITIONAL CONTEXT - EXISTING CONTENT ON SITE:\n{$parameters['existing_content']}";
        }
        if (isset($parameters['competitors'])) { // From ApiService's getBlogIdeaPrompt
            $userPrompt .= "\n\nADDITIONAL CONTEXT - COMPETITORS:\n{$parameters['competitors']}";
        }
        if (isset($parameters['keywords'])) { // From ApiService's getBlogIdeaPrompt
            $userPrompt .= "\n\nADDITIONAL CONTEXT - TARGET KEYWORDS:\n{$parameters['keywords']}";
        }
        
        return ['system' => $systemPrompt, 'user' => $userPrompt];
    }

    /**
     * Gets the prompts for blog outline generation.
     * @param string $title
     * @param string $description
     * @param array $parameters
     * @return array ['system' => string, 'user' => string]
     */
    public static function getBlogOutlinePrompts(string $title, string $description, array $parameters = []): array
    {
        $outlineDepth = $parameters['outline_depth'] ?? 'h2'; // 'h2' or 'h2-h3'
        // Normalize deprecated/unknown depth values
        if ($outlineDepth === 'h2-h3-h4') { $outlineDepth = 'h2-h3'; }
        if ($outlineDepth !== 'h2' && $outlineDepth !== 'h2-h3') { $outlineDepth = 'h2'; }

        $outlineStyle = $parameters['outline_style'] ?? 'descriptive';
        $preset = $parameters['preset'] ?? 'informative';
        $primaryKeywords = $parameters['primary_keywords'] ?? '';
        $secondaryKeywords = $parameters['secondary_keywords'] ?? '';

        // Style-specific guidance
        $styleInstructions = match ($outlineStyle) {
            'question' => "Style: Question-based headings. Make each H2 a clear, concise question a reader would ask. H3s (if any) should also be questions when appropriate.",
            'howto' => "Style: How-to guide. Structure H2 headings as steps using imperative verbs (e.g., 'Set up...', 'Install...', 'Configure...'). Use 1–3 H3 substeps only when they add clarity. Do not label Introduction/Conclusion as steps.",
            'listicle' => "Style: Listicle. Make each H2 a distinct list item with a strong noun phrase. Do not prepend numbers in the heading text; numbering will be handled by the UI. H3s (if any) should add concise supporting points for the item.",
            default => "Style: Descriptive headings. Use concise, compelling statements that accurately describe the section.",
        };

        // Depth-specific guidance
        $depthInstructions = $outlineDepth === 'h2'
            ? "Depth: H2-only. Do not include any subsections."
            : "Depth: H2 + H3. Use H3 subsections sparingly (1–3) only where they add clarity. Never use H4.";

        $systemPrompt = "You are an expert content strategist and outline creator for blog articles. Your task is to create a well-structured, comprehensive outline that is SEO-friendly and genuinely valuable to readers.

Quality rules:
- Structure: Clear, logical flow with an Introduction and a Conclusion.
- Headings: Compelling, SEO-aware, non-clickbait; keep them concise.
- {$depthInstructions}
- Descriptions: 1–3 sentences per section; crisp and purposeful.
- Key Points: 3–6 short, actionable bullets per section; no nested bullets; no long sentences.
- Subsections: Include only when depth is H2+H3 or when they add significant clarity; do not add to Introduction/Conclusion.
- Uniqueness: Each section should advance the article and avoid redundancy.
- Reader value: Ensure clear progression and hooks early in the article.
- SEO: Integrate provided keywords naturally in headings or descriptions where it makes sense (no stuffing).
- {$styleInstructions}

STRICT RESPONSE FORMAT (parser-friendly):
For each main section, use this exact structure and labels. Separate sections with a blank line.

SECTION #[number]: [Section Title]
Description: [1–3 sentences]
Key Points:
- [Bullet 1]
- [Bullet 2]
- [Bullet 3]

For deeper outlines (if requested):
SUBSECTION #[section number].[subsection number]: [Subsection Title]
Description: [1–2 sentences]
Key Points:
- [Bullet 1]
- [Bullet 2]

Do not:
- Write the full article.
- Add labels/sections not requested.
- Use placeholders.
- Exceed the requested depth.

Optional machine-readable block: After the human-readable outline, output a single JSON block inside <OUTLINE_JSON> ... </OUTLINE_JSON> with this exact shape:
<OUTLINE_JSON>
{
  \"sections\": [
    {
      \"heading\": \"...\",
      \"description\": \"...\",
      \"key_points\": [\"...\", \"...\"],
      \"subsections\": [
        {\"heading\": \"...\", \"description\": \"...\", \"key_points\": [\"...\"]}
      ]
    }
  ]
}
</OUTLINE_JSON>
Do not include code fences or commentary inside the tags.";

        $userPrompt = "Create a detailed, SEO-focused outline for the following article. Follow the strict response format exactly.

TITLE: {$title}
DESCRIPTION: {$description}
STYLE: {$outlineStyle}
CONTENT TYPE/PRESET: {$preset}
OUTLINE DEPTH: {$outlineDepth} (Use H2-only for 'h2'. Use H2+H3 for deeper outlines. Do not use H4 under any circumstances.)";

        if (!empty($primaryKeywords)) {
            $userPrompt .= "\nPRIMARY KEYWORDS: {$primaryKeywords}";
        }
        if (!empty($secondaryKeywords)) {
            $userPrompt .= "\nSECONDARY KEYWORDS: {$secondaryKeywords}";
        }

        $userPrompt .= "\n\nRequirements:\n- Include an Introduction and a Conclusion.\n- Use 6–10 main sections (including Intro/Conclusion) unless the topic naturally needs more or fewer.\n- Make section titles concise, attractive, and SEO-aware.\n- Keep Key Points short (not full sentences) and actionable.\n- If depth is H2+H3: Add 1–3 SUBSECTIONS to complex sections only. Do not add subsections to Intro/Conclusion.\n- Follow the exact response format and labels.";
        
        return ['system' => $systemPrompt, 'user' => $userPrompt];
    }

    /**
     * Gets the prompts for blog section generation.
     * @param string $sectionTitle
     * @param string $articleContext
     * @param array $parameters
     * @return array ['system' => string, 'user' => string]
     */
    public static function getBlogSectionPrompts(string $sectionTitle, string $articleContext, array $parameters = []): array
    {
        $tone = $parameters['tone'] ?? 'informative';
        $length = $parameters['length'] ?? 'medium';
        
        $lengthInstructions = match($length) {
            'short' => 'Write a concise section (about 150-200 words).',
            'long' => 'Write a comprehensive section (about 400-500 words).',
            default => 'Write a balanced section (about 250-350 words).'
        };
        $toneInstructions = "Use a {$tone} tone.";

        $systemPrompt = "You are an expert content writer specializing in creating engaging blog content. You excel at writing well-structured, informative sections that flow naturally within a larger article and maintain a consistent tone.";

        $userPrompt = <<<EOT
Write a blog post section with the title "{$sectionTitle}".

Context from the rest of the post (previous sections or overall outline):
{$articleContext}

{$lengthInstructions}
{$toneInstructions}

Include relevant examples, data points, or anecdotes where appropriate. Make the content engaging, informative, and valuable to readers.
The section should be written in HTML format, using <p> tags for paragraphs, and <strong> or <em> for emphasis if needed. Do not include the section title heading itself in the output.

Ensure the section:
1. Starts with a smooth transition from the previous content (if applicable from context).
2. Thoroughly covers the topic indicated by the section title.
3. Uses clear, concise language with good readability.
4. Ends with a transition to the next section (if applicable from context).
5. Maintains consistency with the overall post style and tone.
EOT;
        return ['system' => $systemPrompt, 'user' => $userPrompt];
    }

    /**
     * Gets the prompts for full blog post generation (non-streaming context).
     * This combines the full article system prompt with a user prompt for the post.
     * @param string $title
     * @param string $outline // This is expected to be a string representation of the outline
     * @param array $parameters
     * @return array ['system' => string, 'user' => string]
     */
    public static function getFullBlogPostPrompts(string $title, string $outline, array $parameters = []): array
    {
        $systemPrompt = self::getSystemPromptForFullArticle(); // Reuse the detailed system prompt

        $tone = $parameters['tone'] ?? 'informative';
        $length = $parameters['length'] ?? 'medium';
        
        $lengthInstructions = match($length) {
            'short' => 'Write a concise blog post (about 600-800 words).',
            'long' => 'Write a comprehensive blog post (about 1500-2000 words).',
            default => 'Write a balanced blog post (about 1000-1200 words).'
        };
        $toneInstructions = "Use a {$tone} tone.";

        $userPrompt = <<<EOT
Write a complete blog post with the following title and outline:

Title: {$title}

Outline:
{$outline}

{$lengthInstructions}
{$toneInstructions}

Include an engaging introduction that hooks the reader, well-structured body sections that follow the outline, and a conclusion that summarizes key points and includes a call to action.
Follow all instructions in the system prompt regarding HTML formatting and image tags.
EOT;
        return ['system' => $systemPrompt, 'user' => $userPrompt];
    }

    /**
     * Gets the prompts for SEO metadata generation.
     * @param string $title
     * @param string $content
     * @param array $parameters
     * @return array ['system' => string, 'user' => string]
     */
    public static function getSeoMetadataPrompts(string $title, string $content, array $parameters = []): array
    {
        $contentSummary = strlen($content) > 1000 
            ? substr($content, 0, 1000) . "...\n[Content continues]" 
            : $content;
        
        $systemPrompt = "You are an SEO expert specializing in optimizing content for search engines. You excel at creating metadata that improves search rankings and click-through rates. Always follow the response format rules strictly.";
        
        $userPrompt = <<<EOT
Generate SEO metadata for the following blog post:

Title: {$title}

Content Summary:
{$contentSummary}

REQUIREMENTS:
- Meta title must be ≤ 60 characters.
- Meta description must be ≤ 160 characters.
- Provide 5–7 focus keywords/phrases.
- Provide a URL-friendly slug.

OUTPUT FORMAT (STRICT):
Return ONLY JSON (no markdown, no commentary), with this exact shape:
{
  "meta_title": "...",
  "meta_description": "...",
  "focus_keywords": ["...", "..."],
  "slug": "..."
}

Do not include any other fields. Do not wrap in code fences. Do not include explanations.
EOT;
        return ['system' => $systemPrompt, 'user' => $userPrompt];
    }

    /**
     * Gets the prompts for image caption generation.
     * @param string $imageDescription
     * @param string $context
     * @param array $parameters
     * @return array ['system' => string, 'user' => string]
     */
    public static function getImageCaptionPrompts(string $imageDescription, string $context, array $parameters = []): array
    {
        $style = $parameters['style'] ?? 'descriptive';
        $length = $parameters['length'] ?? 'medium';
        
        $lengthInstructions = match($length) {
            'short' => 'Write a brief caption (1 sentence).',
            'long' => 'Write a detailed caption (3-4 sentences).',
            default => 'Write a standard caption (1-2 sentences).'
        };
        $styleInstructions = match($style) {
            'humorous' => 'Use a light, humorous tone.',
            'technical' => 'Use precise, technical language.',
            'engaging' => 'Make the caption engaging and attention-grabbing.',
            default => 'Be clear and descriptive.'
        };

        $systemPrompt = "You are a professional caption writer specializing in creating engaging image captions. You excel at crafting captions that enhance the viewer's understanding and appreciation of images.";
        
        $userPrompt = <<<EOT
Write a caption for an image with the following description:

Image: {$imageDescription}

Context where the image appears:
{$context}

{$lengthInstructions}
{$styleInstructions}

The caption should be relevant to both the image and the context.
EOT;
        return ['system' => $systemPrompt, 'user' => $userPrompt];
    }

    /**
     * Gets the prompts for image alt text generation.
     * @param string $imageDescription
     * @param array $parameters
     * @return array ['system' => string, 'user' => string]
     */
    public static function getAltTextPrompts(string $imageDescription, array $parameters = []): array
    {
        $systemPrompt = "You are an accessibility expert specializing in creating effective alt text for images. You excel at writing concise, descriptive alt text that helps visually impaired users understand images.";
        
        $userPrompt = <<<EOT
Create concise, descriptive alt text for an image with the following description:

Image: {$imageDescription}

Guidelines for the alt text:
1. Be concise (preferably under 125 characters)
2. Be descriptive and specific
3. Convey the image's purpose and content
4. Don't start with "Image of" or "Picture of"
5. Focus on key details that matter for understanding the content

The alt text should help visually impaired users understand what the image shows and its relevance.
EOT;
        return ['system' => $systemPrompt, 'user' => $userPrompt];
    }

    /**
     * Gets the prompts for improving content.
     * @param string $content
     * @param string $instructions
     * @param array $parameters
     * @return array ['system' => string, 'user' => string]
     */
    public static function getImproveContentPrompts(string $content, string $instructions, array $parameters = []): array
    {
        $systemPrompt = "You are an expert content editor with a keen eye for improving writing. You excel at enhancing content while maintaining its original voice and message.";
        
        $userPrompt = <<<EOT
Improve the following content according to these instructions:

Instructions: {$instructions}

Original Content:
{$content}

Please maintain the original meaning and key points while making the requested improvements.
EOT;
        return ['system' => $systemPrompt, 'user' => $userPrompt];
    }

    /**
     * Gets the prompts for generating a response to a query.
     * @param string $query
     * @param string $context
     * @param array $parameters
     * @return array ['system' => string, 'user' => string]
     */
    public static function getGenerateResponsePrompts(string $query, string $context, array $parameters = []): array
    {
        $tone = $parameters['tone'] ?? 'helpful';
        
        $systemPrompt = "You are a knowledgeable assistant specializing in providing accurate, helpful responses. You excel at synthesizing information and presenting it in a clear, concise manner.";
        
        $userPrompt = <<<EOT
Answer the following user query based on the provided context:

User Query: {$query}

Context Information:
{$context}

Please provide a {$tone}, accurate response based on the context information. If the context doesn't contain enough information to fully answer the query, acknowledge this limitation in your response.
Your response should be:
1. Directly relevant to the user's query
2. Based on the provided context information
3. Clear and easy to understand
4. Formatted for readability with paragraphs and bullet points as needed
5. Comprehensive but concise
EOT;
        return ['system' => $systemPrompt, 'user' => $userPrompt];
    }
}