<?php

namespace Brenzley\Services\ApiProviders;

/**
 * Abstract base class for content generation
 */
abstract class AbstractContentGenerator implements ContentGenerationInterface
{
    /**
     * The API provider to use for content generation
     */
    protected ApiProviderInterface $apiProvider;
    
    /**
     * Constructor
     *
     * @param ApiProviderInterface $apiProvider The API provider to use
     */
    public function __construct(ApiProviderInterface $apiProvider)
    {
        $this->apiProvider = $apiProvider;
    }
    
    /**
     * Generate a blog post idea
     *
     * @param string $fullPrompt The complete prompt (system + user) for the AI
     * @param array $options Additional options for the API provider. These are typically provider-specific like 'temperature', 'top_p', etc.
     * @return array Success status, message, and generated idea
     */
    public function generateBlogIdea(string $fullPrompt, array $options = []): array
    {
        // Options array ($options) is passed directly from ApiService, which includes creativity/temperature.
        $result = $this->apiProvider->generateCompletion($fullPrompt, $options);
        
        if (!$result['success']) {
            return $result;
        }
        
        // The 'text' or 'completion' key depends on the specific provider's response structure.
        // AbstractApiProvider's makeRequest and individual provider's generateCompletion should normalize this.
        // For now, we check common keys.
        $responseText = $result['text'] ?? ($result['completion'] ?? ($result['choices'][0]['message']['content'] ?? ''));
        
        return [
            'success' => true,
            'idea' => $this->formatBlogIdea($responseText),
            'usage' => $result['usage'] ?? null // Pass along usage if available
        ];
    }
    
    /**
     * Generate a blog post outline
     *
     * @param string $fullPrompt The complete prompt (system + user) for the AI
     * @param array $options Additional options for the API provider
     * @return array Success status, message, and generated outline
     */
    public function generateBlogOutline(string $fullPrompt, array $options = []): array
    {
        $result = $this->apiProvider->generateCompletion($fullPrompt, $options);
        
        if (!$result['success']) {
            return $result;
        }
        $responseText = $result['text'] ?? ($result['completion'] ?? ($result['choices'][0]['message']['content'] ?? ''));
        
        return [
            'success' => true,
            'outline' => $this->formatBlogOutline($responseText),
            'usage' => $result['usage'] ?? null
        ];
    }
    
    /**
     * Generate a blog post section
     *
     * @param string $fullPrompt The complete prompt (system + user) for the AI
     * @param array $options Additional options for the API provider
     * @return array Success status, message, and generated section content
     */
    public function generateBlogSection(string $fullPrompt, array $options = []): array
    {
        $result = $this->apiProvider->generateCompletion($fullPrompt, $options);
        
        if (!$result['success']) {
            return $result;
        }
        $responseText = $result['text'] ?? ($result['completion'] ?? ($result['choices'][0]['message']['content'] ?? ''));
        
        return [
            'success' => true,
            'section_content' => $this->formatBlogSection($responseText),
            'usage' => $result['usage'] ?? null
        ];
    }
    
    /**
     * Generate a complete blog post
     *
     * @param string $fullPrompt The complete prompt (system + user) for the AI
     * @param array $options Additional options for the API provider
     * @return array Success status, message, and generated blog post
     */
    public function generateBlogPost(string $fullPrompt, array $options = []): array
    {
        $result = $this->apiProvider->generateCompletion($fullPrompt, $options);
        
        if (!$result['success']) {
            return $result;
        }
        $responseText = $result['text'] ?? ($result['completion'] ?? ($result['choices'][0]['message']['content'] ?? ''));
        
        return [
            'success' => true,
            'blog_post' => $this->formatBlogPost($responseText),
            'usage' => $result['usage'] ?? null
        ];
    }
    
    /**
     * Generate SEO metadata for a blog post
     *
     * @param string $fullPrompt The complete prompt (system + user) for the AI
     * @param array $options Additional options for the API provider
     * @return array Success status, message, and generated SEO metadata
     */
    public function generateSeoMetadata(string $fullPrompt, array $options = []): array
    {
        $result = $this->apiProvider->generateCompletion($fullPrompt, $options);
        
        if (!$result['success']) {
            return $result;
        }
        $responseText = $result['text'] ?? ($result['completion'] ?? ($result['choices'][0]['message']['content'] ?? ''));
        
        return [
            'success' => true,
            'seo_metadata' => $this->parseSeoMetadata($responseText),
            'usage' => $result['usage'] ?? null
        ];
    }
    
    /**
     * Generate an image caption
     *
     * @param string $fullPrompt The complete prompt (system + user) for the AI
     * @param array $options Additional options for the API provider
     * @return array Success status, message, and generated caption
     */
    public function generateImageCaption(string $fullPrompt, array $options = []): array
    {
        $result = $this->apiProvider->generateCompletion($fullPrompt, $options);
        
        if (!$result['success']) {
            return $result;
        }
        $responseText = $result['text'] ?? ($result['completion'] ?? ($result['choices'][0]['message']['content'] ?? ''));
        
        return [
            'success' => true,
            'caption' => $this->cleanOutput($responseText),
            'usage' => $result['usage'] ?? null
        ];
    }
    
    /**
     * Generate alt text for an image
     *
     * @param string $fullPrompt The complete prompt (system + user) for the AI
     * @param array $options Additional options for the API provider
     * @return array Success status, message, and generated alt text
     */
    public function generateAltText(string $fullPrompt, array $options = []): array
    {
        $result = $this->apiProvider->generateCompletion($fullPrompt, $options);
        
        if (!$result['success']) {
            return $result;
        }
        $responseText = $result['text'] ?? ($result['completion'] ?? ($result['choices'][0]['message']['content'] ?? ''));
        
        return [
            'success' => true,
            'alt_text' => $this->cleanOutput($responseText),
            'usage' => $result['usage'] ?? null
        ];
    }
    
    /**
     * Improve or rewrite existing content
     *
     * @param string $fullPrompt The complete prompt (system + user) for the AI
     * @param array $options Additional options for the API provider
     * @return array Success status, message, and improved content
     */
    public function improveContent(string $fullPrompt, array $options = []): array
    {
        $result = $this->apiProvider->generateCompletion($fullPrompt, $options);
        
        if (!$result['success']) {
            return $result;
        }
        $responseText = $result['text'] ?? ($result['completion'] ?? ($result['choices'][0]['message']['content'] ?? ''));
        
        return [
            'success' => true,
            'improved_content' => $this->cleanOutput($responseText),
            'usage' => $result['usage'] ?? null
        ];
    }
    
    /**
     * Generate a response to a user query
     *
     * @param string $fullPrompt The complete prompt (system + user) for the AI
     * @param array $options Additional options for the API provider
     * @return array Success status, message, and generated response
     */
    public function generateResponse(string $fullPrompt, array $options = []): array
    {
        $result = $this->apiProvider->generateCompletion($fullPrompt, $options);
        
        if (!$result['success']) {
            return $result;
        }
        $responseText = $result['text'] ?? ($result['completion'] ?? ($result['choices'][0]['message']['content'] ?? ''));
        
        return [
            'success' => true,
            'response' => $this->cleanOutput($responseText),
            'usage' => $result['usage'] ?? null
        ];
    }
    
    /**
     * Format a blog idea from raw AI output
     *
     * @param string $text Raw AI output
     * @return array Structured blog idea
     */
    protected function formatBlogIdea(string $text): array
    {
        // 1) Prefer strict JSON inside <IDEAS_JSON> ... </IDEAS_JSON>
        if (preg_match('/<IDEAS_JSON>([\s\S]*?)<\/IDEAS_JSON>/i', $text, $m)) {
            $jsonBlock = $m[1] ?? '';
            $json = $this->extractJson($jsonBlock);
            if ($json) {
                $decoded = json_decode($json, true);
                if (is_array($decoded)) {
                    $ideasArr = $decoded['ideas'] ?? null;
                    if (is_array($ideasArr) && count($ideasArr) > 0) {
                        $first = $ideasArr[0];
                        return [
                            'title' => trim((string)($first['title'] ?? '')),
                            'description' => trim((string)($first['description'] ?? '')),
                            'target_audience' => trim((string)($first['target_audience'] ?? '')),
                            'key_points' => array_values(array_filter(array_map('trim', is_array($first['key_points'] ?? null) ? ($first['key_points'] ?? []) : explode('\n', (string)($first['key_points'] ?? '')))))
                        ];
                    }
                }
            }
        }

        // 2) Fallback to label-based parsing. Return the first idea block if multiple are present.
        // Try to isolate the first idea block by splitting on typical idea markers.
        $parts = preg_split('/(?:^|\n)\s*(?:IDEA|Idea)\s*(?:#|)?\s*\d+\s*[:.)-]?/m', $text, -1, PREG_SPLIT_NO_EMPTY);
        $candidate = $parts && count($parts) > 0 ? $parts[0] : $text;

        // Default structure
        $idea = [
            'title' => '',
            'description' => '',
            'target_audience' => '',
            'key_points' => []
        ];

        // Define a boundary to avoid bleeding into next idea
        $ideaBoundary = '(?:(?:^|\n)\s*(?:IDEA|Idea)\s*(?:#|)?\s*\d+\s*[:.)-]?|\z)';

        // Extract title
        if (preg_match('/Title\s*[:\-]\s*(.+?)\s*(?=Description\s*[:\-]|Target\s*Audience\s*[:\-]|Audience\s*[:\-]|Key\s*Points\s*[:\-]|' . $ideaBoundary . ')/is', $candidate, $matches)) {
            $idea['title'] = trim($matches[1]);
        } elseif (preg_match('/^\s*\"([^\"]{3,})\"/m', $candidate, $matches)) {
            $idea['title'] = trim($matches[1]);
        }

        // Extract description
        if (preg_match('/Description\s*[:\-]\s*(.+?)\s*(?=Target\s*Audience\s*[:\-]|Audience\s*[:\-]|Key\s*Points\s*[:\-]|' . $ideaBoundary . ')/is', $candidate, $matches)) {
            $idea['description'] = trim($matches[1]);
        }

        // Extract target audience (accept "Target Audience" or "Audience")
        if (preg_match('/(?:Target\s*Audience|Audience)\s*[:\-]\s*(.+?)\s*(?=Key\s*Points\s*[:\-]|' . $ideaBoundary . ')/is', $candidate, $matches)) {
            $idea['target_audience'] = trim($matches[1]);
        }

        // Extract key points, allowing bullets -, *, • or numbered lists
        if (preg_match('/Key\s*Points\s*[:\-]\s*([\s\S]+?)\s*(?:' . $ideaBoundary . ')/i', $candidate, $matches)) {
            $keyPointsText = $matches[1];
            $lines = preg_split('/\r?\n/', $keyPointsText);
            $points = [];
            foreach ($lines as $line) {
                if (preg_match('/^\s*(?:[-*•]|\d+\.)\s*(.+)$/', $line, $pm)) {
                    $points[] = trim($pm[1]);
                }
            }
            if (!empty($points)) {
                $idea['key_points'] = $points;
            }
        }

        return $idea;
    }
    
    /**
     * Format a blog outline from raw AI output
     *
     * @param string $text Raw AI output
     * @return array Structured blog outline
     */
    protected function formatBlogOutline(string $text): string
    {
        // For now, just clean up the text
        return $this->cleanOutput($text);
    }
    
    /**
     * Format a blog section from raw AI output
     *
     * @param string $text Raw AI output
     * @return string Formatted blog section
     */
    protected function formatBlogSection(string $text): string
    {
        return $this->cleanOutput($text);
    }
    
    /**
     * Format a blog post from raw AI output
     *
     * @param string $text Raw AI output
     * @return string Formatted blog post
     */
    protected function formatBlogPost(string $text): string
    {
        return $this->cleanOutput($text);
    }
    
    /**
     * Parse SEO metadata from raw AI output
     *
     * @param string $text Raw AI output
     * @return array Structured SEO metadata
     */
    protected function parseSeoMetadata(string $text): array
    {
        // 1) Try to parse JSON strictly (preferred)
        $json = $this->extractJson($text);
        if ($json) {
            $decoded = json_decode($json, true);
            if (is_array($decoded)) {
                return [
                    'meta_title' => trim((string)($decoded['meta_title'] ?? '')),
                    'meta_description' => trim((string)($decoded['meta_description'] ?? '')),
                    'focus_keywords' => array_values(array_filter(array_map('trim', is_array($decoded['focus_keywords'] ?? null) ? ($decoded['focus_keywords'] ?? []) : explode(',', (string)($decoded['focus_keywords'] ?? ''))))),
                    'slug' => trim((string)($decoded['slug'] ?? '')),
                ];
            }
        }

        // 2) Fallback to legacy line-based parsing
        $metadata = [
            'meta_title' => '',
            'meta_description' => '',
            'focus_keywords' => [],
            'slug' => ''
        ];
        
        // Extract meta title
        if (preg_match('/meta\s*title:?\s*(.+?)(?:\n|$)/i', $text, $matches)) {
            $metadata['meta_title'] = trim($matches[1]);
        }
        
        // Extract meta description
        if (preg_match('/meta\s*description:?\s*(.+?)(?:\n|$)/i', $text, $matches)) {
            $metadata['meta_description'] = trim($matches[1]);
        }
        
        // Extract focus keywords
        if (preg_match('/focus\s*keywords:?\s*(.+?)(?:\n|$)/i', $text, $matches)) {
            $keywordsText = trim($matches[1]);
            $metadata['focus_keywords'] = array_map('trim', explode(',', $keywordsText));
        }
        
        // Extract slug
        if (preg_match('/slug:?\s*(.+?)(?:\n|$)/i', $text, $matches)) {
            $metadata['slug'] = trim($matches[1]);
        }
        
        return $metadata;
    }

    /**
     * Extracts a JSON object from a text block, removing code fences and trailing characters.
     */
    protected function extractJson(string $text): ?string
    {
        // Remove code fences if present
        $clean = preg_replace('/```[a-zA-Z]*\n?|```/m', '', $text);
        // Find the first { and the last } to get a JSON object substring
        $start = strpos($clean, '{');
        $end = strrpos($clean, '}');
        if ($start === false || $end === false || $end <= $start) {
            return null;
        }
        $json = substr($clean, $start, $end - $start + 1);
        // Remove BOM or stray chars like trailing ',o'
        $json = trim($json);
        // Attempt quick fix for trailing commas
        $json = preg_replace('/,\s*}/', '}', $json);
        $json = preg_replace('/,\s*\]/', ']', $json);
        return $json;
    }
    
    /**
     * Clean up raw AI output
     *
     * @param string $text Raw AI output
     * @return string Cleaned output
     */
    protected function cleanOutput(string $text): string
    {
        // Remove any "AI:" or similar prefixes
        $text = preg_replace('/^(AI:|Assistant:|ChatGPT:)\s*/i', '', $text);
        
        // Trim whitespace
        $text = trim($text);
        
        return $text;
    }
}
