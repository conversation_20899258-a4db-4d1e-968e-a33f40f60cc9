<?php

declare(strict_types=1);

namespace Brenzley\Controllers\Api;

/**
 * Sends a message to the client using Server-Sent Events (SSE) format.
 *
 * @param string $type The type of the event (e.g., 'chunk', 'error', 'log').
 * @param mixed $data The data to send. If it's an array or object, it will be JSON encoded.
 * @return void
 */
function send_sse_message(string $type, $data): void
{
    if (is_array($data) || is_object($data)) {
        $message = json_encode(['type' => $type, 'content' => $data]);
    } else {
        // For simple strings (like logs), wrap them in the standard format.
        $message = json_encode(['type' => $type, 'content' => $data]);
    }

    echo "data: " . $message . "\n\n";

    if (ob_get_level() > 0) {
        ob_flush();
    }
    flush();
}

use Brenzley\Core\Session;
use Brenzley\Models\AiBlogIdeaModel;
use Brenzley\Models\AiBlogOutlineModel;
use Brenzley\Models\AiBlogSectionModel;
use Brenzley\Models\AiBlogPostModel;
use Brenzley\Models\AiSeoMetadataModel;
use Brenzley\Models\AiImageMetadataModel;
use Brenzley\Models\AiUsageLogModel;
use Brenzley\Models\AiContentSessionModel;
use Brenzley\Models\ApiSettingsModel;
use Brenzley\Services\ApiProviders\ContentGeneratorFactory;

/**
 * Controller for AI content generation API endpoints
 */
class AiContentController
{
    protected AiBlogIdeaModel $ideaModel;
    protected AiBlogOutlineModel $outlineModel;
    protected AiBlogSectionModel $sectionModel;
    protected AiBlogPostModel $postModel;
    protected AiSeoMetadataModel $seoModel;
    protected AiImageMetadataModel $imageModel;
    protected AiUsageLogModel $usageModel;
    protected AiContentSessionModel $sessionModel;
    protected ApiSettingsModel $apiSettingsModel;

    public function __construct()
    {
        $this->ideaModel = new AiBlogIdeaModel();
        $this->outlineModel = new AiBlogOutlineModel();
        $this->sectionModel = new AiBlogSectionModel();
        $this->postModel = new AiBlogPostModel();
        $this->seoModel = new AiSeoMetadataModel();
        $this->imageModel = new AiImageMetadataModel();
        $this->usageModel = new AiUsageLogModel();
        $this->sessionModel = new AiContentSessionModel();
        $this->apiSettingsModel = new ApiSettingsModel();
    }

    /**
     * Create a new content generation session
     */
    public function createSession(): void
    {
        // Check authentication
        if (!Session::isLoggedIn()) {
            $this->jsonResponse(['success' => false, 'message' => 'Authentication required'], 401);
            return;
        }
        
        // Validate request method
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->jsonResponse(['success' => false, 'message' => 'Invalid request method'], 405);
            return;
        }
        
        // Get request data
        $data = json_decode(file_get_contents('php://input'), true);
        
        if (!isset($data['session_type']) || empty($data['session_type'])) {
            $this->jsonResponse(['success' => false, 'message' => 'Session type is required'], 400);
            return;
        }
        
        // Validate session type
        $validTypes = ['idea', 'outline', 'section', 'post', 'seo'];
        if (!in_array($data['session_type'], $validTypes)) {
            $this->jsonResponse(['success' => false, 'message' => 'Invalid session type'], 400);
            return;
        }
        
        // Create session
        $sessionData = [
            'user_id' => Session::getUserId(),
            'session_type' => $data['session_type'],
            'status' => 'in_progress'
        ];
        
        // Add related content IDs if provided
        if (isset($data['idea_id'])) {
            $sessionData['idea_id'] = (int)$data['idea_id'];
        }
        
        if (isset($data['outline_id'])) {
            $sessionData['outline_id'] = (int)$data['outline_id'];
        }
        
        if (isset($data['post_id'])) {
            $sessionData['post_id'] = (int)$data['post_id'];
        }
        
        $sessionId = $this->sessionModel->create($sessionData);
        
        if (!$sessionId) {
            $this->jsonResponse(['success' => false, 'message' => 'Failed to create session'], 500);
            return;
        }
        
        $this->jsonResponse([
            'success' => true,
            'session_id' => $sessionId,
            'message' => 'Session created successfully'
        ]);
    }

    /**
     * Get a content generation session
     */
    public function getSession(): void
    {
        // Check authentication
        if (!Session::isLoggedIn()) {
            $this->jsonResponse(['success' => false, 'message' => 'Authentication required'], 401);
            return;
        }
        
        // Validate request method
        if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
            $this->jsonResponse(['success' => false, 'message' => 'Invalid request method'], 405);
            return;
        }
        
        // Get session ID from query parameters
        $sessionId = isset($_GET['id']) ? (int)$_GET['id'] : null;
        
        if (!$sessionId) {
            $this->jsonResponse(['success' => false, 'message' => 'Session ID is required'], 400);
            return;
        }
        
        // Get session
        $session = $this->sessionModel->getCompleteSession($sessionId);
        
        if (!$session) {
            $this->jsonResponse(['success' => false, 'message' => 'Session not found'], 404);
            return;
        }
        
        // Check if the session belongs to the current user
        if ($session['user_id'] !== Session::getUserId()) {
            $this->jsonResponse(['success' => false, 'message' => 'Unauthorized access to session'], 403);
            return;
        }
        
        $this->jsonResponse([
            'success' => true,
            'session' => $session
        ]);
    }

    /**
     * Update a content generation session
     */
    public function updateSession(): void
    {
        // Check authentication
        if (!Session::isLoggedIn()) {
            $this->jsonResponse(['success' => false, 'message' => 'Authentication required'], 401);
            return;
        }
        
        // Validate request method
        if ($_SERVER['REQUEST_METHOD'] !== 'PUT' && $_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->jsonResponse(['success' => false, 'message' => 'Invalid request method'], 405);
            return;
        }
        
        // Get request data
        $data = json_decode(file_get_contents('php://input'), true);
        
        if (!isset($data['id']) || empty($data['id'])) {
            $this->jsonResponse(['success' => false, 'message' => 'Session ID is required'], 400);
            return;
        }
        
        $sessionId = (int)$data['id'];
        
        // Get session
        $session = $this->sessionModel->findById($sessionId);
        
        if (!$session) {
            $this->jsonResponse(['success' => false, 'message' => 'Session not found'], 404);
            return;
        }
        
        // Check if the session belongs to the current user
        if ($session['user_id'] !== Session::getUserId()) {
            $this->jsonResponse(['success' => false, 'message' => 'Unauthorized access to session'], 403);
            return;
        }
        
        // Update session
        $updateData = [];
        
        if (isset($data['status'])) {
            $updateData['status'] = $data['status'];
        }
        
        if (isset($data['idea_id'])) {
            $updateData['idea_id'] = (int)$data['idea_id'];
        }
        
        if (isset($data['outline_id'])) {
            $updateData['outline_id'] = (int)$data['outline_id'];
        }
        
        if (isset($data['post_id'])) {
            $updateData['post_id'] = (int)$data['post_id'];
        }
        
        if (empty($updateData)) {
            $this->jsonResponse(['success' => false, 'message' => 'No data to update'], 400);
            return;
        }
        
        $success = $this->sessionModel->update($sessionId, $updateData);
        
        if (!$success) {
            $this->jsonResponse(['success' => false, 'message' => 'Failed to update session'], 500);
            return;
        }
        
        $this->jsonResponse([
            'success' => true,
            'message' => 'Session updated successfully'
        ]);
    }

    /**
     * Get active content generation sessions for the current user
     */
    public function getActiveSessions(): void
    {
        // Check authentication
        if (!Session::isLoggedIn()) {
            $this->jsonResponse(['success' => false, 'message' => 'Authentication required'], 401);
            return;
        }
        
        // Validate request method
        if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
            $this->jsonResponse(['success' => false, 'message' => 'Invalid request method'], 405);
            return;
        }
        
        // Get active sessions
        $sessions = $this->sessionModel->findActiveByUserId(Session::getUserId());
        
        $this->jsonResponse([
            'success' => true,
            'sessions' => $sessions
        ]);
    }

    /**
     * Log AI usage
     *
     * @param array $usageData Usage data to log
     * @return bool Success status
     */
    protected function logUsage(array $usageData): bool
    {
        // Check if usage tracking is enabled
        $activeProvider = $this->apiSettingsModel->getActiveProvider();
        
        if (!$activeProvider || $activeProvider['provider'] === 'none') {
            return false;
        }
        
        $settings = json_decode($activeProvider['settings'] ?? '{}', true) ?? [];
        
        if (!isset($settings['enable_usage_tracking']) || !$settings['enable_usage_tracking']) {
            return false;
        }
        
        // Add user ID if not provided
        if (!isset($usageData['user_id'])) {
            $usageData['user_id'] = Session::getUserId();
        }
        
        // Add provider and model if not provided
        if (!isset($usageData['provider'])) {
            $usageData['provider'] = $activeProvider['provider'];
        }
        
        if (!isset($usageData['model']) && isset($activeProvider['model'])) {
            $usageData['model'] = $activeProvider['model'];
        }
        
        // Log usage
        return $this->usageModel->log($usageData) !== false;
    }

    /**
     * Handle JSON response
     *
     * @param array $data Response data
     * @param int $statusCode HTTP status code
     */
    protected function jsonResponse(array $data, int $statusCode = 200): void
    {
        header('Content-Type: application/json');
        http_response_code($statusCode);
        echo json_encode($data);
        exit;
    }
}
