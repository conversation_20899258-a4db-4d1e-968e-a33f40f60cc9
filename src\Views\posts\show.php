<?php
// Enhanced post display view with proper styling and image handling
$isLoggedIn = Brenzley\Core\Session::isLoggedIn();
?>

<div class="post-container">
    <article class="single-post" data-post-id="<?= $post['id'] ?>">
        
        <!-- Post Header -->
        <header class="post-header">
            <div class="post-header-content">
                <h1 class="post-title"><?= htmlspecialchars($post['title']) ?></h1>
                
                <div class="post-meta">
                    <div class="post-meta-primary">
                        <span class="post-author">
                            <svg class="icon" width="16" height="16" viewBox="0 0 24 24" fill="none">
                                <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2" stroke="currentColor" stroke-width="2"/>
                                <circle cx="12" cy="7" r="4" stroke="currentColor" stroke-width="2"/>
                            </svg>
                            <?= htmlspecialchars($post['author_username'] ?? 'Unknown') ?>
                        </span>
                        
                        <span class="post-date">
                            <svg class="icon" width="16" height="16" viewBox="0 0 24 24" fill="none">
                                <rect x="3" y="4" width="18" height="18" rx="2" ry="2" stroke="currentColor" stroke-width="2"/>
                                <line x1="16" y1="2" x2="16" y2="6" stroke="currentColor" stroke-width="2"/>
                                <line x1="8" y1="2" x2="8" y2="6" stroke="currentColor" stroke-width="2"/>
                                <line x1="3" y1="10" x2="21" y2="10" stroke="currentColor" stroke-width="2"/>
                            </svg>
                            <time datetime="<?= date('Y-m-d', strtotime($post['published_at'])) ?>">
                                <?= date('F j, Y', strtotime($post['published_at'])) ?>
                            </time>
                        </span>
                        
                        <?php if (!empty($post['category_name']) && !empty($post['category_slug'])): ?>
                            <span class="post-category">
                                <svg class="icon" width="16" height="16" viewBox="0 0 24 24" fill="none">
                                    <path d="M20.59 13.41l-7.17 7.17a2 2 0 0 1-2.83 0L2 12V2h10l8.59 8.59a2 2 0 0 1 0 2.82z" stroke="currentColor" stroke-width="2"/>
                                    <line x1="7" y1="7" x2="7.01" y2="7" stroke="currentColor" stroke-width="2"/>
                                </svg>
                                <a href="/categories/<?= htmlspecialchars($post['category_slug']) ?>">
                                    <?= htmlspecialchars($post['category_name']) ?>
                                </a>
                            </span>
                        <?php endif; ?>
                    </div>
                    
                    <?php if ($isLoggedIn): ?>
                        <div class="post-actions">
                            <button id="save-article-btn" class="btn-action" data-action="<?= $isSaved ? 'unsave' : 'save' ?>">
                                <svg class="icon" width="18" height="18" viewBox="0 0 24 24" fill="none">
                                    <path d="M19 21L12 16L5 21V5C5 4.46957 5.21071 3.96086 5.58579 3.58579C5.96086 3.21071 6.46957 3 7 3H17C17.5304 3 18.0391 3.21071 18.4142 3.58579C18.7893 3.96086 19 4.46957 19 5V21Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                                <span class="btn-text"><?= $isSaved ? 'Saved' : 'Save' ?></span>
                            </button>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </header>

        <!-- Post Content -->
        <div class="post-content">
            <div class="post-content-inner"><?php /* Render AI-editor compatible content with graceful fallbacks */ ?>
                <?php 
                // Process and display the content with proper image handling
                // Ensure all content stays within the container
                // Prefer preprocessed AI-compatible content when available
                $content = $post['post_content'] ?? $post['content'];
                
                // Fix any content that might break out of the container
                $content = preg_replace('/(<\/div>\s*<\/div>)\s*(<div class="ai-image|<h[1-6]>|<p>)/', '$1', $content);
                $content = preg_replace('/(<\/div>)(<p><\/p>\s*)(<div class="ai-image|<h[1-6]>|<p>)/', '$1$3', $content);

                // Server-side pass: use the full publication processor to normalize layouts and keep all content
                try {
                    $pc = new \Brenzley\Controllers\Admin\PostController();
                    $rc = new \ReflectionClass($pc);
                    if ($rc->hasMethod('processContentForPublication')) {
                        $m = $rc->getMethod('processContentForPublication');
                        $m->setAccessible(true);
                        $content = $m->invoke($pc, $content);
                    }
                } catch (\Throwable $e) {
                    // Non-fatal; leave content as-is
                }

               echo $content; 
                ?>
            </div>
        </div>

        <!-- Enhanced Image Error Handler Script -->
        <script>
        document.addEventListener('DOMContentLoaded', () => {
            const postContent = document.querySelector('.post-content-inner');
            if (!postContent) return;
            
            // Handle broken images by replacing with styled placeholders
            const images = postContent.querySelectorAll('img');
            images.forEach(img => {
                img.addEventListener('error', function() {
                    // Only replace if not already a placeholder
                    if (this.closest('.post-image-placeholder') || this.closest('.ai-image-placeholder')) return;
                    
                    // Get alt text or create default
                    const altText = this.alt || 'Image description not available';
                    
                    // Detect layout from parent classes - check both AI and post classes
                    let layout = 'center';
                    const aiPlaceholder = this.closest('.ai-image-placeholder');
                    const figure = this.closest('figure');
                    
                    if (aiPlaceholder) {
                        // Extract layout from AI placeholder classes
                        const classes = aiPlaceholder.className;
                        if (classes.includes('ai-image-layout-left')) layout = 'left';
                        else if (classes.includes('ai-image-layout-right')) layout = 'right';
                        else if (classes.includes('ai-image-layout-wide')) layout = 'wide';
                        else if (classes.includes('ai-image-layout-full')) layout = 'full';
                        else if (classes.includes('ai-image-layout-square')) layout = 'square';
                        else if (classes.includes('ai-image-layout-text_overlay')) layout = 'overlay';
                        else if (classes.includes('ai-image-layout-standard')) layout = 'center';
                    } else if (figure) {
                        const classes = figure.className;
                        if (classes.includes('post-image-left')) layout = 'left';
                        else if (classes.includes('post-image-right')) layout = 'right';
                        else if (classes.includes('post-image-wide')) layout = 'wide';
                        else if (classes.includes('post-image-full')) layout = 'full';
                        else if (classes.includes('post-image-square')) layout = 'square';
                    }
                    
                    // Create placeholder HTML using AI editor structure for consistency
                    const placeholderHtml = `
                        <div class="ai-image-placeholder ai-image-layout-${layout}">
                            <div class="ai-image-placeholder-inner">
                                <div class="placeholder-content">
                                    <span class="placeholder-icon">🖼️</span>
                                    <span class="placeholder-text">Image not found</span>
                                    <span class="placeholder-alt">${altText}</span>
                                </div>
                            </div>
                        </div>
                    `;
                    
                    // Replace the container or just the img
                    if (aiPlaceholder) {
                        aiPlaceholder.outerHTML = placeholderHtml;
                    } else if (figure) {
                        figure.outerHTML = placeholderHtml;
                    } else {
                        this.outerHTML = placeholderHtml;
                    }
                }, { once: true });
            });
            
            // Handle AI image placeholders that don't have images loaded
            const aiPlaceholders = postContent.querySelectorAll('.ai-image-placeholder');
            aiPlaceholders.forEach(placeholder => {
                const img = placeholder.querySelector('img.generated-image');
                if (img && !img.complete) {
                    img.addEventListener('error', function() {
                        // Replace with placeholder content
                        const altText = this.alt || 'Image description not available';
                        const placeholderContent = `
                            <div class="placeholder-content">
                                <span class="placeholder-icon">🖼️</span>
                                <span class="placeholder-text">Image not found</span>
                                <span class="placeholder-alt">${altText}</span>
                            </div>
                        `;
                        
                        const inner = placeholder.querySelector('.ai-image-placeholder-inner');
                        if (inner) {
                            inner.innerHTML = placeholderContent;
                        }
                    }, { once: true });
                }
            });

            // 1) Clean up nested placeholders mistakenly embedded inside placeholder text
            postContent.querySelectorAll('.ai-image-placeholder .placeholder-text').forEach(pt => {
                if (pt.querySelector('.ai-image-placeholder')) {
                    pt.textContent = 'Image not found';
                }
            });
            // Remove any nested ai-image-placeholder inside another placeholder
            postContent.querySelectorAll('.ai-image-placeholder .ai-image-placeholder').forEach(inner => {
                const parent = inner.closest('.ai-image-placeholder');
                if (parent && inner !== parent) {
                    inner.remove();
                }
            });

            // 2) Normalize individual placeholders and floated layout wrappers
            function normalizePlaceholders(container) {
                const placeholders = Array.from(container.querySelectorAll('.ai-image-placeholder'));
                placeholders.forEach(ph => {
                    // Ensure inner structure exists
                    if (!ph.querySelector('.ai-image-placeholder-inner')) {
                        const inner = document.createElement('div');
                        inner.className = 'ai-image-placeholder-inner';
                        const content = document.createElement('div');
                        content.className = 'placeholder-content';
                        content.innerHTML = '<span class="placeholder-icon">🖼️</span><span class="placeholder-text">Image not found</span>';
                        inner.appendChild(content);
                        inner.append(...Array.from(ph.childNodes));
                        ph.appendChild(inner);
                    }

                    // Clean nested placeholders inside text nodes
                    ph.querySelectorAll('.placeholder-text .ai-image-placeholder').forEach(n => n.remove());

                    // Wrap left/right floated images with adjacent text if not wrapped
                    const isLeft = ph.classList.contains('ai-image-layout-left');
                    const isRight = ph.classList.contains('ai-image-layout-right');
                    if ((isLeft || isRight) && !ph.closest('.image-text-wrapper')) {
                        const wrapper = document.createElement('div');
                        wrapper.className = 'image-text-wrapper';
                        wrapper.dataset.layout = isLeft ? 'left' : 'right';

                        const adjacent = document.createElement('div');
                        adjacent.className = 'adjacent-text-content';

                        // Collect following siblings as adjacent text until a breaker element
                        const breakers = ['H1','H2','H3','H4','H5','H6','HR'];
                        let cursor = ph.nextSibling;
                        const toMove = [];
                        while (cursor) {
                            if (cursor.nodeType === 1) {
                                const el = cursor;
                                if (el.classList.contains('ai-image-placeholder') || el.classList.contains('ai-image-gallery') || el.classList.contains('image-text-wrapper') || breakers.includes(el.tagName)) {
                                    break;
                                }
                                toMove.push(el);
                                cursor = el.nextSibling;
                            } else if (cursor.nodeType === 3 && /\S/.test(cursor.nodeValue) === false) {
                                // whitespace: move along
                                const next = cursor.nextSibling;
                                toMove.push(cursor);
                                cursor = next;
                            } else {
                                // text node with content: wrap in <p> for safety
                                const p = document.createElement('p');
                                p.textContent = cursor.nodeValue;
                                toMove.push(p);
                                const next = cursor.nextSibling;
                                cursor.parentNode && cursor.parentNode.removeChild(cursor);
                                cursor = next;
                            }
                        }

                        // Insert wrapper and move nodes
                        ph.parentNode.insertBefore(wrapper, ph);
                        wrapper.appendChild(ph);
                        toMove.forEach(n => adjacent.appendChild(n));
                        // Only append adjacent section if it has content
                        if (adjacent.childNodes.length) wrapper.appendChild(adjacent);
                    }
                });
            }

            // 3) Auto-group consecutive placeholders into gallery containers
            function groupGalleries(container) {
                const children = Array.from(container.childNodes);
                let run = [];
                function finalizeRun() {
                    if (run.length >= 2) {
                        // Do not re-wrap if already in a gallery
                        if (run[0].closest('.ai-image-gallery')) { run = []; return; }
                        const gallery = document.createElement('div');
                        gallery.className = 'ai-image-layout-gallery-' + (run.length === 2 ? '2' : '3') + '-item ai-image-gallery';
                        const grid = document.createElement('div');
                        grid.className = 'ai-gallery-grid';
                        gallery.appendChild(grid);
                        container.insertBefore(gallery, run[0]);
                        run.forEach(node => {
                            grid.appendChild(node);
                        });
                    }
                    run = [];
                }
                for (const node of children) {
                    if (node.nodeType === 1 && node.classList.contains('ai-image-placeholder')) {
                        run.push(node);
                    } else if (node.nodeType === 3 && /\S/.test(node.nodeValue) === false) {
                        // whitespace text node: ignore and continue the run
                        continue;
                    } else {
                        finalizeRun();
                    }
                }
                finalizeRun();
            }

            // Apply grouping at multiple depths where content may live
            groupGalleries(postContent);
            postContent.querySelectorAll('#ai-stream-static, .ai-content-area, .post-content-inner').forEach(el => groupGalleries(el));
        });
        </script>

        <!-- Post Footer -->
        <footer class="post-footer">
            <?php if (!empty($post['ai_generation_metadata'])): ?>
                <?php $metadata = json_decode($post['ai_generation_metadata'], true); ?>
                <?php if (!empty($metadata['focus_keywords'])): ?>
                    <div class="post-keywords">
                        <span class="keywords-label">Keywords:</span>
                        <div class="keywords-list">
                            <?php 
                            $keywords = is_array($metadata['focus_keywords']) 
                                ? $metadata['focus_keywords'] 
                                : explode(',', $metadata['focus_keywords']);
                            foreach ($keywords as $keyword): 
                                $keyword = trim($keyword);
                                if (!empty($keyword)):
                            ?>
                                <span class="keyword-tag"><?= htmlspecialchars($keyword) ?></span>
                            <?php 
                                endif;
                            endforeach; 
                            ?>
                        </div>
                    </div>
                <?php endif; ?>
            <?php endif; ?>
        </footer>

    </article>

    <!-- Comments Section -->
    <section class="comments-section">
        <div class="comments-header">
            <h2 class="comments-title">
                <svg class="icon" width="20" height="20" viewBox="0 0 24 24" fill="none">
                    <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z" stroke="currentColor" stroke-width="2"/>
                </svg>
                Comments (<?= count($comments) ?>)
            </h2>
        </div>

        <div class="comments-content">
            <div class="comment-list">
                <?php if (empty($comments)): ?>
                    <div class="no-comments">
                        <svg class="icon" width="48" height="48" viewBox="0 0 24 24" fill="none">
                            <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z" stroke="currentColor" stroke-width="1.5"/>
                        </svg>
                        <p>Be the first to share your thoughts!</p>
                    </div>
                <?php else: ?>
                    <?php foreach ($comments as $comment): ?>
                        <div class="comment" id="comment-<?= $comment['id'] ?>">
                            <div class="comment-header">
                                <div class="comment-author">
                                    <svg class="icon" width="16" height="16" viewBox="0 0 24 24" fill="none">
                                        <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2" stroke="currentColor" stroke-width="2"/>
                                        <circle cx="12" cy="7" r="4" stroke="currentColor" stroke-width="2"/>
                                    </svg>
                                    <strong><?= htmlspecialchars($comment['author_username'] ?? $comment['author_name'] ?? 'Anonymous') ?></strong>
                                </div>
                                <time class="comment-date" datetime="<?= date('Y-m-d\TH:i:s', strtotime($comment['created_at'])) ?>">
                                    <?= date('M j, Y \a\t g:i A', strtotime($comment['created_at'])) ?>
                                </time>
                            </div>
                            <div class="comment-content">
                                <?= nl2br(htmlspecialchars($comment['content'])) ?>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>

            <!-- Comment Form -->
            <div class="comment-form-section">
                <h3 class="comment-form-title">Leave a Comment</h3>
                
                <?php Brenzley\Core\Session::displayFlashMessage(); ?> 

                <form class="comment-form" action="/posts/<?= $post['id'] ?>/comments" method="POST">
                    <input type="hidden" name="csrf_token" value="<?= $csrf_token ?? '' ?>">
                    
                    <?php if (!Brenzley\Core\Session::isLoggedIn()): ?>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="author_name">Name <span class="required">*</span></label>
                                <input type="text" id="author_name" name="author_name" value="<?= htmlspecialchars($old_comment_input['author_name'] ?? '') ?>" required>
                                <?php if (isset($comment_errors['author_name'])): ?>
                                    <div class="error"><?= htmlspecialchars($comment_errors['author_name']) ?></div>
                                <?php endif; ?>
                            </div>
                            <div class="form-group">
                                <label for="author_email">Email <span class="required">*</span></label>
                                <input type="email" id="author_email" name="author_email" value="<?= htmlspecialchars($old_comment_input['author_email'] ?? '') ?>" required>
                                <small>Your email will not be published</small>
                                <?php if (isset($comment_errors['author_email'])): ?>
                                    <div class="error"><?= htmlspecialchars($comment_errors['author_email']) ?></div>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endif; ?>

                    <div class="form-group">
                        <label for="content">Your Comment <span class="required">*</span></label>
                        <textarea id="content" name="content" rows="5" placeholder="Share your thoughts..." required><?= htmlspecialchars($old_comment_input['content'] ?? '') ?></textarea>
                        <?php if (isset($comment_errors['content'])): ?>
                            <div class="error"><?= htmlspecialchars($comment_errors['content']) ?></div>
                        <?php endif; ?>
                    </div>

                    <div class="form-actions">
                        <button type="submit" class="btn-submit">
                            <svg class="icon" width="16" height="16" viewBox="0 0 24 24" fill="none">
                                <line x1="22" y1="2" x2="11" y2="13" stroke="currentColor" stroke-width="2"/>
                                <polygon points="22,2 15,22 11,13 2,9 22,2" stroke="currentColor" stroke-width="2"/>
                            </svg>
                            Submit Comment
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </section>
</div>

<?php if ($isLoggedIn): ?>
<script>
document.addEventListener('DOMContentLoaded', () => {
    const postId = document.querySelector('.single-post').dataset.postId;
    const saveButton = document.getElementById('save-article-btn');
    const saveButtonText = saveButton?.querySelector('.btn-text');
    let isReadSent = false;

    // Mark as Read functionality
    const observerOptions = {
        root: null,
        rootMargin: '0px',
        threshold: 0.3
    };

    const markReadCallback = (entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting && !isReadSent) {
                isReadSent = true;
                fetch(`/posts/${postId}/read`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' }
                }).catch(error => console.error('Error marking as read:', error));
                observer.unobserve(entry.target);
            }
        });
    };

    const observer = new IntersectionObserver(markReadCallback, observerOptions);
    const contentElement = document.querySelector('.post-content');
    if (contentElement) {
        observer.observe(contentElement);
    }

    // Save/Unsave functionality
    if (saveButton) {
        saveButton.addEventListener('click', () => {
            const action = saveButton.dataset.action;
            const url = `/posts/${postId}/${action}`;

            saveButton.disabled = true;
            if (saveButtonText) saveButtonText.textContent = 'Processing...';

            fetch(url, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    if (action === 'save') {
                        saveButton.dataset.action = 'unsave';
                        if (saveButtonText) saveButtonText.textContent = 'Saved';
                        saveButton.classList.add('saved');
                    } else {
                        saveButton.dataset.action = 'save';
                        if (saveButtonText) saveButtonText.textContent = 'Save';
                        saveButton.classList.remove('saved');
                    }
                } else {
                    alert('Error: ' + (data.message || 'Could not update save status.'));
                }
            })
            .catch(error => {
                console.error('Error saving/unsaving article:', error);
                alert('An error occurred. Please try again.');
            })
            .finally(() => {
                saveButton.disabled = false;
                if (saveButtonText && !saveButton.classList.contains('saved')) {
                    saveButtonText.textContent = action === 'save' ? 'Save' : 'Saved';
                }
            });
        });
    }
});
</script>
<?php endif; ?>