import { getPresetFromGoal, getGoalDescription, removeMarkdown, showNotification } from '../utils/EditorUtils.js';

/**
 * Handles the "Idea" step functionalities.
 */
export class IdeaHandler {
    constructor(uiManager, stateManager, chatFeature) {
        this.uiManager = uiManager;
        this.stateManager = stateManager;
        this.chatFeature = chatFeature;
        this.elements = uiManager.elements;
    }

    init() {
        if (this.elements.generateIdeasBtn) {
            this.elements.generateIdeasBtn.addEventListener('click', () => this.generateIdeas());
        }
    }

    generateIdeas() {
        const topic = (this.elements.ideaTopicInput?.value || '').trim();
        const audience = (this.elements.ideaAudienceInput?.value || '').trim();
        const goal = (this.elements.ideaGoalSelect?.value || 'educate').trim();

        // Basic validation with inline feedback
        let valid = true;
        if (!topic) {
            valid = false;
            showNotification('Topic Area is required to generate ideas.', 'warning');
            this.elements.ideaTopicInput?.classList.add('ai-input-error');
            this.elements.ideaTopicInput?.focus();
        } else {
            this.elements.ideaTopicInput?.classList.remove('ai-input-error');
        }
        if (!goal) {
            valid = false;
            showNotification('Please select a Content Goal.', 'warning');
            this.elements.ideaGoalSelect?.classList.add('ai-input-error');
        } else {
            this.elements.ideaGoalSelect?.classList.remove('ai-input-error');
        }
        if (!valid) return;

        // Prepare UI
        if (this.elements.generateIdeasBtn) {
            this.elements.generateIdeasBtn.disabled = true;
            this.elements.generateIdeasBtn.textContent = 'Generating...';
        }
        if (this.elements.ideasContainer) {
            this.elements.ideasContainer.innerHTML = '<div class="ai-status-message">Connecting...</div>';
        }
        // Use streaming generation for better UX
        this.streamIdeas(topic, audience, goal);
        this.chatFeature.addAssistantMessage(`Generating ideas for "${topic}"${audience ? ' targeting ' + audience : ''}.`);
    }

    streamIdeas(topic, audience, goal) {
        this.elements.ideasContainer.innerHTML = ''; // Clear for streaming
        const ideasGrid = document.createElement('div');
        ideasGrid.className = 'ai-ideas-container'; // Re-use existing class for styling
        this.elements.ideasContainer.appendChild(ideasGrid);

        const statusMessage = document.createElement('div');
        statusMessage.className = 'ai-status-message';
        statusMessage.textContent = 'Generating ideas...';
        this.elements.ideasContainer.appendChild(statusMessage);

        let collectedContent = '';
        let currentIdeasData = []; // To store parsed idea objects
        let currentIdeaTextSnippets = []; // To store raw text for each idea card during streaming

        const eventSource = new EventSource(`/api/admin/ai/blog-ideas/stream?topic=${encodeURIComponent(topic)}&audience=${encodeURIComponent(audience)}&goal=${encodeURIComponent(goal)}`);

        const onRetry = () => {
            this.streamIdeas(topic, audience, goal);
        };

        eventSource.addEventListener('message', (event) => {
            if (!event || !event.data) return;
            try {
                const data = JSON.parse(event.data);
                switch (data.type) {
                    case 'chunk':
                        collectedContent += data.content;
                        // Attempt to parse and render incrementally
                        this.updateStreamedIdeaCards(collectedContent, ideasGrid, statusMessage, currentIdeaTextSnippets);
                        break;
                    case 'end':
                        // Prefer server-provided ideas if present; otherwise, fallback to locally parsed ideas
                        let finalIdeas = [];
                        if (data && data.content && Array.isArray(data.content.ideas) && data.content.ideas.length) {
                            finalIdeas = (data.content.ideas || []).map((idea, idx) => ({
                                client_id: idea.client_id || `idea-${idx + 1}`,
                                db_id: idea.db_id || null,
                                title: idea.title || '',
                                description: idea.description || '',
                                preset: getPresetFromGoal(this.elements.ideaGoalSelect.value),
                                target_audience: idea.target_audience || (this.elements.ideaAudienceInput?.value || ''),
                                key_points: idea.key_points || []
                            }));
                        } else {
                            // Fallback: parse ideas from collected stream content
                            console.log('No server ideas found, parsing from collected content:', collectedContent);
                            const parsedFallback = this.parseStreamingContentFallback(collectedContent) || [];
                            console.log('Parsed fallback ideas:', parsedFallback);
                            finalIdeas = parsedFallback.map((idea, idx) => ({
                                client_id: `idea-${idx + 1}`,
                                db_id: null,
                                title: idea.title || `Idea ${idx + 1}`,
                                description: idea.description || '',
                                preset: getPresetFromGoal(this.elements.ideaGoalSelect.value),
                                target_audience: idea.target_audience || (this.elements.ideaAudienceInput?.value || ''),
                                key_points: Array.isArray(idea.key_points) ? idea.key_points : []
                            }));
                            console.log('Final ideas after mapping:', finalIdeas);
                        }
                        this.finalizeStreamedIdeas(finalIdeas, ideasGrid, statusMessage);
                        eventSource.close();
                        this.resetGenerateButton();
                        break;
                    case 'error':
                        const errMsg = data.message || 'Stream error';
                        showNotification(errMsg, 'error');
                        this.elements.ideasContainer.innerHTML = `<div class="ai-error">Error: ${errMsg}</div>`;
                        const retryBtn2 = document.createElement('button');
                        retryBtn2.className = 'ai-btn ai-btn-secondary';
                        retryBtn2.textContent = 'Try Again';
                        retryBtn2.addEventListener('click', onRetry);
                        this.elements.ideasContainer.appendChild(retryBtn2);
                        this.chatFeature.addAssistantMessage(`Stream error: ${errMsg}`);
                        eventSource.close();
                        this.resetGenerateButton();
                        break;
                }
            } catch (error) {
                console.error('Error handling streaming message:', error, event.data);
            }
        });

        eventSource.addEventListener('error', (error) => {
            console.error('Streaming connection error:', error);
            showNotification('Connection error during idea generation.', 'error');
            if (this.elements.ideasContainer) {
                this.elements.ideasContainer.innerHTML = '<div class="ai-error">Error: Failed to connect for streaming ideas.</div>';
                const retryBtn = document.createElement('button');
                retryBtn.className = 'ai-btn ai-btn-secondary';
                retryBtn.textContent = 'Try Again';
                retryBtn.addEventListener('click', onRetry);
                this.elements.ideasContainer.appendChild(retryBtn);
            }
            this.chatFeature.addAssistantMessage('Sorry, a connection error occurred while streaming ideas.');
            try { eventSource.close(); } catch {}
            this.resetGenerateButton();
        });
    }

    updateStreamedIdeaCards(fullContent, ideasGrid, statusMessage, currentIdeaTextSnippets) {
        // Do not display any machine-readable JSON block or anything after it in the idea cards
        let displayContent = fullContent;
        const jsonStart = displayContent.search(/<IDEAS_JSON/i); // match even if '>' hasn't arrived yet
        if (jsonStart !== -1) {
            displayContent = displayContent.substring(0, jsonStart);
        }

        const ideaMarkers = displayContent.match(/(?:^|\n)\s*IDEA\s*(?:#\s*)?\d+\s*[:.)-]?/gi);
        if (!ideaMarkers) { // Still receiving the first idea or preamble
            currentIdeaTextSnippets[0] = displayContent;
            this.createOrUpdateIdeaCardDOM(0, currentIdeaTextSnippets[0], ideasGrid);
            statusMessage.textContent = `Generating idea #1...`;
            return;
        }

        const ideaContents = displayContent.split(/(?:^|\n)\s*IDEA\s*(?:#\s*)?\d+\s*[:.)-]?/i).slice(1); // Content parts after each marker

        ideaMarkers.forEach((marker, index) => {
            const ideaNumberMatch = marker.match(/IDEA\s*(?:#\s*)?(\d+)/i);
            const ideaIndex = ideaNumberMatch ? parseInt(ideaNumberMatch[1]) - 1 : index;

            let currentText = marker + (ideaContents[index] || '');
            currentIdeaTextSnippets[ideaIndex] = currentText;
            this.createOrUpdateIdeaCardDOM(ideaIndex, currentText, ideasGrid);
            statusMessage.textContent = `Generating idea #${ideaIndex + 1}...`;
        });
    }

    finalizeStreamedIdeas(ideas, ideasGrid, statusMessage) {
        statusMessage.remove();

        if (!ideas || ideas.length === 0) {
            console.error("finalizeStreamedIdeas: No ideas were generated or parsed.");
            this.elements.ideasContainer.innerHTML = '<div class="ai-error">No ideas were generated. Please try again.</div>';
            const retryBtn = document.createElement('button');
            retryBtn.className = 'ai-btn ai-btn-secondary';
            retryBtn.textContent = 'Try Again';
            retryBtn.addEventListener('click', () => this.generateIdeas());
            this.elements.ideasContainer.appendChild(retryBtn);
            this.chatFeature.addAssistantMessage('I was unable to generate ideas. Please try again.');
            return;
        }

        // Remove generating class from all cards first to ensure clean state
        const allCards = ideasGrid.querySelectorAll('.ai-idea-card');
        allCards.forEach(card => {
            card.classList.remove('generating');
        });

        // The ideas are already rendered. Now we just need to update them with the final
        // data (especially the db_id) and attach the final event listeners.
        ideas.forEach((idea, index) => {
            // Try multiple selectors to find the card
            let card = ideasGrid.querySelector(`.ai-idea-card[data-id="${idea.client_id}"]`);
            if (!card) {
                // Fallback: try finding by index position
                const cardContainers = ideasGrid.querySelectorAll('.ai-idea-card-container');
                if (cardContainers[index]) {
                    card = cardContainers[index].querySelector('.ai-idea-card');
                }
            }
            
            if (card) {
                // Ensure generating class is removed
                card.classList.remove('generating');
                
                // Update the data-id to match the client_id for consistency
                card.dataset.id = idea.client_id;
                
                // The idea object from the server now has the definitive data.
                // We re-create the object for the event listener to ensure it's clean.
                const finalIdeaData = {
                    client_id: idea.client_id,
                    db_id: idea.db_id, // The correct, unique DB ID for this idea
                    title: idea.title,
                    description: idea.description,
                    preset: getPresetFromGoal(this.elements.ideaGoalSelect.value),
                    target_audience: idea.target_audience,
                    key_points: idea.key_points
                };

                // Replace the card with a fresh one to easily manage event listeners
                const newCard = card.cloneNode(true);
                card.parentNode.replaceChild(newCard, card);
                newCard.addEventListener('click', () => this.toggleIdeaSelection(finalIdeaData, newCard));
            }
        });

        this.chatFeature.addAssistantMessage(`I've generated ${ideas.length} article idea(s). Click one to proceed.`);
        this.addContinueButtonIfNeeded();
    }


    createOrUpdateIdeaCardDOM(index, content, ideasGrid) {
        let cardContainer = ideasGrid.querySelector(`.ai-idea-card-container[data-index="${index}"]`);
        const parsedIdea = this.parseIdeaContent(content);

        if (!cardContainer) {
            cardContainer = document.createElement('div');
            cardContainer.className = 'ai-idea-card-container';
            cardContainer.dataset.index = index;

            const card = document.createElement('div');
            card.className = 'ai-idea-card generating'; // Add 'generating' class
            card.dataset.id = `idea-${index + 1}`; // Temporary ID

            card.innerHTML = `
                <div class="ai-idea-card-number">${index + 1}</div>
                <div class="ai-idea-title">${parsedIdea.title || `Generating idea #${index + 1}...`}</div>
                <div class="ai-idea-description">${parsedIdea.description || ''}</div>
                <div class="ai-idea-target-audience">${parsedIdea.target_audience ? `<strong>Target Audience:</strong> ${parsedIdea.target_audience}` : ''}</div>
                <div class="ai-idea-key-points"></div>
                <div class="ai-idea-meta">
                    <div class="ai-idea-preset">${getPresetFromGoal(this.elements.ideaGoalSelect.value)}</div>
                </div>
            `;
            cardContainer.appendChild(card);
            ideasGrid.appendChild(cardContainer);
        }

        const card = cardContainer.querySelector('.ai-idea-card');
        // Update content
        card.querySelector('.ai-idea-title').textContent = parsedIdea.title || `Generating idea #${index + 1}...`;
        card.querySelector('.ai-idea-description').textContent = parsedIdea.description || '';
        const audienceEl = card.querySelector('.ai-idea-target-audience');
        if (parsedIdea.target_audience) {
            audienceEl.innerHTML = `<strong>Target Audience:</strong> ${parsedIdea.target_audience}`;
        } else {
            audienceEl.innerHTML = '';
        }

        const keyPointsEl = card.querySelector('.ai-idea-key-points');
        if (parsedIdea.key_points && parsedIdea.key_points.length > 0) {
            const sanitized = parsedIdea.key_points
                .map(p => (p || '').replace(/^\s*(?:[-*•]|\d+\.)\s*/, '').trim())
                .filter(Boolean);
            keyPointsEl.innerHTML = sanitized.length ? ('<ul>' + sanitized.map(p => `<li>${p}</li>`).join('') + '</ul>') : '';
        } else {
            keyPointsEl.innerHTML = '';
        }
        return card;
    }


    fetchIdeas(topic, audience, goal) {
        fetch('/api/admin/ai/blog-ideas/generate', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json', 'X-Requested-With': 'XMLHttpRequest' },
            body: JSON.stringify({ topic, parameters: { audience, goal } })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success && data.idea) {
                const ideas = this.formatApiIdeas(data.idea);
                this.renderIdeas(ideas);
                this.chatFeature.addAssistantMessage(`I've generated ${ideas.length} article ideas. Select one to continue.`);
            } else {
                showNotification(data.message || 'Failed to generate ideas', 'error');
                this.elements.ideasContainer.innerHTML = `<div class="ai-error">Error: ${data.message || 'API error'}</div>`;
                this.chatFeature.addAssistantMessage(`Error generating ideas: ${data.message || 'Unknown API error'}`);
            }
        })
        .catch(error => {
            console.error('Error fetching ideas:', error);
            showNotification('Failed to connect to server for idea generation.', 'error');
            this.elements.ideasContainer.innerHTML = '<div class="ai-error">Error: Server connection failed.</div>';
            this.chatFeature.addAssistantMessage('Sorry, I couldn\'t connect to the server to generate ideas.');
        })
        .finally(() => this.resetGenerateButton());
    }

    resetGenerateButton() {
        this.elements.generateIdeasBtn.disabled = false;
        this.elements.generateIdeasBtn.textContent = 'Generate Ideas';
    }

    formatApiIdeas(apiIdeaData) {
        // This function should transform the API response (which might be a single complex object or an array)
        // into a consistent array of idea objects.
        const ideas = [];
        const goal = this.elements.ideaGoalSelect.value;

        if (apiIdeaData.ideas && Array.isArray(apiIdeaData.ideas)) {
            apiIdeaData.ideas.forEach((idea, index) => {
                ideas.push({
                    id: idea.id || `idea-${index + 1}`,
                    title: idea.title || 'Untitled Idea',
                    description: idea.description || 'No description.',
                    preset: getPresetFromGoal(goal),
                    target_audience: idea.target_audience || apiIdeaData.target_audience || '',
                    key_points: idea.key_points || []
                });
            });
        } else if (apiIdeaData.title) { // Single idea object
            ideas.push({
                id: 'idea-1',
                title: apiIdeaData.title,
                description: apiIdeaData.description || 'No description.',
                preset: getPresetFromGoal(goal),
                target_audience: apiIdeaData.target_audience || '',
                key_points: apiIdeaData.key_points || []
            });
        }
        // Add more parsing logic if the API structure is different
        return ideas;
    }

    parseIdeaContent(content) {
        if (!content) return { title: '', description: '', target_audience: '', key_points: [] };
        // Remove any trailing JSON block (closed) and any partial JSON tail (open without close), then normalize newlines
        let contentSansJson = content.replace(/<IDEAS_JSON>[\s\S]*?<\/?IDEAS_JSON>/gi, '');
        if (/<IDEAS_JSON>/i.test(contentSansJson)) {
            // If a start tag still exists without a proper close, strip everything from the start tag to end
            contentSansJson = contentSansJson.replace(/<IDEAS_JSON>[\s\S]*$/i, '');
        }
        const cleanContent = removeMarkdown(contentSansJson).replace(/\r\n?|\r/g, '\n');
        const idea = { title: '', description: '', target_audience: '', key_points: [] };

        // Title extraction: simple labeled Title:
        const titleMatch = cleanContent.match(/Title\s*:(.*?)(?=Description|Target Audience|Key Points|IDEA\s*#|$)/is);
        if (titleMatch && titleMatch[1]) {
            idea.title = titleMatch[1].trim();
        }

        // Description until next label
        const descMatch = cleanContent.match(/Description\s*:(.*?)(?=Target Audience|Key Points|IDEA\s*#|$)/is);
        if (descMatch && descMatch[1]) {
            idea.description = descMatch[1].trim();
        }

        // Audience
        const audienceMatch = cleanContent.match(/(?:Target\s*Audience|Audience)\s*:(.*?)(?=(?:Key\s*Points|Key\s*Takeaways|Main\s*Points)|IDEA\s*#|$)/is);
        if (audienceMatch && audienceMatch[1]) {
            idea.target_audience = audienceMatch[1].trim();
        }

        // Key points
        const keyPointsMatch = cleanContent.match(/(?:Key\s*Points|Key\s*Takeaways|Main\s*Points)\s*:\s*([\s\S]*?)(?=\n\s*(?:IDEA\s*(?:#\s*)?\d+|Title\s*:)|$)/is);
        if (keyPointsMatch && keyPointsMatch[1]) {
            idea.key_points = keyPointsMatch[1]
                .trim()
                .split(/\n\s*[-*•]\s*|\n\s*\d+\.\s*|;\s*/)
                .map(p => p.trim())
                .filter(p => p);
        }
        return idea;
    }
    
    parseStreamingContentFallback(content) {
        // A simpler parser for when IDEA # markers are not consistently present or clear
        const ideas = [];
        
        if (!content || typeof content !== 'string') {
            console.log('No content to parse or content is not a string:', content);
            return ideas;
        }
        
        // Clean the content first
        const cleanContent = content.trim();
        console.log('Parsing content of length:', cleanContent.length);
        
        // Try multiple parsing strategies
        
        // Strategy 1: Look for IDEA markers first
        const ideaMarkers = cleanContent.match(/(?:^|\n)\s*IDEA\s*(?:#\s*)?\d+/gi);
        if (ideaMarkers && ideaMarkers.length > 0) {
            console.log('Found IDEA markers:', ideaMarkers.length);
            const ideaSections = cleanContent.split(/(?:^|\n)\s*IDEA\s*(?:#\s*)?\d+[:.)-]?/i).slice(1);
            ideaSections.forEach((section, idx) => {
                const parsed = this.parseIdeaContent('IDEA ' + (idx + 1) + ': ' + section);
                if (parsed.title) ideas.push(parsed);
            });
        }
        
        // Strategy 2: Try splitting by "Title:" markers
        if (ideas.length === 0) {
            console.log('No IDEA markers found, trying Title: markers');
            const blocks = cleanContent.split(/\n\s*Title\s*:/i);
            blocks.forEach((block, index) => {
                if (index === 0 && !cleanContent.toLowerCase().startsWith("title:")) {
                    if (block.trim().length > 10) {
                        const parsedPreamble = this.parseIdeaContent(block);
                        if(parsedPreamble.title) ideas.push(parsedPreamble);
                    }
                    return;
                }
                const ideaContent = (index > 0 ? "Title:" : "") + block;
                const parsed = this.parseIdeaContent(ideaContent);
                if (parsed.title) ideas.push(parsed);
            });
        }
        
        // Strategy 3: If still no ideas, try to extract from the whole content
        if (ideas.length === 0 && cleanContent.length > 10) {
            console.log('No structured markers found, parsing whole content as single idea');
            const parsed = this.parseIdeaContent(cleanContent);
            if (parsed.title || cleanContent.length > 20) {
                // Even if no title found, create a basic idea if we have substantial content
                ideas.push({
                    title: parsed.title || 'Generated Article Idea',
                    description: parsed.description || cleanContent.substring(0, 200).trim() + (cleanContent.length > 200 ? '...' : ''),
                    target_audience: parsed.target_audience || '',
                    key_points: parsed.key_points || []
                });
            }
        }
        
        console.log('parseStreamingContentFallback result:', ideas.length, 'ideas found');
        return ideas.filter(idea => idea.title);
    }


    renderIdeas(ideas) {
        this.elements.ideasContainer.innerHTML = ''; // Clear previous
        const ideasGrid = document.createElement('div');
        ideasGrid.className = 'ai-ideas-container'; // Use the same class for styling consistency
        ideas.forEach(idea => this.renderIdeaCard(idea, ideasGrid));
        this.elements.ideasContainer.appendChild(ideasGrid);

        if (ideas.length > 0) {
            this.addContinueButtonIfNeeded();
        }
    }

    renderIdeaCard(idea, container) {
        const cardContainer = document.createElement('div');
        cardContainer.className = 'ai-idea-card-container'; // Wrapper for potential future styling/grid behavior

        const card = document.createElement('div');
        card.className = 'ai-idea-card';
        card.dataset.clientId = idea.client_id; // Use client_id for DOM selection
        card.dataset.id = idea.db_id; // Store the database ID

        let audienceHTML = '';
        if (idea.target_audience) {
            audienceHTML = `<div class="ai-idea-target-audience"><strong>Target Audience:</strong> ${idea.target_audience}</div>`;
        }

        let keyPointsHTML = '';
        if (idea.key_points && idea.key_points.length > 0) {
            keyPointsHTML = '<div class="ai-idea-key-points"><strong>Key Points:</strong><ul>' +
                            idea.key_points.map(point => `<li>${point}</li>`).join('') +
                            '</ul></div>';
        }
        
        card.innerHTML = `
            <div class="ai-idea-card-number">${idea.client_id ? idea.client_id.split('-')[1] : ''}</div>
            <div class="ai-idea-title">${idea.title}</div>
            <div class="ai-idea-description">${idea.description}</div>
            ${audienceHTML}
            ${keyPointsHTML}
            <div class="ai-idea-meta">
                <div class="ai-idea-preset">${idea.preset}</div>
            </div>
        `;
        // The event listener is now added in finalizeStreamedIdeas
        cardContainer.appendChild(card);
        container.appendChild(cardContainer);
    }

    selectIdea(ideaDataFromCard) { // preserve original select behavior for external calls
        this.applyIdeaSelection(ideaDataFromCard, true);
    }

    toggleIdeaSelection(ideaDataFromCard, cardEl) {
        const current = this.stateManager.getStateValue('selectedIdea');
        const isSame = current && (current.client_id === ideaDataFromCard.client_id || current.id === ideaDataFromCard.id);
        if (isSame) {
            // Deselect
            this.stateManager.setSelectedIdea(null);
            this.elements.ideasContainer.querySelectorAll('.ai-idea-card').forEach(card => card.classList.remove('selected'));
            this.chatFeature.addAssistantMessage('Idea deselected. You can choose another idea to continue.');
            return;
        }
        // Select
        this.applyIdeaSelection(ideaDataFromCard, false, cardEl);
    }

    applyIdeaSelection(ideaDataFromCard, fromExternal = false, cardEl = null) {
        // Persist the selected idea and preset in state
        this.stateManager.setSelectedIdea(ideaDataFromCard);
        if (ideaDataFromCard.preset) {
            this.stateManager.updateState('ideaPreset', ideaDataFromCard.preset);
        }

        // Update UI highlight on selected card with smooth transition
        this.elements.ideasContainer.querySelectorAll('.ai-idea-card').forEach(card => {
            card.classList.remove('selected');
            // Remove any existing generating class to ensure clean state
            card.classList.remove('generating');
        });
        
        // Prefer using provided element if available, else query by id
        const selectorId = ideaDataFromCard.client_id || ideaDataFromCard.id || '';
        const selectedCard = cardEl || (selectorId ? this.elements.ideasContainer.querySelector(`.ai-idea-card[data-id="${selectorId}"]`) : null);
        
        if (selectedCard) {
            // Add selected class with a slight delay for smooth animation
            setTimeout(() => {
                selectedCard.classList.add('selected');
            }, 50);
        }

        if (!fromExternal) {
            this.chatFeature.addAssistantMessage(`You've selected "${ideaDataFromCard.title}". This will use the "${ideaDataFromCard.preset || this.stateManager.getStateValue('ideaPreset') || 'default'}" preset. Click "Continue" to create an outline.`);
        }
        this.addContinueButtonIfNeeded();
    }

    addContinueButtonIfNeeded() {
        if (!this.elements.ideasContainer.querySelector('#ai-continue-to-outline')) {
            const continueBtn = document.createElement('button');
            continueBtn.id = 'ai-continue-to-outline';
            continueBtn.className = 'ai-btn ai-btn-primary ai-btn-full';
            continueBtn.style.marginTop = '1rem';
            continueBtn.textContent = 'Continue to Outline';
            continueBtn.addEventListener('click', () => {
                this.stateManager.setCurrentStep('outline');
                // The main orchestrator should handle the goToStep logic
                // This might involve emitting an event or calling a method on the orchestrator
                document.dispatchEvent(new CustomEvent('aieditor:stepChange', { detail: { step: 'outline' } }));
            });
            this.elements.ideasContainer.appendChild(continueBtn);
        }
    }
}