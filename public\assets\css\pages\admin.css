/**
 * <PERSON><PERSON><PERSON><PERSON> Admin Interface Styles
 * Following the design system in docs/BRENZLEY_DESIGN_SYSTEM.md and admin layout in docs/pages/05_admin.md
 */

/* Admin-specific CSS variables */
:root {
  /* Light mode */
  --admin-bg: #F8FAFC;
  --admin-surface: #FFFFFF;
  --admin-border: #E2E8F0;
  --admin-sidebar-bg: #FFFFFF;
  --admin-header-bg: #FFFFFF;
  --admin-text-primary: #1E293B;
  --admin-text-secondary: #64748B;
  --admin-text-muted: #94A3B8;
  --admin-accent: #3B82F6;
  --admin-accent-hover: #2563EB;
  --admin-active-item-bg: rgba(59, 130, 246, 0.1);
  --admin-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --admin-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --admin-success: #10B981;
  --admin-warning: #F59E0B;
  --admin-danger: #EF4444;
  --admin-info: #3B82F6;
  --admin-nav-hover: #F1F5F9;
}

.dark {
  /* Dark mode */
  --admin-bg: #121826;
  --admin-surface: #1E293B;
  --admin-border: #334155;
  --admin-sidebar-bg: #1E293B;
  --admin-header-bg: #1E293B;
  --admin-text-primary: #F1F5F9;
  --admin-text-secondary: #94A3B8;
  --admin-text-muted: #64748B;
  --admin-accent: #60A5FA;
  --admin-accent-hover: #3B82F6;
  --admin-active-item-bg: rgba(96, 165, 250, 0.2);
  --admin-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.3), 0 1px 2px 0 rgba(0, 0, 0, 0.2);
  --admin-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
  --admin-success: #34D399;
  --admin-warning: #FBBF24;
  --admin-danger: #F87171;
  --admin-info: #60A5FA;
  --admin-nav-hover: #293548;
}

/* General Admin Styles */
.admin-body {
  background-color: var(--admin-bg);
  color: var(--admin-text-primary);
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  font-family: 'Inter', sans-serif;
}

/* Admin Header */
.admin-header {
  background-color: var(--admin-header-bg);
  border-bottom: 1px solid var(--admin-border);
  padding: 0.75rem 1.5rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: sticky;
  top: 0;
  z-index: 100;
  box-shadow: var(--admin-shadow);
  height: 4rem;
}

.admin-header-left,
.admin-header-right {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.admin-header-center {
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 1;
}

.admin-logo {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.admin-brand {
  font-family: 'DM Serif Display', serif;
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--admin-text-primary);
}

.admin-label {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--admin-accent);
  padding: 0.25rem 0.5rem;
  background-color: var(--admin-active-item-bg);
  border-radius: 0.25rem;
}

.admin-page-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--admin-text-primary);
  margin: 0;
}

.sidebar-toggle {
  background: none;
  border: none;
  color: var(--admin-text-secondary);
  cursor: pointer;
  padding: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 0.25rem;
  transition: background-color 0.2s ease;
}

.sidebar-toggle:hover {
  background-color: var(--admin-nav-hover);
  color: var(--admin-text-primary);
}

.admin-action-button {
  background: none;
  border: none;
  color: var(--admin-text-secondary);
  cursor: pointer;
  padding: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 0.25rem;
  transition: background-color 0.2s ease, color 0.2s ease;
  position: relative;
}

.admin-action-button:hover {
  background-color: var(--admin-nav-hover);
  color: var(--admin-text-primary);
}

/* Theme Toggle Button */
.theme-toggle {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  padding: 0;
  background: transparent;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  color: var(--admin-text-primary);
  transition: background-color 0.2s ease;
}

.theme-toggle:hover {
  background-color: var(--admin-nav-hover);
}

.theme-toggle-icon {
  position: absolute;
  width: 20px;
  height: 20px;
  transition: 
    transform 0.5s cubic-bezier(0.23, 1, 0.32, 1),
    opacity 0.5s cubic-bezier(0.23, 1, 0.32, 1);
}

.theme-toggle-icon--sun {
  opacity: 0;
  transform: rotate(90deg) scale(0);
}

.theme-toggle-icon--moon {
  opacity: 1;
  transform: rotate(0) scale(1);
}

.dark .theme-toggle-icon--sun {
  opacity: 1;
  transform: rotate(0) scale(1);
}

.dark .theme-toggle-icon--moon {
  opacity: 0;
  transform: rotate(-90deg) scale(0);
}

.notifications-button {
  position: relative;
}

.notification-badge {
  position: absolute;
  top: 0.25rem;
  right: 0.25rem;
  background-color: var(--admin-danger);
  color: white;
  font-size: 0.625rem;
  width: 1rem;
  height: 1rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
}

/* User Dropdown */
.user-dropdown {
  position: relative;
}

.user-dropdown-button {
  background: none;
  border: none;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.375rem 0.75rem;
  border-radius: 0.25rem;
  cursor: pointer;
  color: var(--admin-text-primary);
  transition: background-color 0.2s ease;
}

.user-dropdown-button:hover {
  background-color: var(--admin-nav-hover);
}

.user-avatar {
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  overflow: hidden;
  border: 2px solid var(--admin-border);
}

.user-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.user-name {
  font-weight: 500;
  font-size: 0.875rem;
}

.user-dropdown-menu {
  position: absolute;
  top: calc(100% + 0.5rem);
  right: 0;
  background-color: var(--admin-surface);
  border: 1px solid var(--admin-border);
  border-radius: 0.375rem;
  box-shadow: var(--admin-shadow-md);
  width: 12rem;
  z-index: 10;
  display: none;
  overflow: hidden;
}

.user-dropdown-menu.active {
  display: block;
}

.user-dropdown-item {
  padding: 0.75rem 1rem;
  display: block;
  text-decoration: none;
  color: var(--admin-text-primary);
  font-size: 0.875rem;
  transition: background-color 0.2s ease;
}

.user-dropdown-item:hover {
  background-color: var(--admin-nav-hover);
  color: var(--admin-accent);
  text-decoration: none;
}

/* Admin Layout */
.admin-layout {
  display: flex;
  flex: 1;
}

/* Admin Sidebar */
.admin-sidebar {
  width: 260px;
  background-color: var(--admin-sidebar-bg);
  border-right: 1px solid var(--admin-border);
  display: flex;
  flex-direction: column;
  transition: width 0.3s ease;
  overflow: hidden;
  height: calc(100vh - 4rem);
  position: sticky;
  top: 4rem;
}

.admin-sidebar.collapsed {
  width: 70px;
}

.admin-sidebar.collapsed .admin-nav-item span,
.admin-sidebar.collapsed .admin-nav-heading,
.admin-sidebar.collapsed .status-text,
.admin-sidebar.collapsed .sidebar-collapse-toggle span {
  display: none;
}

.admin-sidebar.collapsed .admin-nav-item {
  justify-content: center;
  padding: 0.75rem;
}

.admin-sidebar.collapsed .sidebar-collapse-toggle {
  justify-content: center;
}

.admin-nav {
  display: flex;
  flex-direction: column;
  padding: 1.5rem 0;
  flex: 1;
  overflow-y: auto;
}

.admin-nav-section {
  margin-bottom: 1.5rem;
}

.admin-nav-heading {
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  color: var(--admin-text-muted);
  margin: 1.5rem 1.25rem 0.5rem;
  letter-spacing: 0.05em;
}

.admin-nav-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1.25rem;
  color: var(--admin-text-secondary);
  text-decoration: none;
  font-size: 0.875rem;
  font-weight: 500;
  border-radius: 0.25rem;
  margin: 0 0.5rem;
  transition: background-color 0.2s ease, color 0.2s ease;
}

.admin-nav-item:hover {
  background-color: var(--admin-nav-hover);
  color: var(--admin-text-primary);
  text-decoration: none;
}

.admin-nav-item.active {
  background-color: var(--admin-active-item-bg);
  color: var(--admin-accent);
  font-weight: 600;
}

.admin-nav-item svg {
  flex-shrink: 0;
}

.admin-sidebar-footer {
  padding: 1rem;
  border-top: 1px solid var(--admin-border);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.system-status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.75rem;
  color: var(--admin-text-secondary);
}

.status-indicator {
  width: 0.5rem;
  height: 0.5rem;
  border-radius: 50%;
}

.status-indicator.online {
  background-color: var(--admin-success);
}

.sidebar-collapse-toggle {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: none;
  border: none;
  color: var(--admin-text-secondary);
  font-size: 0.75rem;
  cursor: pointer;
  padding: 0.375rem;
  border-radius: 0.25rem;
  transition: background-color 0.2s ease, color 0.2s ease;
}

.sidebar-collapse-toggle:hover {
  background-color: var(--admin-nav-hover);
  color: var(--admin-text-primary);
}

/* Main Content Area */
.admin-content {
  flex: 1;
  padding: 1.5rem;
  overflow-y: auto;
}

.admin-content-wrapper {
  max-width: 1280px;
  margin: 0 auto;
}

/* General admin link styles */
.admin-content a:hover,
.admin-header a:hover,
.admin-sidebar a:hover {
  text-decoration: none;
}

/* Flash Messages */
.admin-flash-message {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem;
  border-radius: 0.375rem;
  margin-bottom: 1.5rem;
  position: relative;
}

.admin-flash-message.success {
  background-color: rgba(16, 185, 129, 0.1);
  border: 1px solid var(--admin-success);
  color: var(--admin-success);
}

.admin-flash-message.error {
  background-color: rgba(239, 68, 68, 0.1);
  border: 1px solid var(--admin-danger);
  color: var(--admin-danger);
}

.admin-flash-message.warning {
  background-color: rgba(245, 158, 11, 0.1);
  border: 1px solid var(--admin-warning);
  color: var(--admin-warning);
}

.admin-flash-message.info {
  background-color: rgba(59, 130, 246, 0.1);
  border: 1px solid var(--admin-info);
  color: var(--admin-info);
}

.close-flash {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  background: none;
  border: none;
  font-size: 1.25rem;
  cursor: pointer;
  color: currentColor;
  opacity: 0.7;
  padding: 0.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
}

.close-flash:hover {
  opacity: 1;
}

/* Cards */
.admin-card {
  background-color: var(--admin-surface);
  border-radius: 0.5rem;
  border: 1px solid var(--admin-border);
  overflow: hidden;
  box-shadow: var(--admin-shadow);
  margin-bottom: 1.5rem;
}

.admin-card-header {
  padding: 1rem 1.5rem;
  border-bottom: 1px solid var(--admin-border);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.admin-card-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--admin-text-primary);
  margin: 0;
}

.admin-card-body {
  padding: 1.5rem;
}

.admin-card-footer {
  padding: 1rem 1.5rem;
  border-top: 1px solid var(--admin-border);
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 0.75rem;
}

/* Dashboard Stats */
.admin-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.admin-stat-card {
  background-color: var(--admin-surface);
  border-radius: 0.5rem;
  border: 1px solid var(--admin-border);
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  box-shadow: var(--admin-shadow);
}

.admin-stat-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.admin-stat-title {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--admin-text-secondary);
  margin: 0;
}

.admin-stat-icon {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 0.375rem;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.admin-stat-icon.posts {
  background-color: var(--admin-accent);
}

.admin-stat-icon.comments {
  background-color: var(--admin-info);
}

.admin-stat-icon.categories {
  background-color: var(--admin-success);
}

.admin-stat-icon.users {
  background-color: var(--admin-warning);
}

.admin-stat-value {
  font-size: 2rem;
  font-weight: 700;
  color: var(--admin-text-primary);
  line-height: 1;
}

.admin-stat-info {
  font-size: 0.875rem;
  color: var(--admin-text-secondary);
}

/* Tables */
.admin-table-container {
  background-color: var(--admin-surface);
  border-radius: 0.5rem;
  border: 1px solid var(--admin-border);
  overflow: hidden;
  box-shadow: var(--admin-shadow);
  margin-bottom: 2rem;
}

.admin-table-header {
  padding: 1rem 1.5rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid var(--admin-border);
}

.admin-table-title {
  font-size: 1.125rem;
  font-weight: 600;
  margin: 0;
}

.admin-table-actions {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.admin-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.875rem;
}

.admin-table th {
  background-color: var(--admin-bg);
  text-align: left;
  padding: 0.75rem 1.5rem;
  font-weight: 600;
  color: var(--admin-text-secondary);
  border-bottom: 1px solid var(--admin-border);
}

.admin-table td {
  padding: 1rem 1.5rem;
  border-bottom: 1px solid var(--admin-border);
  color: var(--admin-text-primary);
}

.admin-table tr:last-child td {
  border-bottom: none;
}

.admin-table tr:hover td {
  background-color: var(--admin-bg);
}

.admin-table-footer {
  padding: 1rem 1.5rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-top: 1px solid var(--admin-border);
}

/* Status Badges */
.admin-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.25rem 0.625rem;
  border-radius: 1rem;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
}

.admin-badge-primary {
  background-color: rgba(59, 130, 246, 0.15);
  color: var(--admin-accent);
}

.admin-badge-success {
  background-color: rgba(16, 185, 129, 0.15);
  color: var(--admin-success);
}

.admin-badge-warning {
  background-color: rgba(245, 158, 11, 0.15);
  color: var(--admin-warning);
}

.admin-badge-danger {
  background-color: rgba(239, 68, 68, 0.15);
  color: var(--admin-danger);
}

/* Buttons */
.admin-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: background-color 0.2s ease, color 0.2s ease, border-color 0.2s ease;
}

/* Remove underlines from buttons on hover */
.admin-btn:hover, 
.admin-btn:focus {
  text-decoration: none;
}

.admin-btn-primary {
  background-color: var(--admin-accent);
  border: 1px solid var(--admin-accent);
  color: white;
}

.admin-btn-primary:hover {
  background-color: var(--admin-accent-hover);
  border-color: var(--admin-accent-hover);
}

.admin-btn-secondary {
  background-color: transparent;
  border: 1px solid var(--admin-border);
  color: var(--admin-text-primary);
}

.admin-btn-secondary:hover {
  background-color: var(--admin-bg);
  border-color: var(--admin-text-secondary);
}

.admin-btn-danger {
  background-color: var(--admin-danger);
  border: 1px solid var(--admin-danger);
  color: white;
}

.admin-btn-danger:hover {
  background-color: #DC2626;
  border-color: #DC2626;
}

/* Disabled state for admin buttons */
.admin-btn:disabled,
.admin-btn.is-disabled {
  opacity: 0.55;
  cursor: not-allowed;
  filter: grayscale(0.3);
  box-shadow: none;
}

.admin-btn-icon {
  padding: 0.5rem;
}

/* Forms */
.admin-form-group {
  margin-bottom: 1.5rem;
}

.admin-form-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  margin-bottom: 0.5rem;
  color: var(--admin-text-primary);
}

.admin-form-input,
.admin-form-select,
.admin-form-textarea {
  width: 100%;
  padding: 0.625rem 0.75rem;
  border: 1px solid var(--admin-border);
  border-radius: 0.375rem;
  background-color: var(--admin-surface);
  color: var(--admin-text-primary);
  font-size: 0.875rem;
  font-family: 'Inter', sans-serif;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.admin-form-textarea {
  min-height: 150px;
  resize: vertical;
}

.admin-form-input:focus,
.admin-form-select:focus,
.admin-form-textarea:focus {
  outline: none;
  border-color: var(--admin-accent);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
}

.admin-form-input::placeholder,
.admin-form-textarea::placeholder {
  color: var(--admin-text-muted);
}

.admin-form-error {
  color: var(--admin-danger);
  font-size: 0.75rem;
  margin-top: 0.375rem;
}

.admin-form-hint {
  color: var(--admin-text-secondary);
  font-size: 0.75rem;
  margin-top: 0.375rem;
}

.admin-form-row {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.admin-form-col {
  flex: 1;
}

/* Mobile Sidebar Overlay */
.admin-sidebar-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 150;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease, visibility 0.3s ease;
}

.admin-sidebar-overlay.active {
  opacity: 1;
  visibility: visible;
}

/* Responsive adjustments */
@media (max-width: 1024px) {
  .admin-layout {
    position: relative;
  }
  
  .admin-sidebar {
    position: fixed;
    top: 4rem;
    left: 0;
    z-index: 200;
    transform: translateX(-100%);
    transition: transform 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
    height: calc(100vh - 4rem);
    width: 280px;
  }
  
  .admin-sidebar.active {
    transform: translateX(0);
  }
  
  .admin-sidebar.collapsed {
    width: 280px; /* Override collapsed width on mobile */
  }
  
  .admin-sidebar.collapsed .admin-nav-item span,
  .admin-sidebar.collapsed .admin-nav-heading,
  .admin-sidebar.collapsed .status-text,
  .admin-sidebar.collapsed .sidebar-collapse-toggle span {
    display: block; /* Show text on mobile even when "collapsed" */
  }
  
  .admin-sidebar.collapsed .admin-nav-item {
    justify-content: flex-start; /* Align left on mobile */
    padding: 0.75rem 1.25rem;
  }
  
  .admin-sidebar.collapsed .sidebar-collapse-toggle {
    justify-content: flex-start;
  }
  
  .admin-content {
    margin-left: 0;
    width: 100%;
  }
  
  .admin-form-row {
    flex-direction: column;
    gap: 1.5rem;
  }
}

@media (max-width: 768px) {
  .admin-header {
    padding: 0.75rem 1rem;
  }
  
  .admin-header-center {
    display: none;
  }
  
  .admin-content {
    padding: 1rem;
  }
  
  .admin-stats-grid {
    grid-template-columns: 1fr;
  }
  
  .user-name {
    display: none;
  }
  
  .admin-sidebar {
    width: 260px; /* Slightly smaller on mobile */
  }
}

@media (max-width: 640px) {
  .admin-brand {
    font-size: 1.25rem;
  }
  
  .admin-label {
    display: none;
  }
  
  .admin-table th,
  .admin-table td {
    padding: 0.75rem 1rem;
  }
  
  .admin-sidebar {
    width: 240px; /* Even smaller on very small screens */
  }
}

@media (max-width: 480px) {
  .admin-sidebar {
    width: 220px;
  }
  
  .admin-header {
    padding: 0.5rem 0.75rem;
  }
  
  .admin-content {
    padding: 0.75rem;
  }
}

@media (max-width: 360px) {
  .admin-sidebar {
    width: 200px;
  }
  
  .admin-nav-item {
    padding: 0.625rem 1rem;
    font-size: 0.8125rem;
  }
  
  .admin-nav-heading {
    margin: 1.25rem 1rem 0.375rem;
    font-size: 0.6875rem;
  }
}

/* Custom Dialog Modal Styles */
.custom-dialog-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.6);
    z-index: 1050; /* Higher than other elements */
    display: none; /* Hidden by default */
    align-items: center;
    justify-content: center;
    padding: var(--space-md);
}

.custom-dialog-modal.is-visible {
    display: flex;
}

.custom-dialog-content {
    background-color: var(--admin-surface);
    border-radius: var(--border-radius-md);
    box-shadow: var(--admin-shadow-md);
    padding: var(--space-lg);
    max-width: 500px;
    width: 100%;
    position: relative;
    text-align: center;
}

.custom-dialog-close-btn {
    position: absolute;
    top: var(--space-sm);
    right: var(--space-sm);
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--admin-text-muted);
    cursor: pointer;
    padding: var(--space-xs);
    line-height: 1;
}

.custom-dialog-icon {
    font-size: 2rem;
    margin-bottom: var(--space-md);
}

.custom-dialog-icon.icon-success { color: var(--admin-success); }
.custom-dialog-icon.icon-error { color: var(--admin-danger); }
.custom-dialog-icon.icon-warning { color: var(--admin-warning); }
.custom-dialog-icon.icon-info { color: var(--admin-info); }
.custom-dialog-icon.icon-confirm { color: var(--admin-accent); }

.custom-dialog-message {
    margin-bottom: var(--space-lg);
    color: var(--admin-text-secondary);
    font-size: var(--font-size-body);
}

.custom-dialog-actions {
    display: flex;
    justify-content: center;
    gap: var(--space-md);
}

/* Hide cancel button by default for non-confirm dialogs */
#custom-dialog-cancel-btn {
    display: none;
} 