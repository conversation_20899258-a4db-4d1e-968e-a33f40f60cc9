import { removeMarkdown, showNotification } from '../utils/EditorUtils.js';

/**
 * Handles the "Outline" step functionalities.
 */
export class OutlineHandler {
    constructor(uiManager, stateManager, chatFeature) {
        this.uiManager = uiManager;
        this.stateManager = stateManager;
        this.chatFeature = chatFeature;
        
        this.elements = uiManager.elements;
        this.draggingElement = null;
    }

    init() {
        this.elements.regenerateOutlineBtn?.addEventListener('click', () => this.generateOutline());
        this.elements.approveOutlineBtn?.addEventListener('click', () => this.approveOutline());
        this.elements.toggleOutlineOptionsBtn?.addEventListener('click', () => this.uiManager.toggleOutlineOptions());
        this.elements.outlineTabs?.forEach(tab => {
            tab.addEventListener('click', () => {
                const tabName = tab.dataset.tab;
                this.uiManager.switchOutlineTab(tabName);
                if (tabName === 'visual') this.renderOutlineMap();
            });
        });
        this.elements.outlineDepthSelect?.addEventListener('change', () => this.generateOutline());
        this.elements.outlineStyleSelect?.addEventListener('change', () => this.generateOutline());
    }

    generateOutline() {
        const selectedIdea = this.stateManager.getStateValue('selectedIdea');
        if (!selectedIdea || !selectedIdea.db_id) {
            showNotification('Please select a generated idea first.', 'warning');
            // Ensure we do not advance or render anything if no idea is selected
            return;
        }

        const outlineDepth = this.elements.outlineDepthSelect.value;
        const outlineStyle = this.elements.outlineStyleSelect.value;
        const ideaPreset = this.stateManager.getStateValue('ideaPreset');

        this.elements.regenerateOutlineBtn.disabled = true;
        this.elements.approveOutlineBtn.disabled = true;
        this.elements.outlineContainer.innerHTML = '<div class="ai-loading">Generating outline...</div>';
        this.elements.outlineMapContainer.innerHTML = ''; // Clear map

        this.chatFeature.addAssistantMessage(`Creating a ${outlineStyle} outline for "${selectedIdea.title}" using the "${ideaPreset}" preset.`);

        const useStreaming = true; // Or from config

        if (useStreaming) {
            this.streamOutline(selectedIdea, outlineDepth, outlineStyle, ideaPreset);
        } else {
            this.fetchOutline(selectedIdea, outlineDepth, outlineStyle, ideaPreset);
        }
    }

    streamOutline(selectedIdea, outlineDepth, outlineStyle, preset) {
        const onRetry = () => {
            this.streamOutline(selectedIdea, outlineDepth, outlineStyle, preset);
        };

        const statusMessage = document.createElement('div');
        statusMessage.className = 'ai-status-message';
        statusMessage.textContent = 'Generating outline...';
        this.elements.outlineContainer.innerHTML = ''; // Clear for streaming
        this.elements.outlineContainer.appendChild(statusMessage);

        const outlineItemsContainer = document.createElement('div');
        outlineItemsContainer.className = 'ai-outline-items-container';
        this.elements.outlineContainer.appendChild(outlineItemsContainer);
        
        let collectedContent = "";
        let currentSectionsRaw = []; // Store raw text for each section

        const title = selectedIdea.title || 'Untitled Article';
        const description = selectedIdea.description || `Article about ${title}`;
        // Prioritize db_id if available, otherwise fallback (though fallback might still be problematic)
        const blogIdeaId = selectedIdea.db_id || (selectedIdea.client_id ? selectedIdea.client_id.replace('idea-', '') : '1');

        if (!selectedIdea.db_id) {
            console.warn('OutlineHandler: db_id not found for selectedIdea. Using client_id as fallback for blog_idea_id. This might cause issues if client_id is not a valid DB ID.', selectedIdea);
            showNotification('Warning: Database ID for the idea is missing. Outline might not save correctly.', 'warning');
        }

        const eventSource = new EventSource(
            `/api/admin/ai/blog-outlines/stream?` +
            `title=${encodeURIComponent(title)}` +
            `&description=${encodeURIComponent(description)}` +
            `&blog_idea_id=${encodeURIComponent(blogIdeaId)}` + // This now uses db_id if present
            `&outline_depth=${encodeURIComponent(outlineDepth)}` +
            `&outline_style=${encodeURIComponent(outlineStyle)}` +
            `&preset=${encodeURIComponent(preset)}`
        );

        eventSource.addEventListener('message', (event) => {
            try {
                // Basic check to ensure we are trying to parse a JSON object
                if (event.data && event.data.trim().startsWith('{')) {
                    const data = JSON.parse(event.data);
                    switch (data.type) {
                        case 'chunk':
                            collectedContent += data.content;
                            this.updateStreamedOutlineItems(collectedContent, outlineItemsContainer, statusMessage, currentSectionsRaw);
                            break;
                        case 'end':
                            this.finalizeStreamedOutline(collectedContent, outlineItemsContainer, statusMessage, data.content.outline_id);
                            eventSource.close();
                            this.resetOutlineButtons();
                            break;
                        case 'error':
                            const errMsg = data.message || 'Stream error';
                            showNotification(errMsg || 'Failed to stream outline', 'error');
                            statusMessage.remove();
                            outlineItemsContainer.innerHTML = `<div class="ai-error">Error: ${errMsg}</div>`;
                            const retryBtn = document.createElement('button');
                            retryBtn.className = 'ai-btn ai-btn-secondary';
                            retryBtn.textContent = 'Try Again';
                            retryBtn.addEventListener('click', onRetry);
                            outlineItemsContainer.appendChild(retryBtn);
                            this.chatFeature.addAssistantMessage(`Outline stream error: ${errMsg}`);
                            eventSource.close();
                            this.resetOutlineButtons();
                            break;
                    }
                } else if (event.data) {
                    // Handle cases where the stream might send non-JSON data or gets cut off
                    console.warn("Received non-JSON or incomplete data from stream:", event.data);
                }
            } catch (error) {
                console.error('Error handling outline stream message:', error, `Data: ${event.data}`);
            }
        });
        eventSource.addEventListener('error', (e) => {
            console.error('Outline streaming connection error:', e);
            showNotification('Connection error during outline generation.', 'error');
            statusMessage.remove();
            outlineItemsContainer.innerHTML = '<div class="ai-error">Error: Connection lost.</div>';
            const retryBtn = document.createElement('button');
            retryBtn.className = 'ai-btn ai-btn-secondary';
            retryBtn.textContent = 'Try Again';
            retryBtn.addEventListener('click', onRetry);
            outlineItemsContainer.appendChild(retryBtn);
            this.chatFeature.addAssistantMessage('Connection error during outline generation.');
            eventSource.close();
            this.resetOutlineButtons();
        });
    }
    
    updateStreamedOutlineItems(fullContent, container, statusMessage, currentSectionsRaw) {
        // Clip anything at or after the machine-readable JSON block so it doesn't show in cards
        let displayContent = fullContent;
        const jsonStart = displayContent.search(/<OUTLINE_JSON/i);
        if (jsonStart !== -1) {
            displayContent = displayContent.substring(0, jsonStart);
        }
        // Use a regex that looks for "SECTION <number>:" to split the content.
        // The positive lookahead `(?=...)` ensures the delimiter is kept.
        const sections = displayContent.split(/(?=SECTION\s+#?\d+:)/i).filter(s => s.trim().startsWith('SECTION'));

        // Iterate over all available sections, including the last (potentially incomplete) one.
        sections.forEach((sectionContent, i) => {
            // The createOrUpdateOutlineItemDOM function is designed to handle partial content,
            // so we can call it on every update.
            if (currentSectionsRaw[i] !== sectionContent) {
                currentSectionsRaw[i] = sectionContent;
                this.createOrUpdateOutlineItemDOM(i, sectionContent, container);
                statusMessage.textContent = `Generating section #${i + 1}...`;
            }
        });
    }

    finalizeStreamedOutline(finalContent, container, statusMessage, outlineId) {
        statusMessage.remove();

        // Final parse of the entire collected content to ensure everything is captured.
        // Final parse of the entire collected content to ensure everything is captured.
        const clipped = finalContent.split(/<OUTLINE_JSON/i)[0] || finalContent;
        const sections = clipped.split(/(?=SECTION\s+#?\d+:)/i).filter(s => s.trim().startsWith('SECTION'));
        const finalOutline = sections.map((sectionContent) => {
            return this.parseSectionContent(sectionContent);
        });

        if (finalOutline.length === 0) {
            container.innerHTML = '<div class="ai-error">Could not parse a valid outline from the stream.</div>';
            this.chatFeature.addAssistantMessage('I had trouble parsing the generated outline. Please try again.');
            return;
        }

        this.stateManager.setOutline(finalOutline);
        this.stateManager.updateState('outlineId', outlineId);

        this.renderOutline(); // Re-render from the final, clean state
        this.renderOutlineMap();
        this.chatFeature.addAssistantMessage(`Outline ready with ${finalOutline.length} sections. Review and approve.`);
    }


    createOrUpdateOutlineItemDOM(index, content, container) {
        let itemEl = container.querySelector(`.ai-outline-item[data-index="${index}"]`);
        const parsedSection = this.parseSectionContent(content); // Ensure this robustly parses partial/full content

        if (!itemEl) {
            itemEl = document.createElement('div');
            itemEl.className = 'ai-outline-item generating';
            itemEl.dataset.index = index;
            itemEl.draggable = true; // Will be properly set up later in renderOutline
            // Basic structure, will be filled by parsedSection
            itemEl.innerHTML = `
                <div class="ai-outline-heading">
                    <span class="ai-outline-number">${index + 1}</span>
                    ${parsedSection.heading || `Generating section #${index + 1}...`}
                </div>
                <div class="ai-outline-description">${parsedSection.description || ''}</div>
                <div class="ai-outline-key-points"></div>
                `;
            container.appendChild(itemEl);
        }
        
        // Update content based on parsedSection
        itemEl.querySelector('.ai-outline-heading').innerHTML = `<span class="ai-outline-number">${index + 1}</span> ${parsedSection.heading || `Generating section #${index + 1}...`}`;
        itemEl.querySelector('.ai-outline-description').textContent = parsedSection.description || '';
        
        const keyPointsEl = itemEl.querySelector('.ai-outline-key-points');
        if (parsedSection.key_points && parsedSection.key_points.length > 0) {
            const sanitized = parsedSection.key_points
                .map(p => (p || '').replace(/^\s*(?:[-*•]|\d+\.)\s*/, '').trim())
                .filter(Boolean);
            keyPointsEl.innerHTML = sanitized.length ? ('<ul>' + sanitized.map(p => `<li>${p}</li>`).join('') + '</ul>') : '';
        } else {
            keyPointsEl.innerHTML = '';
        }

        let subsectionsEl = itemEl.querySelector('.ai-outline-subsections');
        if (parsedSection.subsections && parsedSection.subsections.length > 0) {
            if (!subsectionsEl) { // Create if it doesn't exist
                subsectionsEl = document.createElement('div');
                subsectionsEl.className = 'ai-outline-subsections';
                itemEl.appendChild(subsectionsEl); 
            }
            let subsectionsHtml = '<h4>Subsections:</h4>';
            parsedSection.subsections.forEach((sub, subIdx) => {
                let subKeyPointsHtml = '';
                if (sub.key_points && sub.key_points.length > 0) {
                    subKeyPointsHtml = '<div class="ai-outline-key-points ai-subsection-key-points"><ul>' + sub.key_points.map(p => `<li>${p}</li>`).join('') + '</ul></div>';
                }
                subsectionsHtml += `<div class="ai-outline-subsection">
                    <div class="ai-outline-subsection-heading"><span class="ai-outline-subsection-number">${index + 1}.${subIdx + 1}</span> ${sub.heading}</div>
                    <div class="ai-outline-subsection-description">${sub.description || ''}</div>
                    ${subKeyPointsHtml}
                </div>`;
            });
            subsectionsEl.innerHTML = subsectionsHtml;
        } else if (subsectionsEl) { 
            subsectionsEl.innerHTML = ''; 
        }
        return itemEl;
    }


    fetchOutline(selectedIdea, outlineDepth, outlineStyle, preset) {
        const title = selectedIdea.title || 'Untitled Article';
        const description = selectedIdea.description || `Article about ${title}`;
        // Prioritize db_id if available
        const blogIdeaId = selectedIdea.db_id || (selectedIdea.client_id ? selectedIdea.client_id.replace('idea-', '') : '1');

        if (!selectedIdea.db_id) {
            console.warn('OutlineHandler (fetch): db_id not found for selectedIdea. Using client_id as fallback. This might cause issues.', selectedIdea);
            showNotification('Warning: Database ID for the idea is missing. Outline might not save correctly.', 'warning');
        }

        fetch('/api/admin/ai/blog-outlines/generate', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json', 'X-Requested-With': 'XMLHttpRequest' },
            body: JSON.stringify({
                title, description, blog_idea_id: blogIdeaId,
                parameters: { outline_depth: outlineDepth, outline_style: outlineStyle, preset }
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success && data.outline) {
                const formattedOutline = this.formatApiOutline(data.outline, outlineDepth);
                this.stateManager.setOutline(formattedOutline);
                this.renderOutline();
                this.renderOutlineMap();
                this.chatFeature.addAssistantMessage(`Outline generated with ${formattedOutline.length} sections.`);
            } else {
                showNotification(data.message || 'Failed to generate outline', 'error');
                this.elements.outlineContainer.innerHTML = `<div class="ai-error">Error: ${data.message || 'API error'}</div>`;
                this.chatFeature.addAssistantMessage(`Outline generation error: ${data.message || 'Unknown'}`);
            }
        })
        .catch(error => {
            console.error('Error fetching outline:', error);
            showNotification('Server connection error for outline.', 'error');
            this.elements.outlineContainer.innerHTML = '<div class="ai-error">Error: Server connection failed.</div>';
            this.chatFeature.addAssistantMessage('Server error during outline generation.');
        })
        .finally(() => this.resetOutlineButtons());
    }

    resetOutlineButtons() {
        this.elements.regenerateOutlineBtn.disabled = false;
        this.elements.approveOutlineBtn.disabled = false;
    }

    formatApiOutline(apiOutline, outlineDepth) {
        let outline = [];
        if (typeof apiOutline === 'string') {
            try { outline = JSON.parse(apiOutline); }
            catch (e) { outline = this.parseOutlineFromTextFallback(apiOutline); }
        } else if (Array.isArray(apiOutline)) {
            outline = apiOutline;
        } else if (typeof apiOutline === 'object' && apiOutline !== null) {
            outline = Object.entries(apiOutline).map(([key, value]) => ({
                heading: key,
                description: typeof value === 'string' ? value : (value.description || ''),
                key_points: value.key_points || [], // Ensure key_points are handled here
                subsections: Array.isArray(value.subsections) ? value.subsections : []
            }));
        }

        return outline.map(item => ({
            heading: item.heading || 'Untitled Section',
            description: item.description || '',
            key_points: item.key_points || [],
            subsections: (item.subsections || []).map(sub => ({
                heading: sub.heading || 'Untitled Subsection',
                description: sub.description || '',
                key_points: sub.key_points || [],
                subSubsections: (sub.subSubsections || []).map(subSub => ({ 
                    heading: subSub.heading || 'Untitled Sub-subsection',
                    description: subSub.description || '',
                    key_points: subSub.key_points || [] // Added key_points for subSubsections
                }))
            }))
        }));
    }
    
    parseSectionContent(content) {
        if (!content) return { heading: '', description: '', key_points: [], subsections: [] };
        
        const cleanContent = removeMarkdown(content);
        const section = { heading: '', description: '', key_points: [], subsections: [] };
        let remainingContent = cleanContent;
    
        // 1. Extract Main Section Heading
        // This regex now handles "SECTION 1: Title" format and is anchored to the start.
        const headingMatch = remainingContent.match(/^SECTION\s*#?\d+\s*[:\-]?\s*(.*?)(?=\nDescription:|\nKey Points:|\nSUBSECTION|$)/is);
        if (headingMatch && headingMatch[1]) {
            section.heading = headingMatch[1].trim();
            remainingContent = remainingContent.substring(headingMatch[0].length).trim();
        } else {
            // Fallback for headings that might not be perfectly formatted
            const lines = remainingContent.split('\n');
            section.heading = lines[0].replace(/SECTION\s*#?\d+\s*[:\-]?\s*/i, '').trim();
            remainingContent = lines.slice(1).join('\n').trim();
        }
    
        // 2. Extract Main Section Description
        // This regex looks for "Description:" and captures until the next major keyword.
        const descMatch = remainingContent.match(/^Description\s*:\s*([\s\S]*?)(?=\nKey Points:|\nSUBSECTION|$)/is);
        if (descMatch && descMatch[1]) {
            section.description = descMatch[1].trim();
            remainingContent = remainingContent.substring(descMatch[0].length).trim();
        }
    
        // 3. Extract Main Section Key Points
        // This regex looks for "Key Points:" and captures until the next major keyword.
        const keyPointsMatch = remainingContent.match(/^Key Points\s*:\s*([\s\S]*?)(?=\nSUBSECTION|$)/is);
        if (keyPointsMatch && keyPointsMatch[1]) {
            section.key_points = keyPointsMatch[1].trim().split(/\n\s*[-*•]\s*|\n\s*\d+\.\s*/)
                                .map(p => p.trim()).filter(p => p && p.length > 0);
            remainingContent = remainingContent.substring(keyPointsMatch[0].length).trim();
        }
    
        // 4. Parse Subsections using a more robust method
        if (remainingContent.toLowerCase().includes('subsection')) {
            // Regex to find all subsection blocks, including their content.
            const subsectionRegex = /SUBSECTION\s*#?\s*\d+\.\d+[\s\S]*?(?=\nSUBSECTION\s*#?\s*\d+\.\d+|$)/gi;
            const subsectionBlocks = remainingContent.match(subsectionRegex);
    
            if (subsectionBlocks) {
                subsectionBlocks.forEach(block => {
                    const subSection = { heading: '', description: '', key_points: [] };
                    let subRemainingContent = block;
    
                    // Extract subsection heading
                    const subHeadingMatch = subRemainingContent.match(/^SUBSECTION\s*#?\s*\d+\.\d+\s*[:\-]?\s*(.*?)(?=\nDescription:|\nKey Points:|$)/i);
                    if (subHeadingMatch && subHeadingMatch[1]) {
                        subSection.heading = subHeadingMatch[1].trim();
                        subRemainingContent = subRemainingContent.substring(subHeadingMatch[0].length).trim();
                    }
    
                    // Extract subsection description
                    const subDescMatch = subRemainingContent.match(/^Description\s*:\s*([\s\S]*?)(?=\nKey Points:|$)/is);
                    if (subDescMatch && subDescMatch[1]) {
                        subSection.description = subDescMatch[1].trim();
                        subRemainingContent = subRemainingContent.substring(subDescMatch[0].length).trim();
                    }
    
                    // Extract subsection key points
                    const subKeyPointsMatch = subRemainingContent.match(/^Key Points\s*:\s*([\s\S]*?)$/is);
                    if (subKeyPointsMatch && subKeyPointsMatch[1]) {
                        subSection.key_points = subKeyPointsMatch[1].trim().split(/\n\s*[-*•]\s*|\n\s*\d+\.\s*/)
                                            .map(p => p.trim()).filter(p => p && p.length > 0);
                    }
    
                    section.subsections.push(subSection);
                });
            }
        }
        return section;
    }
    
    parseSectionFromDOM(itemEl) {
        const section = {
            heading: itemEl.querySelector('.ai-outline-item-title')?.textContent || '',
            description: itemEl.querySelector('.ai-outline-description')?.textContent || '',
            key_points: [],
            subsections: []
        };

        itemEl.querySelectorAll('.ai-outline-key-points > ul > li').forEach(li => {
            section.key_points.push(li.textContent);
        });

        itemEl.querySelectorAll('.ai-outline-subsection').forEach(subEl => {
            const subsection = {
                heading: subEl.querySelector('.ai-outline-subsection-heading')?.textContent.replace(/\d+\.\d+\s*/, '') || '',
                description: subEl.querySelector('.ai-outline-subsection-description')?.textContent || '',
                key_points: []
            };
            subEl.querySelectorAll('.ai-subsection-key-points > ul > li').forEach(li => {
                subsection.key_points.push(li.textContent);
            });
            section.subsections.push(subsection);
        });

        return section;
    }

    parseOutlineFromTextFallback(text) {
        const sections = [];
        const potentialSections = text.split(/\n\s*\n|(?=\n(?:Introduction|Conclusion|\d+\.\s*))/);
        
        potentialSections.forEach(block => {
            if (block.trim().length < 10) return; 

            const lines = block.trim().split('\n');
            const heading = lines[0].trim();
            const description = lines.slice(1).join(' ').trim();
            const key_points = lines.filter(l => l.trim().startsWith('- ') || l.trim().startsWith('* ')).map(l => l.trim().substring(2));

            sections.push({
                heading: heading || "Untitled Section",
                description: description || "No description.",
                key_points: key_points,
                subsections: [] 
            });
        });
        return sections.filter(s => s.heading); 
    }


    renderOutline() {
        const outline = this.stateManager.getStateValue('outline');
        this.elements.outlineContainer.innerHTML = ''; 
        if (!outline || outline.length === 0) {
            this.elements.outlineContainer.innerHTML = '<p>No outline generated yet.</p>';
            return;
        }

        outline.forEach((item, index) => {
            const itemEl = this.createOutlineItemElement(item, index);
            this.elements.outlineContainer.appendChild(itemEl);
        });
        this.setupOutlineDragAndDrop();
    }

    createOutlineItemElement(item, index) {
        const itemEl = document.createElement('div');
        itemEl.className = 'ai-outline-item';
        itemEl.dataset.index = index;
        itemEl.draggable = true;

        let keyPointsHtml = '';
        if (item.key_points && item.key_points.length > 0) {
            const sanitized = item.key_points
                .map(p => (p || '').replace(/^\s*(?:[-*•]|\d+\.)\s*/, '').trim())
                .filter(Boolean);
            if (sanitized.length) {
                keyPointsHtml = '<div class="ai-outline-key-points"><ul>' + sanitized.map(p => `<li>${p}</li>`).join('') + '</ul></div>';
            }
        }
        let subsectionsHtml = '';
        if (item.subsections && item.subsections.length > 0) {
            subsectionsHtml = '<div class="ai-outline-subsections"><h4>Subsections:</h4>';
            item.subsections.forEach((sub, subIdx) => {
                let subKeyPointsHtml = '';
                if (sub.key_points && sub.key_points.length > 0) {
                    subKeyPointsHtml = '<div class="ai-outline-key-points ai-subsection-key-points"><ul>' + sub.key_points.map(p => `<li>${p}</li>`).join('') + '</ul></div>';
                }
                subsectionsHtml += `<div class="ai-outline-subsection">
                    <div class="ai-outline-subsection-heading"><span class="ai-outline-subsection-number">${index + 1}.${subIdx + 1}</span> ${sub.heading}</div>
                    <div class="ai-outline-subsection-description">${sub.description || ''}</div>
                    ${subKeyPointsHtml}
                </div>`;
            });
            subsectionsHtml += '</div>';
        }

        itemEl.innerHTML = `
            <div class="ai-outline-heading">
                <span class="ai-outline-number">${index + 1}</span>
                <span class="ai-outline-item-title" contenteditable="true">${item.heading}</span>
            </div>
            <div class="ai-outline-description" contenteditable="true">${item.description}</div>
            ${keyPointsHtml}
            ${subsectionsHtml}
            <div class="ai-outline-controls">
                <button class="ai-outline-control-btn ai-outline-move-up" title="Move up">↑</button>
                <button class="ai-outline-control-btn ai-outline-move-down" title="Move down">↓</button>
            </div>`;
        
        itemEl.querySelector('.ai-outline-item-title').addEventListener('blur', (e) => this.updateOutlineItemText(index, 'heading', e.target.textContent));
        itemEl.querySelector('.ai-outline-description').addEventListener('blur', (e) => this.updateOutlineItemText(index, 'description', e.target.textContent));
        
        itemEl.querySelector('.ai-outline-move-up').addEventListener('click', () => this.reorderOutlineItem(index, index - 1));
        itemEl.querySelector('.ai-outline-move-down').addEventListener('click', () => this.reorderOutlineItem(index, index + 1));

        return itemEl;
    }
    
    updateOutlineItemText(index, field, newText) {
        const outline = this.stateManager.getStateValue('outline');
        if (outline[index] && outline[index][field] !== newText) {
            outline[index][field] = newText;
            this.stateManager.setOutline([...outline]); 
            this.stateManager.setDirty(true);
            this.renderOutlineMap(); 
        }
    }

    setupOutlineDragAndDrop() {
        const items = this.elements.outlineContainer.querySelectorAll('.ai-outline-item');
        items.forEach(item => {
            item.addEventListener('dragstart', this.handleDragStart.bind(this));
            item.addEventListener('dragover', this.handleDragOver.bind(this));
            item.addEventListener('drop', this.handleDrop.bind(this));
            item.addEventListener('dragend', this.handleDragEnd.bind(this));
        });
    }

    handleDragStart(e) {
        this.draggingElement = e.target.closest('.ai-outline-item');
        e.dataTransfer.effectAllowed = 'move';
        e.dataTransfer.setData('text/plain', this.draggingElement.dataset.index);
        this.draggingElement.classList.add('dragging');
    }

    handleDragOver(e) {
        e.preventDefault();
        e.dataTransfer.dropEffect = 'move';
        const targetItem = e.target.closest('.ai-outline-item');
        if (targetItem && targetItem !== this.draggingElement) {
            this.elements.outlineContainer.querySelectorAll('.drag-over').forEach(el => el.classList.remove('drag-over'));
            targetItem.classList.add('drag-over');
        }
    }

    handleDrop(e) {
        e.preventDefault();
        const fromIndex = parseInt(e.dataTransfer.getData('text/plain'));
        const toItem = e.target.closest('.ai-outline-item');
        if (toItem) {
            const toIndex = parseInt(toItem.dataset.index);
            if (fromIndex !== toIndex) {
                this.reorderOutlineItem(fromIndex, toIndex);
            }
        }
        this.elements.outlineContainer.querySelectorAll('.drag-over').forEach(el => el.classList.remove('drag-over'));
    }

    handleDragEnd() {
        if (this.draggingElement) {
            this.draggingElement.classList.remove('dragging');
        }
        this.draggingElement = null;
        this.elements.outlineContainer.querySelectorAll('.drag-over').forEach(el => el.classList.remove('drag-over'));
    }

    reorderOutlineItem(fromIndex, toIndex) {
        const outline = this.stateManager.getStateValue('outline');
        if (toIndex < 0 || toIndex >= outline.length) return;

        const item = outline.splice(fromIndex, 1)[0];
        outline.splice(toIndex, 0, item);

        this.stateManager.setOutline([...outline]); 
        this.renderOutline(); 
        this.renderOutlineMap();
        this.stateManager.setDirty(true);
        this.chatFeature.addAssistantMessage('Outline sections reordered.');
    }

    renderOutlineMap() {
        const outline = this.stateManager.getStateValue('outline');
        const selectedIdea = this.stateManager.getStateValue('selectedIdea');
        this.elements.outlineMapContainer.innerHTML = ''; 
        if (!outline || outline.length === 0 || !selectedIdea) return;

        const treeContainer = document.createElement('div');
        treeContainer.className = 'ai-outline-tree';
        const rootNode = document.createElement('div');
        rootNode.className = 'ai-tree-node ai-tree-root';
        rootNode.innerHTML = `<div class="ai-tree-node-content"><div class="ai-tree-node-title">${selectedIdea.title}</div></div>`;
        treeContainer.appendChild(rootNode);

        const childrenContainer = document.createElement('div');
        childrenContainer.className = 'ai-tree-children';
        rootNode.appendChild(childrenContainer);

        outline.forEach((item, index) => {
            const sectionBranch = document.createElement('div');
            sectionBranch.className = 'ai-tree-branch';
            const sectionNode = document.createElement('div');
            sectionNode.className = 'ai-tree-node ai-tree-section';
            sectionNode.innerHTML = `<div class="ai-tree-node-content"><div class="ai-tree-node-number">${index + 1}</div><div class="ai-tree-node-title">${item.heading}</div></div>`;
            sectionBranch.appendChild(sectionNode);

            if (item.subsections && item.subsections.length > 0) {
                const subsectionsContainer = document.createElement('div');
                subsectionsContainer.className = 'ai-tree-children';
                item.subsections.forEach((sub, subIdx) => {
                    const subBranch = document.createElement('div');
                    subBranch.className = 'ai-tree-branch';
                    const subNode = document.createElement('div');
                    subNode.className = 'ai-tree-node ai-tree-subsection';
                    subNode.innerHTML = `<div class="ai-tree-node-content"><div class="ai-tree-node-number">${index + 1}.${subIdx + 1}</div><div class="ai-tree-node-title">${sub.heading}</div></div>`;
                    subBranch.appendChild(subNode);
                    
                    if (sub.subSubsections && sub.subSubsections.length > 0) {
                        const subSubContainer = document.createElement('div');
                        subSubContainer.className = 'ai-tree-children';
                        sub.subSubsections.forEach((subSubItem, subSubIdx) => {
                             const subSubNode = document.createElement('div');
                             subSubNode.className = 'ai-tree-node ai-tree-subsubsection'; 
                             subSubNode.innerHTML = `<div class="ai-tree-node-content"><div class="ai-tree-node-number">${index + 1}.${subIdx + 1}.${subSubIdx + 1}</div><div class="ai-tree-node-title">${subSubItem.heading}</div></div>`;
                             subSubContainer.appendChild(subSubNode);
                        });
                        subBranch.appendChild(subSubContainer);
                    }
                    subsectionsContainer.appendChild(subBranch);
                });
                sectionBranch.appendChild(subsectionsContainer);
            }
            childrenContainer.appendChild(sectionBranch);
        });
        this.elements.outlineMapContainer.appendChild(treeContainer);
    }

    approveOutline() {
        const outline = this.stateManager.getStateValue('outline');
        if (!outline || outline.length === 0) {
            showNotification('Please generate an outline first', 'warning');
            return;
        }
        this.stateManager.setCurrentStep('content');
        document.dispatchEvent(new CustomEvent('aieditor:stepChange', { detail: { step: 'content' } }));
    }
}