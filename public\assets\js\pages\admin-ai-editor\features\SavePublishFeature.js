/**
 * SaveDraftFeature - Handles only draft saving functionality
 * Publish functionality has been completely removed
 */
class SavePublishFeature {
    constructor(elements, stateManager, chatFeature) {
        this.elements = elements;
        this.stateManager = stateManager;
        this.chatFeature = chatFeature;
        this.csrfToken = this.getCSRFToken();
        this.isSubmitting = false; // Flag to prevent multiple submissions
        
        this.init();
    }

    init() {
        this.bindEvents();
        this.disablePublishButtons();
    }

    bindEvents() {
        // Bind publish buttons to show placeholder message
        if (this.elements.publishBtn) {
            this.elements.publishBtn.addEventListener('click', () => this.showPublishPlaceholder());
        }
        if (this.elements.publishMobileBtn) {
            this.elements.publishMobileBtn.addEventListener('click', () => this.showPublishPlaceholder());
        }
        // Save draft button (if exists)
        if (this.elements.saveBtn) {
            this.elements.saveBtn.removeEventListener('click', this.handleSave);
            this.elements.saveBtn.addEventListener('click', () => this.handleSave());
        }

        // Check for mobile save button too
        if (this.elements.saveMobileBtn) {
            this.elements.saveMobileBtn.removeEventListener('click', this.handleSave);
            this.elements.saveMobileBtn.addEventListener('click', () => this.handleSave());
        }
    }

    /**
     * Disable publish buttons and show placeholder message
     */
    disablePublishButtons() {
        if (this.elements.publishBtn) {
            this.elements.publishBtn.disabled = false; // Keep clickable to show message
            this.elements.publishBtn.innerHTML = '<span class="ai-btn-text">Publish</span>';
            this.elements.publishBtn.title = 'Publish functionality to be implemented';
            this.elements.publishBtn.style.cursor = 'pointer';
            this.elements.publishBtn.style.opacity = '0.7';
        }

        if (this.elements.publishMobileBtn) {
            this.elements.publishMobileBtn.disabled = false; // Keep clickable to show message
            this.elements.publishMobileBtn.innerHTML = 'Publish';
            this.elements.publishMobileBtn.title = 'Publish functionality to be implemented';
            this.elements.publishMobileBtn.style.cursor = 'pointer';
            this.elements.publishMobileBtn.style.opacity = '0.7';
        }
    }

    /**
     * Show placeholder message when publish button is clicked
     */
    showPublishPlaceholder() {
        alert('Publish functionality to be implemented.');
        console.log('Publish functionality has been removed and needs to be reimplemented.');
    }

    /**
     * Handle save draft button click
     */
    async handleSave() {
        if (this.isSubmitting) {
            console.warn('Save already in progress, ignoring duplicate click');
            return;
        }
        await this.savePost('draft');
    }

    async savePost(status = 'draft') {
        // Only allow draft saving - completely block any non-draft status
        if (status !== 'draft') {
            console.warn('Publish functionality has been disabled');
            return;
        }

        // Prevent multiple simultaneous submissions
        if (this.isSubmitting) {
            console.warn('Save operation already in progress');
            return;
        }
        
        this.isSubmitting = true;
        
        try {
            // Get the complete content from the state manager first (latest AI generated content)
            // Fall back to content area if state manager content is empty
            let rawContent = this.stateManager.getStateValue('content') || '';
            if (!rawContent.trim()) {
                const contentElement = this.elements.contentArea;
                rawContent = contentElement ? contentElement.innerHTML : '';
            }
            
            // Get all the metadata
            const selectedIdea = this.stateManager.getStateValue('selectedIdea');
            const title = selectedIdea?.title || 'Untitled Post';
            const seoData = this.stateManager.getStateValue('seoData') || {};

            // Validate required content
            if (!rawContent.trim()) {
                console.error('Please generate content before saving.');
                alert('Please generate content before saving.');
                return;
            }

            if (!title.trim()) {
                console.error('Please select an idea with a title before saving.');
                alert('Please select an idea with a title before saving.');
                return;
            }

            // Show progress message
            console.log('Saving draft...');
            
            // Disable button during request
            const button = this.elements.saveBtn;
            const originalText = button?.textContent || '';
            if (button) {
                button.disabled = true;
                button.textContent = 'Saving...';
            }

            // Generate unique request ID to prevent duplicate processing
            const requestId = `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
            
            // Prepare form data with complete content
            const formData = new FormData();
            formData.append('csrf_token', this.csrfToken);
            formData.append('request_id', requestId); // Add unique request ID
            formData.append('title', title);
            formData.append('content', rawContent); // Send the complete HTML content with images
            formData.append('meta_title', seoData.metaTitle || title);
            formData.append('meta_description', seoData.metaDescription || '');
            
            // Handle focus keywords properly
            let focusKeywords = '';
            if (Array.isArray(seoData.focusKeywords)) {
                focusKeywords = seoData.focusKeywords.join(', ');
            } else if (typeof seoData.focusKeywords === 'string') {
                focusKeywords = seoData.focusKeywords;
            }
            formData.append('focus_keywords', focusKeywords);
            
            formData.append('status', 'draft'); // Force draft status
            formData.append('category_id', '1'); // Default category

            // Make the request
            const response = await fetch('/admin/posts/store', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });

            if (response.ok) {
                const message = 'Post saved as draft!';
                console.log(message);
                alert(message);
            } else {
                // Don't retry for CSRF errors - just show error and let user refresh
                const errorText = await response.text();
                console.error('Save error:', errorText);
                
                if (response.status === 403) {
                    if (errorText.includes('Invalid security token') || errorText.includes('CSRF')) {
                        throw new Error('Security token expired. Please refresh the page and try again.');
                    } else {
                        throw new Error(`Access denied: ${errorText}`);
                    }
                } else {
                    throw new Error(`Failed to save post: ${response.status}`);
                }
            }

        } catch (error) {
            console.error('Save error:', error);
            alert(error.message || 'An error occurred while saving.');
        } finally {
            // Reset submission flag
            this.isSubmitting = false;
            
            // Re-enable button
            const button = this.elements.saveBtn;
            if (button) {
                button.disabled = false;
                button.textContent = 'Save Draft';
            }
        }
    }

    // Publish functionality has been completely removed
    // This method now only shows a placeholder message
    async handlePublish() {
        this.showPublishPlaceholder();
    }

    getCSRFToken() {
        const tokenElement = document.querySelector('meta[name="csrf-token"]') ||
                           document.querySelector('input[name="csrf_token"]');
        return tokenElement ? tokenElement.getAttribute('content') || tokenElement.value : '';
    }
}

// Export the class for module usage
export { SavePublishFeature };
