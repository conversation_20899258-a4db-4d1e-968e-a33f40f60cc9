<?php

declare(strict_types=1);

namespace Brenzley\Controllers\Admin;

use B<PERSON><PERSON>y\Core\View;
use <PERSON><PERSON><PERSON>y\Core\Session;
use B<PERSON>zley\Models\PostModel;
use B<PERSON>zley\Models\CategoryModel;

class PostController
{
    private PostModel $postModel;
    private CategoryModel $categoryModel;

    public function __construct()
    {
        $this->postModel = new PostModel();
        $this->categoryModel = new CategoryModel();
        // Note: Access control is currently handled in index.php router
        // In a real router/framework, this would be middleware.
    }

    /**
     * Display a list of all posts.
     */
    public function index(): void
    {
        $posts = $this->postModel->findAllWithStats();

        // Capture content in buffer to use with layout
        ob_start();
        View::render('admin/posts/index', [
            'title' => 'Manage Posts',
            'posts' => $posts
        ], null);
        $content = ob_get_clean();

        // Render with admin layout
        View::renderLayout('admin', [
            'title' => 'Manage Posts',
            'content' => $content,
            'userRole' => \Brenzley\Core\Session::get('user_role', '')
        ], ['admin-posts']);
    }

    /**
     * Show the form for creating a new post with AI-powered editor.
     */
    public function create(): void
    {
        $categories = $this->categoryModel->findAll();

        // Capture content in buffer to use with layout
        ob_start();
        View::render('admin/posts/ai-create', [
            'title' => 'Create New Article with AI',
            'categories' => $categories
        ], null);
        $content = ob_get_clean();

        // Render with admin layout
        View::renderLayout('admin', [
            'title' => 'Create New Article with AI',
            'content' => $content,
            'userRole' => Session::get('user_role', '')
        ], ['admin-ai-editor']);
    }

    /**
     * Store a newly created post in storage.
     */
    public function store(): void
    {
        // Validate CSRF token
        if (!Session::verifyCsrfToken($_POST['csrf_token'] ?? '')) {
            http_response_code(403);
            echo "Invalid security token.";
            return;
        }

        // Check for duplicate request using request ID
        $requestId = $_POST['request_id'] ?? '';
        if (!empty($requestId)) {
            $sessionKey = 'processed_request_' . $requestId;
            if (Session::has($sessionKey)) {
                // This request has already been processed
                error_log("Duplicate request detected: $requestId");
                http_response_code(409); // Conflict
                echo "Request already processed.";
                return;
            }
            // Mark this request as being processed
            Session::set($sessionKey, true, 300); // Expire after 5 minutes
        }

        // Validate input
        $title = trim($_POST['title'] ?? '');
        $content = $_POST['content'] ?? '';
        $metaTitle = trim($_POST['meta_title'] ?? '');
        $metaDescription = trim($_POST['meta_description'] ?? '');
        $focusKeywords = $_POST['focus_keywords'] ?? '';
        $status = 'draft'; // Force draft status only - publish functionality removed
        $categoryId = (int)($_POST['category_id'] ?? 1);

        $errors = [];

        if (empty($title)) {
            $errors['title'] = 'Title is required';
        }

        if (empty($content)) {
            $errors['content'] = 'Content is required';
        }

        // If there are errors, redirect back with errors and input
        if (!empty($errors)) {
            Session::set('errors', $errors);
            Session::set('old_input', $_POST);
            header('Location: /admin/posts/create');
            exit;
        }

        // Generate slug from title
        $slug = $this->generateSlug($title);

        // Process content to apply image alt texts and handle placeholders
        $processedContent = $this->processContentForPublication($content);

        // Prepare post data - only draft status allowed
        $postData = [
            'title' => $title,
            'slug' => $slug,
            'content' => $processedContent,
            'meta_title' => $metaTitle ?: $title,
            'meta_description' => $metaDescription,
            'status' => 'draft', // Force draft status
            'category_id' => $categoryId,
            'user_id' => Session::get('user_id'),
            'published_at' => null // Never set published_at since we only save drafts
        ];

        // Create the post
        $postId = $this->postModel->create($postData);

        if ($postId) {
            Session::set('flash_message', 'Post saved as draft successfully! Publish functionality to be implemented.');
            header('Location: /admin/posts');
        } else {
            Session::set('flash_message', 'Error creating post.');
            Session::set('old_input', $_POST);
            header('Location: /admin/posts/create');
        }
        exit;
    }

    /**
     * Process content for publication - comprehensive content processing
     */
    private function processContentForPublication(string $content): string
    {
        // Debug: Log original content structure
        error_log("Original content length: " . strlen($content));
        error_log("Original content preview: " . substr($content, 0, 500));
        
        // Pre-sanitize malformed nested AI placeholder structures
        $content = $this->preSanitizeAiPlaceholders($content);

        // DOM-based normalization to rebuild correct AI layout structures and preserve content
        $content = $this->normalizeAiLayoutsWithDom($content);

        // Step 1: Process all image placeholders with comprehensive pattern matching FIRST
        // This ensures any user-uploaded images inside AI placeholders are preserved
        $content = $this->processImagePlaceholders($content);
        error_log("After processing images length: " . strlen($content));
        
        // Step 2: Remove AI editor elements after conversion
        $content = $this->removeAiEditorElements($content);
        error_log("After removing AI elements length: " . strlen($content));
        
        // Final cleanup of any remaining malformed placeholder text
        $content = $this->cleanupMalformedPlaceholders($content);
        error_log("After placeholder cleanup length: " . strlen($content));
        
        // Step 3: Clean up any remaining artifacts
        $content = $this->cleanupContent($content);
        error_log("Final content length: " . strlen($content));
        
        return $content;
    }

    /**
     * Remove all AI editor elements and controls
     */
    private function removeAiEditorElements(string $content): string
    {
        // Remove editor-only controls but KEEP AI layout structure for public rendering
        // 1) Remove specific control blocks (buttons, controls, upload areas, CTAs)
        $controlPatterns = [
            '/<div[^>]*class="[^"]*ai-upload-text[^"]*"[^>]*>.*?<\/div>/s',
            '/<div[^>]*class="[^"]*ai-image-controls[^"]*"[^>]*>.*?<\/div>/s',
            '/<div[^>]*class="[^"]*ai-show-description-cta[^"]*"[^>]*>.*?<\/div>/s',
            '/<div[^>]*class="[^"]*ai-image-upload-area[^"]*"[^>]*>.*?<\/div>/s',
            '/<div[^>]*class="[^"]*ai-image-description-container[^"]*"[^>]*>.*?<\/div>/s',
            '/<button[^>]*class="[^"]*ai-upload-image[^"]*"[^>]*>.*?<\/button>/s',
            '/<button[^>]*class="[^"]*ai-show-description[^"]*"[^>]*>.*?<\/button>/s',
            '/<button[^>]*class="[^"]*ai-generate-image[^"]*"[^>]*>.*?<\/button>/s',
            // Optional: remove gallery header (purely editor UI)
            '/<div[^>]*class="[^"]*ai-gallery-header[^"]*"[^>]*>.*?<\/div>/s',
        ];
        foreach ($controlPatterns as $pattern) {
            $content = preg_replace($pattern, '', $content);
        }

        // 2) Strip editor-only attributes, but do NOT remove layout elements
        $attrPatterns = [
            '/\s*data-ai-[^=]*="[^"]*"/i',
            '/\s*data-placeholder-id="[^"]*"/i',
            '/\s*data-gallery="[^"]*"/i',
            '/\s*contenteditable="[^"]*"/i',
            // Remove inline user-select styles but keep other styles
            '/\s*style="[^"]*user-select:[^;\"]*;?[^"]*"/i',
        ];
        foreach ($attrPatterns as $pattern) {
            $content = preg_replace($pattern, '', $content);
        }

        // IMPORTANT: Do NOT remove .ai-image-placeholder, .ai-image-gallery, .ai-gallery-grid,
        // .image-text-wrapper, .adjacent-text-content, .image-text-overlay-content
        // These are required for public rendering parity with the editor.

        return $content;
    }

    /**
     * Process all types of image placeholders
     */
    private function processImagePlaceholders(string $content): string
    {
        // Pattern 1: Process existing post-image-placeholder divs (already processed)
        $content = preg_replace_callback(
            '/<div[^>]*class="[^"]*post-image-placeholder[^"]*post-image-([^"]*)"[^>]*>(.*?)<\/div>/s',
            function($matches) {
                $layout = $matches[1];
                $innerContent = $matches[2];
                // Keep existing placeholders as they are
                return $matches[0];
            },
            $content
        );
        
        // Pattern 2: AI image placeholders with data-layout or class-based layout (most common)
        $content = preg_replace_callback(
            '/<div[^>]*class="[^"]*ai-image-placeholder[^"]*"[^>]*(?:data-layout="([^"]*)")?[^>]*>(.*?)<\/div>/s',
            function($matches) {
                // Enrich matches with class-based layout if data-layout is missing
                $full = $matches[0];
                $layoutFromAttr = $matches[1] ?? '';
                if (!$layoutFromAttr) {
                    if (preg_match('/class="[^"]*ai-image-layout-([^\s\"]+)/', $full, $lm)) {
                        $layoutFromAttr = $lm[1];
                    }
                }
                // Rebuild matches so processImagePlaceholder sees layout
                $matches[1] = $layoutFromAttr ?: 'center';
                return $this->processImagePlaceholder($matches);
            },
            $content
        );
        
        // Pattern 3: Image placeholders without ai- prefix but with layout
        $content = preg_replace_callback(
            '/<div[^>]*class="[^"]*image-placeholder[^"]*"[^>]*(?:data-layout="([^"]*)")?[^>]*>(.*?)<\/div>/s',
            [$this, 'processImagePlaceholder'],
            $content
        );
        
        // Pattern 4: Any div containing generated-image
        $content = preg_replace_callback(
            '/<div[^>]*>(.*?<img[^>]*class="[^"]*generated-image[^"]*"[^>]*>.*?)<\/div>/s',
            [$this, 'processImageContainer'],
            $content
        );
        
        // Pattern 5: Direct img tags that need wrapping
        $content = preg_replace_callback(
            '/<img[^>]*class="[^"]*generated-image[^"]*"[^>]*>/i',
            [$this, 'wrapStandaloneImage'],
            $content
        );
        
        // Pattern 6: Image-text-wrapper containers (for floated layouts)
        $content = preg_replace_callback(
            '/<div[^>]*class="[^"]*image-text-wrapper[^"]*"[^>]*data-layout="([^"]*)"[^>]*>(.*?)<\/div>/s',
            [$this, 'processFloatedContainer'],
            $content
        );
        
        // Pattern 7: Fix broken gallery structures
        $content = preg_replace_callback(
            '/<div[^>]*class="[^"]*post-image-gallery[^"]*post-image-gallery-(\d+)-item[^"]*"[^>]*>(.*?)<\/div>/s',
            [$this, 'fixGalleryStructure'],
            $content
        );
        
        // Pattern 8: Fix standalone placeholders with missing layout classes
        $content = preg_replace_callback(
            '/<div[^>]*class="[^"]*(?:post-image-placeholder post-image-|ai-image-placeholder ai-image-layout-)"[^>]*>(.*?)<\/div>/s',
            [$this, 'fixPlaceholderLayout'],
            $content
        );
        
        // Pattern 6: Look for image descriptions that should become placeholders
        $content = preg_replace_callback(
            '/\[IMAGE:\s*([^\]]+)\]/i',
            function($matches) {
                return $this->createImagePlaceholderWithAlt('center', $matches[1]);
            },
            $content
        );
        
        // Pattern 7: Handle standalone placeholder text spans
        $content = preg_replace_callback(
            '/<span[^>]*class="[^"]*placeholder-text[^"]*"[^>]*>Image not found<\/span>/i',
            function($matches) {
                return $this->createImagePlaceholder('center');
            },
            $content
        );
        
        // Pattern 8: Handle any remaining bare "Image not found" text that should be placeholders
        $content = preg_replace_callback(
            '/(?:^|\s)(Image not found)(?=\s|$)/m',
            function($matches) {
                return $this->createImagePlaceholder('center');
            },
            $content
        );
        
        // Pattern 9: Handle malformed placeholder structures from AI generation
        $content = preg_replace_callback(
            '/<span[^>]*class="[^"]*placeholder-text[^"]*"[^>]*>([^<]*)<\/span>\s*<\/div>\s*<\/div>\s*<div[^>]*class="[^"]*adjacent-text-content[^"]*"[^>]*>/i',
            function($matches) {
                $altText = trim($matches[1]);
                return $this->createImagePlaceholderWithAlt('left', $altText) . '<div class="clearfix"></div>';
            },
            $content
        );
        
        // Pattern 10: Handle standalone placeholder text that appears between content
        $content = preg_replace_callback(
            '/\s*<span[^>]*class="[^"]*placeholder-text[^"]*"[^>]*>([^<]*)<\/span>\s*/i',
            function($matches) {
                $altText = trim($matches[1]);
                if ($altText === 'Image not found' || empty($altText)) {
                    return $this->createImagePlaceholder('center');
                }
                return $this->createImagePlaceholderWithAlt('center', $altText);
            },
            $content
        );
        
        // Pattern 11: Clean up any remaining malformed structures
        $content = preg_replace('/\s*<\/div>\s*<\/div>\s*<div[^>]*class="[^"]*adjacent-text-content[^"]*"[^>]*>/', '<div class="clearfix"></div>', $content);
        
        // Pattern 7: Process gallery layouts
        $content = $this->processGalleryLayouts($content);
        
        return $content;
    }

    /**
     * Process gallery layouts specifically
     */
    private function processGalleryLayouts(string $content): string
    {
        // Process 2-item galleries
        $content = preg_replace_callback(
            '/<div[^>]*class="[^"]*ai-image-layout-gallery-2-item[^"]*"[^>]*>(.*?)<\/div>/s',
            function($matches) {
                return $this->createGalleryLayout('gallery-2-item', $matches[1]);
            },
            $content
        );
        
        // Process 3-item galleries
        $content = preg_replace_callback(
            '/<div[^>]*class="[^"]*ai-image-layout-gallery-3-item[^"]*"[^>]*>(.*?)<\/div>/s',
            function($matches) {
                return $this->createGalleryLayout('gallery-3-item', $matches[1]);
            },
            $content
        );
        
        // Process overlay layouts
        $content = preg_replace_callback(
            '/<div[^>]*class="[^"]*ai-image-layout-overlay[^"]*"[^>]*>(.*?)<\/div>/s',
            function($matches) {
                return $this->createGalleryLayout('overlay', $matches[1]);
            },
            $content
        );
        
        return $content;
    }

    /**
     * Create gallery layout using AI editor structure
     */
    private function createGalleryLayout(string $layout, string $content): string
    {
        // Extract images from the gallery content with media IDs
        preg_match_all('/<img[^>]*src="([^"]*)"[^>]*(?:alt="([^"]*)")?[^>]*(?:data-media-id="([^"]*)")?[^>]*>/i', $content, $images, PREG_SET_ORDER);
        
        if (empty($images)) {
            // No images found, create placeholder gallery
            return $this->createGalleryPlaceholder($layout);
        }
        
        $galleryHtml = '<div class="ai-image-gallery ai-image-layout-' . htmlspecialchars($layout) . '">';
        
        foreach ($images as $img) {
            $src = htmlspecialchars($img[1]);
            $alt = htmlspecialchars($img[2] ?? '');
            $mediaId = htmlspecialchars($img[3] ?? '');
            
            $dataAttr = !empty($mediaId) ? ' data-media-id="' . $mediaId . '"' : '';
            
            $galleryHtml .= '<div class="ai-image-placeholder ai-image-layout-' . htmlspecialchars($layout) . '"' . $dataAttr . '>
                <div class="ai-image-placeholder-inner">
                    <img src="' . $src . '" alt="' . $alt . '" loading="lazy" class="generated-image">
                </div>
            </div>';
        }
        
        $galleryHtml .= '</div>';
        
        return $galleryHtml;
    }

    /**
     * Create gallery placeholder using AI editor structure
     */
    private function createGalleryPlaceholder(string $layout): string
    {
        $layout = htmlspecialchars($layout);
        $itemCount = 2; // default
        
        if (strpos($layout, '3-item') !== false) {
            $itemCount = 3;
        } elseif (strpos($layout, '2-item') !== false) {
            $itemCount = 2;
        }
        
        $galleryHtml = '<div class="ai-image-gallery ai-image-layout-' . $layout . '">';
        
        for ($i = 0; $i < $itemCount; $i++) {
            $galleryHtml .= '<div class="ai-image-placeholder ai-image-layout-' . $layout . '">
                <div class="ai-image-placeholder-inner">
                    <div class="placeholder-content">
                        <span class="placeholder-icon">🖼️</span>
                        <span class="placeholder-text">Image not found</span>
                    </div>
                </div>
            </div>';
        }
        
        $galleryHtml .= '</div>';
        
        return $galleryHtml;
    }

    /**
     * Process individual image placeholder
     */
    private function processImagePlaceholder($matches): string
    {
        $fullMatch = $matches[0];
        $layout = $matches[1] ?? 'center';
        $innerContent = $matches[2];
        
        // Normalize layout names
        $layout = $this->normalizeLayoutName($layout);
        
        // Extract media ID from data attributes if present
        $mediaId = '';
        if (preg_match('/data-media-id="([^"]*)"/', $fullMatch, $mediaMatches)) {
            $mediaId = $mediaMatches[1];
        }
        
        // Extract image from inner content
        if (preg_match('/<img[^>]*src="([^"]*)"[^>]*(?:alt="([^"]*)")?[^>]*>/i', $innerContent, $imgMatches)) {
            $src = $imgMatches[1];
            $alt = $imgMatches[2] ?? '';
            
            // Handle special layouts with additional content
            if ($layout === 'overlay') {
                return $this->createTextOverlayLayout($src, $alt, $innerContent, $mediaId);
            } elseif ($layout === 'left' || $layout === 'right') {
                return $this->createFloatedLayout($src, $alt, $layout, $innerContent, $mediaId);
            }
            
            return $this->createImageFigure($src, $alt, $layout, $mediaId);
        }
        
        // Look for alt text in the content even if no image
        $altText = $this->extractAltTextFromContent($innerContent);
        
        // Handle special layouts without images
        if ($layout === 'overlay') {
            return $this->createTextOverlayPlaceholder($layout, $altText, $innerContent);
        } elseif ($layout === 'left' || $layout === 'right') {
            return $this->createFloatedPlaceholder($layout, $altText, $innerContent);
        }
        
        // Return placeholder with alt text if available
        if (!empty($altText)) {
            return $this->createImagePlaceholderWithAlt($layout, $altText);
        }
        
        // No image found - return basic placeholder
        return $this->createImagePlaceholder($layout);
    }

    /**
     * Normalize layout names to match CSS classes
     */
    private function normalizeLayoutName(string $layout): string
    {
        $layoutMap = [
            'default' => 'center',
            'centered' => 'center',
            'left-align' => 'left',
            'right-align' => 'right',
            'full-width' => 'full',
            'wide-width' => 'wide',
            'square-crop' => 'square',
            'gallery-2' => 'gallery-2-item',
            'gallery-3' => 'gallery-3-item',
            'gallery-4' => 'gallery-4-item',
            'overlay-text' => 'overlay',
            'text_overlay' => 'overlay'
        ];
        
        return $layoutMap[$layout] ?? $layout;
    }

    /**
     * Process image container
     */
    private function processImageContainer($matches): string
    {
        $fullMatch = $matches[0];
        $innerContent = $matches[1];
        
        // Extract media ID from container
        $mediaId = '';
        if (preg_match('/data-media-id="([^"]*)"/', $fullMatch, $mediaMatches)) {
            $mediaId = $mediaMatches[1];
        }
        
        // Try to detect layout from container classes or data-layout attribute
        $layout = 'center';
        if (preg_match('/data-layout="([^"]*)"/', $fullMatch, $layoutMatches)) {
            $layout = $this->normalizeLayoutName($layoutMatches[1]);
        } elseif (preg_match('/class="[^"]*ai-image-layout-([^"\s]+)/', $fullMatch, $layoutMatches)) {
            $layout = $this->normalizeLayoutName($layoutMatches[1]);
        } elseif (preg_match('/class="[^"]*image-text-wrapper[^"]*"[^>]*data-layout="([^"]*)"/', $fullMatch, $layoutMatches)) {
            $layout = $this->normalizeLayoutName($layoutMatches[1]);
        }
        
        // Extract image from container
        if (preg_match('/<img[^>]*src="([^"]*)"[^>]*(?:alt="([^"]*)")?[^>]*>/i', $innerContent, $imgMatches)) {
            $src = $imgMatches[1];
            $alt = $imgMatches[2] ?? '';
            
            // Handle special layouts with additional content
            if ($layout === 'overlay') {
                return $this->createTextOverlayLayout($src, $alt, $innerContent, $mediaId);
            } elseif ($layout === 'left' || $layout === 'right') {
                return $this->createFloatedLayout($src, $alt, $layout, $innerContent, $mediaId);
            }
            
            return $this->createImageFigure($src, $alt, $layout, $mediaId);
        }
        
        // Handle containers without images but with layout info
        if ($layout === 'overlay') {
            return $this->createTextOverlayPlaceholder($layout, '', $innerContent);
        } elseif ($layout === 'left' || $layout === 'right') {
            return $this->createFloatedPlaceholder($layout, '', $innerContent);
        }
        
        return $this->createImagePlaceholder($layout);
    }

    /**
     * Wrap standalone image
     */
    private function wrapStandaloneImage($matches): string
    {
        $imgTag = $matches[0];
        
        // Extract src and alt from img tag
        preg_match('/src="([^"]*)"/', $imgTag, $srcMatches);
        preg_match('/alt="([^"]*)"/', $imgTag, $altMatches);
        preg_match('/data-media-id="([^"]*)"/', $imgTag, $mediaMatches);
        
        $src = $srcMatches[1] ?? '';
        $alt = $altMatches[1] ?? '';
        $mediaId = $mediaMatches[1] ?? '';
        
        if (!empty($src)) {
            return $this->createImageFigure($src, $alt, 'center', $mediaId);
        }
        
        return $imgTag;
    }

    /**
     * Create properly formatted image figure using AI editor class structure
     */
    private function createImageFigure(string $src, string $alt, string $layout, string $mediaId = ''): string
    {
        $layout = htmlspecialchars($layout);
        $src = htmlspecialchars($src);
        $alt = htmlspecialchars($alt);
        $mediaId = htmlspecialchars($mediaId);
        
        $dataAttr = !empty($mediaId) ? ' data-media-id="' . $mediaId . '"' : '';
        
        // Use AI editor class structure for consistency
        return '<div class="ai-image-placeholder ai-image-layout-' . $layout . '"' . $dataAttr . '>
            <div class="ai-image-placeholder-inner">
                <img src="' . $src . '" alt="' . $alt . '" loading="lazy" class="generated-image">
            </div>
        </div>';
    }

    /**
     * Create image placeholder for missing images using AI editor class structure
     */
    private function createImagePlaceholder(string $layout): string
    {
        $layout = htmlspecialchars($layout);
        
        return '<div class="ai-image-placeholder ai-image-layout-' . $layout . '">
            <div class="ai-image-placeholder-inner">
                <div class="placeholder-content">
                    <span class="placeholder-icon">🖼️</span>
                    <span class="placeholder-text">Image not found</span>
                </div>
            </div>
        </div>';
    }

    /**
     * Create image placeholder with alt text description using AI editor class structure
     */
    private function createImagePlaceholderWithAlt(string $layout, string $altText): string
    {
        $layout = htmlspecialchars($layout);
        $altText = htmlspecialchars($altText);
        
        return '<div class="ai-image-placeholder ai-image-layout-' . $layout . '">
            <div class="ai-image-placeholder-inner">
                <div class="placeholder-content">
                    <span class="placeholder-icon">🖼️</span>
                    <span class="placeholder-text">Image not found</span>
                    <span class="placeholder-alt">' . $altText . '</span>
                </div>
            </div>
        </div>';
    }

    /**
     * Extract alt text from various content formats
     */
    private function extractAltTextFromContent(string $content): string
    {
        // Try different methods to extract meaningful alt text
        if (preg_match('/alt="([^"]*)"/', $content, $altMatches)) {
            return $altMatches[1];
        } elseif (preg_match('/<p[^>]*>([^<]+)<\/p>/', $content, $textMatches)) {
            return trim(strip_tags($textMatches[1]));
        } elseif (preg_match('/data-description="([^"]*)"/', $content, $descMatches)) {
            return $descMatches[1];
        } elseif (preg_match('/<div[^>]*class="[^"]*ai-image-description[^"]*"[^>]*>([^<]+)<\/div>/', $content, $descMatches)) {
            return trim(strip_tags($descMatches[1]));
        } elseif (preg_match('/>\s*([^<]+)\s*</', $content, $textMatches)) {
            $text = trim(strip_tags($textMatches[1]));
            // Only return if it's meaningful (not just whitespace or common phrases)
            if (strlen($text) > 5 && !in_array(strtolower($text), ['upload image', 'show description', 'manual image'])) {
                return $text;
            }
        }
        
        return '';
    }

    /**
     * Create text overlay layout with image and overlay content using AI editor structure
     */
    private function createTextOverlayLayout(string $src, string $alt, string $innerContent, string $mediaId = ''): string
    {
        $src = htmlspecialchars($src);
        $alt = htmlspecialchars($alt);
        $mediaId = htmlspecialchars($mediaId);
        
        // Extract overlay content from .image-text-overlay-content or data-text-overlay
        $overlayContent = $this->extractOverlayContent($innerContent);
        
        $dataAttr = !empty($mediaId) ? ' data-media-id="' . $mediaId . '"' : '';
        
        $html = '<div class="ai-image-placeholder ai-image-layout-text_overlay"' . $dataAttr . '>';
        $html .= '<div class="ai-image-placeholder-inner">';
        $html .= '<img src="' . $src . '" alt="' . $alt . '" loading="lazy" class="generated-image">';
        
        if (!empty($overlayContent)) {
            $html .= '<div class="image-text-overlay-content">' . $overlayContent . '</div>';
        }
        
        $html .= '</div>';
        $html .= '</div>';
        
        return $html;
    }

    /**
     * Create floated layout with image and adjacent content using AI editor structure
     */
    private function createFloatedLayout(string $src, string $alt, string $layout, string $innerContent, string $mediaId = ''): string
    {
        $src = htmlspecialchars($src);
        $alt = htmlspecialchars($alt);
        $layout = htmlspecialchars($layout);
        $mediaId = htmlspecialchars($mediaId);
        
        // Extract adjacent content from .adjacent-text-content or data-adjacent-content
        $adjacentContent = $this->extractAdjacentContent($innerContent);
        
        $dataAttr = !empty($mediaId) ? ' data-media-id="' . $mediaId . '"' : '';
        
        if (!empty($adjacentContent)) {
            // Create wrapper with image and adjacent content using AI editor structure
            $html = '<div class="image-text-wrapper" data-layout="' . $layout . '"' . $dataAttr . '>';
            $html .= '<div class="ai-image-placeholder ai-image-layout-' . $layout . '">';
            $html .= '<div class="ai-image-placeholder-inner">';
            $html .= '<img src="' . $src . '" alt="' . $alt . '" loading="lazy" class="generated-image">';
            $html .= '</div>';
            $html .= '</div>';
            $html .= '<div class="adjacent-text-content">' . $adjacentContent . '</div>';
            $html .= '</div>';
            
            return $html;
        } else {
            // Just the floated image
            return $this->createImageFigure($src, $alt, $layout, $mediaId);
        }
    }

    /**
     * Create text overlay placeholder using AI editor structure
     */
    private function createTextOverlayPlaceholder(string $layout, string $altText, string $innerContent): string
    {
        $layout = htmlspecialchars($layout);
        $altText = htmlspecialchars($altText);
        
        $overlayContent = $this->extractOverlayContent($innerContent);
        
        $html = '<div class="ai-image-placeholder ai-image-layout-text_overlay">';
        $html .= '<div class="ai-image-placeholder-inner">';
        $html .= '<div class="placeholder-content">';
        $html .= '<span class="placeholder-icon">🖼️</span>';
        $html .= '<span class="placeholder-text">Image not found</span>';
        if (!empty($altText)) {
            $html .= '<span class="placeholder-alt">' . $altText . '</span>';
        }
        $html .= '</div>';
        
        if (!empty($overlayContent)) {
            $html .= '<div class="image-text-overlay-content">' . $overlayContent . '</div>';
        }
        
        $html .= '</div>';
        $html .= '</div>';
        
        return $html;
    }

    /**
     * Create floated placeholder using AI editor structure
     */
    private function createFloatedPlaceholder(string $layout, string $altText, string $innerContent): string
    {
        $layout = htmlspecialchars($layout);
        $altText = htmlspecialchars($altText);
        
        $adjacentContent = $this->extractAdjacentContent($innerContent);
        
        if (!empty($adjacentContent)) {
            $html = '<div class="image-text-wrapper" data-layout="' . $layout . '">';
            $html .= '<div class="ai-image-placeholder ai-image-layout-' . $layout . '">';
            $html .= '<div class="ai-image-placeholder-inner">';
            $html .= '<div class="placeholder-content">';
            $html .= '<span class="placeholder-icon">🖼️</span>';
            $html .= '<span class="placeholder-text">Image not found</span>';
            if (!empty($altText)) {
                $html .= '<span class="placeholder-alt">' . $altText . '</span>';
            }
            $html .= '</div>';
            $html .= '</div>';
            $html .= '</div>';
            $html .= '<div class="adjacent-text-content">' . $adjacentContent . '</div>';
            $html .= '</div>';
            
            return $html;
        } else {
            return $this->createImagePlaceholderWithAlt($layout, $altText);
        }
    }

    /**
     * Extract overlay content from inner HTML
     */
    private function extractOverlayContent(string $innerContent): string
    {
        // Look for .image-text-overlay-content
        if (preg_match('/<div[^>]*class="[^"]*image-text-overlay-content[^"]*"[^>]*>(.*?)<\/div>/s', $innerContent, $matches)) {
            return $this->sanitizeContent($matches[1]);
        }
        
        // Look for data-text-overlay attribute
        if (preg_match('/data-text-overlay="([^"]*)"/', $innerContent, $matches)) {
            return $this->sanitizeContent(html_entity_decode($matches[1]));
        }
        
        return '';
    }

    /**
     * Extract adjacent content from inner HTML
     */
    private function extractAdjacentContent(string $innerContent): string
    {
        // Look for .adjacent-text-content
        if (preg_match('/<div[^>]*class="[^"]*adjacent-text-content[^"]*"[^>]*>(.*?)<\/div>/s', $innerContent, $matches)) {
            return $this->sanitizeContent($matches[1]);
        }
        
        // Look for data-adjacent-content attribute
        if (preg_match('/data-adjacent-content="([^"]*)"/', $innerContent, $matches)) {
            return $this->sanitizeContent(html_entity_decode($matches[1]));
        }
        
        return '';
    }

    /**
     * Process floated container (image-text-wrapper)
     */
    private function processFloatedContainer($matches): string
    {
        $layout = $matches[1];
        $innerContent = $matches[2];
        
        // Normalize layout
        $layout = $this->normalizeLayoutName($layout);
        
        // Extract image placeholder and adjacent content
        $imagePlaceholder = '';
        $adjacentContent = '';
        
        // Look for image placeholder
        if (preg_match('/<div[^>]*class="[^"]*post-image-placeholder[^"]*"[^>]*>(.*?)<\/div>/s', $innerContent, $imgMatches)) {
            $imagePlaceholder = $imgMatches[0];
            // Fix the layout class in the placeholder
            $imagePlaceholder = preg_replace('/post-image-"/', "post-image-{$layout}\"", $imagePlaceholder);
        }
        
        // Look for adjacent content
        if (preg_match('/<div[^>]*class="[^"]*adjacent-text-content[^"]*"[^>]*>(.*?)<\/div>/s', $innerContent, $adjMatches)) {
            $adjacentContent = $this->sanitizeContent($adjMatches[1]);
        }
        
        // Create proper floated layout
        if (!empty($imagePlaceholder) && !empty($adjacentContent)) {
            return '<div class="post-image-float-wrapper post-image-float-' . $layout . '">' .
                   $imagePlaceholder .
                   '<div class="post-image-adjacent-content">' . $adjacentContent . '</div>' .
                   '<div class="clearfix"></div>' .
                   '</div>';
        } elseif (!empty($imagePlaceholder)) {
            return $imagePlaceholder;
        }
        
        return '';
    }

    /**
     * Fix broken gallery structure
     */
    private function fixGalleryStructure($matches): string
    {
        $itemCount = $matches[1];
        $innerContent = $matches[2];
        
        // Extract all gallery placeholders
        preg_match_all('/<div[^>]*class="[^"]*gallery-placeholder[^"]*"[^>]*>(.*?)<\/div>/s', $innerContent, $placeholders, PREG_SET_ORDER);
        
        $galleryHtml = '<div class="post-image-gallery post-image-gallery-' . $itemCount . '-item">';
        
        foreach ($placeholders as $placeholder) {
            $galleryHtml .= '<figure class="gallery-item">' . $placeholder[0] . '</figure>';
        }
        
        $galleryHtml .= '</div>';
        
        return $galleryHtml;
    }
    
    /**
     * Fix placeholder with missing layout class using AI editor structure
     */
    private function fixPlaceholderLayout($matches): string
    {
        $innerContent = $matches[1];
        
        // Default to standard layout for standalone placeholders
        return '<div class="ai-image-placeholder ai-image-layout-standard">
            <div class="ai-image-placeholder-inner">' . $innerContent . '</div>
        </div>';
    }

    /**
     * Ensure proper content structure and prevent content from breaking out of containers
     */
    private function ensureProperContentStructure(string $content): string
    {
        // Remove any content that appears after closing tags that would break structure
        // Pattern: content that appears after </div></div> which indicates broken nesting
        $content = preg_replace('/(<\/div>\s*<\/div>)\s*(<div class="(?:post-image-placeholder|ai-image-placeholder).*?<\/div>)/s', '$1', $content);
        
        // Fix pattern where content breaks out after </div><p></p>
        $content = preg_replace('/(<\/div>)(<p><\/p>)/', '$1', $content);
        
        // Fix AI image placeholders that might break container structure
        $content = preg_replace('/(<\/div>\s*<\/div>)\s*(<div class="ai-image-placeholder)/s', '$1', $content);
        
        // Ensure any floating image elements are properly contained
        // Move any image divs that appear at the end back into the content flow
        if (preg_match_all('/<div class="(?:post-image-placeholder|ai-image-placeholder)[^>]*>.*?<\/div>/s', $content, $matches, PREG_OFFSET_CAPTURE)) {
            $lastMatch = end($matches[0]);
            $lastImagePos = $lastMatch[1] + strlen($lastMatch[0]);
            
            // If there's significant content after the last image, it might be misplaced
            $remainingContent = substr($content, $lastImagePos);
            if (strlen(trim($remainingContent)) > 50 && !preg_match('/<\/div>\s*$/', $remainingContent)) {
                // Content is likely misplaced, need to restructure
                $beforeImages = substr($content, 0, $lastMatch[1]);
                $imageContent = $lastMatch[0];
                $afterContent = substr($content, $lastImagePos);
                
                // Clean up the after content and ensure it's properly structured
                $afterContent = trim($afterContent);
                if (!empty($afterContent) && !preg_match('/^<\//', $afterContent)) {
                    // Insert the image before the after content
                    $content = $beforeImages . $imageContent . "\n\n" . $afterContent;
                }
            }
        }
        
        // Ensure proper paragraph structure around images
        $content = preg_replace('/(<\/h[1-6]>)\s*(<div class="(?:post-image|ai-image))/', '$1' . "\n\n" . '$2', $content);
        $content = preg_replace('/(<\/div>)\s*(<h[1-6]>)/', '$1' . "\n\n" . '$2', $content);
        
        // Prevent AI image containers from breaking out of post-content-inner
        $content = preg_replace('/(<\/article>.*?)(<div class="ai-image-placeholder.*?<\/div>)/s', '$1', $content);
        
        return $content;
    }

    /**
     * Sanitize content for safe output
     */
    private function sanitizeContent(string $content): string
    {
        // Remove dangerous elements
        $content = preg_replace('/<script[^>]*>.*?<\/script>/is', '', $content);
        $content = preg_replace('/<iframe[^>]*>.*?<\/iframe>/is', '', $content);
        $content = preg_replace('/on\w+="[^"]*"/i', '', $content); // Remove event handlers
        
        // Clean up whitespace
        $content = trim($content);
        
        return $content;
    }

    /**
     * Clean up malformed placeholder text and structures
     */
    private function preSanitizeAiPlaceholders(string $content): string
    {
        // Collapse repeated nested ai-image-placeholder blocks that appear inside placeholder-text
        $content = preg_replace('/<span[^>]*class="[^"]*placeholder-text[^"]*"[^>]*>\s*(<div[^>]*class="[^"]*ai-image-placeholder[^"]*"[^>]*>.*?<\/div>)+\s*<\/span>/is', '<span class="placeholder-text">Image not found</span>', $content);

        // Remove repeated nested inner wrappers
        $content = preg_replace('/(<div[^>]*class="[^"]*ai-image-placeholder-inner[^"]*"[^>]*>\s*){2,}/is', '<div class="ai-image-placeholder-inner">', $content);

        // Ensure closing tags where deeply nested structures broke
        $content = preg_replace('/(<div[^>]*class="[^"]*ai-image-placeholder[^"]*"[^>]*>)(?!.*?<\/div>)/is', '$1</div>', $content);

        return $content;
    }

    private function cleanupMalformedPlaceholders(string $content): string
    {
        // Handle the specific pattern from your HTML: standalone placeholder text spans
        $content = preg_replace_callback(
            '/\s*<span[^>]*class="[^"]*placeholder-text[^"]*"[^>]*>([^<]*)<\/span>\s*/i',
            function($matches) {
                $altText = trim($matches[1]);
                if ($altText === 'Image not found' || empty($altText)) {
                    return "\n\n" . $this->createImagePlaceholder('center') . "\n\n";
                }
                return "\n\n" . $this->createImagePlaceholderWithAlt('center', $altText) . "\n\n";
            },
            $content
        );
        
        // Handle bare "Image not found" text that appears in content
        $content = preg_replace_callback(
            '/(?<=>|\s)Image not found(?=<|\s|$)/i',
            function($matches) {
                return $this->createImagePlaceholder('center');
            },
            $content
        );
        
        // Handle malformed adjacent-text-content structures that contain image descriptions
        $content = preg_replace_callback(
            '/<div[^>]*class="[^"]*adjacent-text-content[^"]*"[^>]*>(.*?)<\/div>/is',
            function($matches) {
                $textContent = trim($matches[1]);
                
                // Check if this contains image-related content that should be a placeholder
                if (stripos($textContent, 'Image not found') !== false || 
                    stripos($textContent, 'placeholder') !== false ||
                    stripos($textContent, 'Demonstrates a left‑aligned image') !== false) {
                    
                    // Extract any meaningful description
                    if (preg_match('/<description[^>]*>(.*?)<\/description>/is', $textContent, $descMatches)) {
                        $description = strip_tags($descMatches[1]);
                        return $this->createImagePlaceholderWithAlt('left', $description);
                    }
                    
                    return $this->createImagePlaceholder('center');
                }
                
                // Otherwise, keep as regular content but clean it up
                $cleanContent = strip_tags($textContent, '<p><br><strong><em><ul><li><ol>');
                if (!empty(trim($cleanContent))) {
                    return '<div class="content-section">' . $cleanContent . '</div>';
                }
                
                return '';
            },
            $content
        );
        
        // Clean up any remaining malformed div structures
        $content = preg_replace('/<\/div>\s*<\/div>\s*<div[^>]*class="[^"]*adjacent-text-content[^"]*"[^>]*>/', '', $content);
        
        // Remove empty placeholder spans and other artifacts
        $content = preg_replace('/<span[^>]*class="[^"]*placeholder-[^"]*"[^>]*>\s*<\/span>/i', '', $content);
        $content = preg_replace('/<description[^>]*class="[^"]*clearfix-after[^"]*"[^>]*>.*?<\/description>/is', '', $content);
        
        return $content;
    }

    /**
     * Final content cleanup
     */
    private function cleanupContent(string $content): string
    {
        // Fix unclosed div tags and broken HTML structure
        $content = $this->fixHtmlStructure($content);
        
        // Ensure all content is properly wrapped and structured
        $content = $this->ensureProperContentStructure($content);
        
        // Remove only truly empty paragraphs (no content, no images, no placeholders)
        $content = preg_replace('/<p[^>]*>\s*<\/p>/i', '', $content);
        
        // Remove only empty divs that don't have important classes
        $content = preg_replace('/<div(?![^>]*class="[^"]*(?:post-image|placeholder)[^"]*")[^>]*>\s*<\/div>/i', '', $content);
        
        // Clean up excessive whitespace but preserve line breaks in content
        $content = preg_replace('/[ \t]+/', ' ', $content);
        $content = preg_replace('/\n\s*\n\s*\n/', "\n\n", $content);
        
        // Ensure proper spacing between block elements
        $content = preg_replace('/(<\/p>)(<figure)/', '$1' . "\n\n" . '$2', $content);
        $content = preg_replace('/(<\/figure>)(<p)/', '$1' . "\n\n" . '$2', $content);
        $content = preg_replace('/(<\/p>)(<div class="post-image)/', '$1' . "\n\n" . '$2', $content);
        $content = preg_replace('/(<\/div>)(<p)/', '$1' . "\n\n" . '$2', $content);
        
        return trim($content);
    }

    /**
     * Fix broken HTML structure and unclosed tags
     */
    private function normalizeAiLayoutsWithDom(string $html): string
    {
        // Use DOMDocument to correct malformed nested placeholders and ensure proper structures
        libxml_use_internal_errors(true);
        $doc = new \DOMDocument();
        $wrapped = '<!DOCTYPE html><html><body><div id="__aiwrap">' . $html . '</div></body></html>';
        $doc->loadHTML($wrapped, LIBXML_HTML_NOIMPLIED | LIBXML_HTML_NODEFDTD | LIBXML_NOWARNING | LIBXML_NOERROR);
        $xpath = new \DOMXPath($doc);
        $root = $doc->getElementById('__aiwrap');
        if (!$root) return $html;

        // 1) Flatten nested ai-image-placeholder inside placeholder-text
        foreach ($xpath->query(".//span[contains(concat(' ', normalize-space(@class), ' '), ' placeholder-text ')]//div[contains(concat(' ', normalize-space(@class), ' '), ' ai-image-placeholder ')]") as $nested) {
            $parentText = $nested->parentNode;
            if ($parentText && $parentText->nodeName === 'span') {
                // Replace contents of placeholder-text with text node
                while ($parentText->firstChild) { $parentText->removeChild($parentText->firstChild); }
                $parentText->appendChild($doc->createTextNode('Image not found'));
            }
        }

        // 2) Ensure inner wrapper exists for each ai-image-placeholder
        foreach ($xpath->query(".//div[contains(concat(' ', normalize-space(@class), ' '), ' ai-image-placeholder ')]") as $ph) {
            $hasInner = false;
            foreach ($ph->childNodes as $child) {
                if ($child->nodeType === XML_ELEMENT_NODE && $child->nodeName === 'div' && strpos(' ' . $child->getAttribute('class') . ' ', ' ai-image-placeholder-inner ') !== false) {
                    $hasInner = true; break;
                }
            }
            if (!$hasInner) {
                $inner = $doc->createElement('div');
                $inner->setAttribute('class', 'ai-image-placeholder-inner');
                while ($ph->firstChild) { $inner->appendChild($ph->firstChild); }
                $ph->appendChild($inner);
            } else {
                // Flatten multiple nested "ai-image-placeholder-inner" wrappers into one
                $inners = [];
                foreach ($ph->getElementsByTagName('div') as $d) {
                    if (strpos(' ' . $d->getAttribute('class') . ' ', ' ai-image-placeholder-inner ') !== false) {
                        $inners[] = $d;
                    }
                }
                if (count($inners) > 1) {
                    $primary = array_shift($inners);
                    foreach ($inners as $extra) {
                        while ($extra->firstChild) { $primary->appendChild($extra->firstChild); }
                        $extra->parentNode && $extra->parentNode->removeChild($extra);
                    }
                }
            }
        }

        // 2b) For nested placeholders, move their inner content after the outer placeholder, then remove nested node
        foreach ($xpath->query(".//div[contains(concat(' ', normalize-space(@class), ' '), ' ai-image-placeholder ')][ancestor::div[contains(concat(' ', normalize-space(@class), ' '), ' ai-image-placeholder ')]]") as $nestedPh) {
            // Find the nearest ancestor that is an ai-image-placeholder but not nested inside another (outer)
            $outer = $nestedPh->parentNode;
            while ($outer && !($outer->nodeType === XML_ELEMENT_NODE && $outer->nodeName === 'div' && strpos(' ' . $outer->getAttribute('class') . ' ', ' ai-image-placeholder ') !== false)) {
                $outer = $outer->parentNode;
            }
            // climb one more to ensure we place after the highest-level placeholder in this chain
            $highest = $outer;
            while ($highest && $highest->parentNode && $highest->parentNode->nodeType === XML_ELEMENT_NODE && $highest->parentNode->nodeName === 'div' && strpos(' ' . $highest->parentNode->getAttribute('class') . ' ', ' ai-image-placeholder ') !== false) {
                $highest = $highest->parentNode;
            }
            if ($highest) {
                $frag = $doc->createDocumentFragment();
                while ($nestedPh->firstChild) { $frag->appendChild($nestedPh->firstChild); }
                // Insert fragment after the highest outer placeholder
                if ($highest->nextSibling) {
                    $highest->parentNode->insertBefore($frag, $highest->nextSibling);
                } else {
                    $highest->parentNode->appendChild($frag);
                }
            }
            // Remove the nested placeholder container itself
            if ($nestedPh->parentNode) { $nestedPh->parentNode->removeChild($nestedPh); }
        }

        // 2c) For each inner wrapper, move stray non-allowed children outside (preserve following content)
        foreach ($xpath->query(".//div[contains(concat(' ', normalize-space(@class), ' '), ' ai-image-placeholder-inner ')]") as $inner) {
            $allowed = ['placeholder-content','image-text-overlay-content'];
            $toMoveOut = [];
            foreach (iterator_to_array($inner->childNodes) as $child) {
                if ($child->nodeType === XML_TEXT_NODE) {
                    if (trim($child->nodeValue) === '') continue;
                    $toMoveOut[] = $child;
                    continue;
                }
                if ($child->nodeType === XML_ELEMENT_NODE) {
                    $cls = ' ' . $child->getAttribute('class') . ' ';
                    // Keep actual image and overlay content
                    if ($child->nodeName === 'img' && strpos($child->getAttribute('class'), 'generated-image') !== false) continue;
                    $isAllowed = false;
                    foreach ($allowed as $ac) { if (strpos($cls, ' ' . $ac . ' ') !== false) { $isAllowed = true; break; } }
                    if ($isAllowed) continue;
                    $toMoveOut[] = $child;
                }
            }
            if (!empty($toMoveOut)) {
                $ph = $inner->parentNode; // ai-image-placeholder
                foreach ($toMoveOut as $n) {
                    $after = $ph->nextSibling;
                    if ($after) {
                        $ph->parentNode->insertBefore($n, $after);
                    } else {
                        $ph->parentNode->appendChild($n);
                    }
                }
            }
        }

        // 2d) Ensure placeholder-content and placeholder-text exist for image-less placeholders
        foreach ($xpath->query(".//div[contains(concat(' ', normalize-space(@class), ' '), ' ai-image-placeholder ')]") as $ph) {
            // Find inner
            $inner = null;
            foreach ($ph->childNodes as $child) {
                if ($child->nodeType === XML_ELEMENT_NODE && $child->nodeName === 'div' && strpos(' ' . $child->getAttribute('class') . ' ', ' ai-image-placeholder-inner ') !== false) {
                    $inner = $child; break;
                }
            }
            if (!$inner) continue;

            $hasImg = false; $hasContent = false; $hasText = false;
            foreach ($inner->childNodes as $c) {
                if ($c->nodeType === XML_ELEMENT_NODE && $c->nodeName === 'img' && strpos(' ' . $c->getAttribute('class') . ' ', ' generated-image ') !== false) {
                    $hasImg = true;
                }
                if ($c->nodeType === XML_ELEMENT_NODE && $c->nodeName === 'div' && strpos(' ' . $c->getAttribute('class') . ' ', ' placeholder-content ') !== false) {
                    $hasContent = true;
                    // Check for placeholder-text inside
                    foreach ($c->childNodes as $cc) {
                        if ($cc->nodeType === XML_ELEMENT_NODE && $cc->nodeName === 'span' && strpos(' ' . $cc->getAttribute('class') . ' ', ' placeholder-text ') !== false) {
                            $hasText = true; break;
                        }
                    }
                }
            }
            if (!$hasImg) {
                // Ensure placeholder-content
                $contentDiv = null;
                if (!$hasContent) {
                    $contentDiv = $doc->createElement('div');
                    $contentDiv->setAttribute('class', 'placeholder-content');
                    $inner->appendChild($contentDiv);
                } else {
                    // Find existing contentDiv
                    foreach ($inner->childNodes as $c) {
                        if ($c->nodeType === XML_ELEMENT_NODE && $c->nodeName === 'div' && strpos(' ' . $c->getAttribute('class') . ' ', ' placeholder-content ') !== false) { $contentDiv = $c; break; }
                    }
                }
                if ($contentDiv && !$hasText) {
                    // Add icon if missing
                    $hasIcon = false;
                    foreach ($contentDiv->childNodes as $cc) {
                        if ($cc->nodeType === XML_ELEMENT_NODE && $cc->nodeName === 'span' && strpos(' ' . $cc->getAttribute('class') . ' ', ' placeholder-icon ') !== false) { $hasIcon = true; break; }
                    }
                    if (!$hasIcon) {
                        $icon = $doc->createElement('span', '\u{1F5BC}'); // 🖼️
                        $icon->setAttribute('class', 'placeholder-icon');
                        $contentDiv->appendChild($icon);
                    }
                    $txt = $doc->createElement('span', 'Image not found');
                    $txt->setAttribute('class', 'placeholder-text');
                    $contentDiv->appendChild($txt);
                }
            }
        }

        // 3) Rebuild floated wrappers if needed: wrap left/right placeholders with image-text-wrapper and adjacent content
        foreach ($xpath->query(".//div[contains(concat(' ', normalize-space(@class), ' '), ' ai-image-placeholder ')][contains(concat(' ', normalize-space(@class), ' '), ' ai-image-layout-left ') or contains(concat(' ', normalize-space(@class), ' '), ' ai-image-layout-right ')]") as $ph) {
            if ($ph->parentNode && strpos(' ' . $ph->parentNode->getAttribute('class') . ' ', ' image-text-wrapper ') !== false) continue;
            $wrapper = $doc->createElement('div');
            $wrapper->setAttribute('class', 'image-text-wrapper');
            $wrapper->setAttribute('data-layout', (strpos(' ' . $ph->getAttribute('class') . ' ', ' ai-image-layout-right ') !== false) ? 'right' : 'left');

            $adj = $doc->createElement('div');
            $adj->setAttribute('class', 'adjacent-text-content');

            // Move following siblings that are not image placeholders/galleries/headings into adjacent
            $sibling = $ph->nextSibling;
            while ($sibling) {
                $next = $sibling->nextSibling;
                $isElem = $sibling->nodeType === XML_ELEMENT_NODE;
                $isBreaker = $isElem && (
                    preg_match('/\bai-image-placeholder\b|\bai-image-gallery\b|\bimage-text-wrapper\b/', ' ' . $sibling->getAttribute('class') . ' ') ||
                    in_array(strtoupper($sibling->nodeName), ['H1','H2','H3','H4','H5','H6','HR'])
                );
                if ($isBreaker) break;
                $adj->appendChild($sibling);
                $sibling = $next;
            }

            $ph->parentNode->insertBefore($wrapper, $ph);
            $wrapper->appendChild($ph);
            if ($adj->hasChildNodes()) $wrapper->appendChild($adj);
        }

        // 4) Group consecutive placeholders into galleries if not already inside one
        $children = iterator_to_array($root->childNodes);
        $run = [];
        $flushRun = function() use (&$run, $doc, $root) {
            if (count($run) >= 2) {
                // create 2 or 3 item gallery based on run size
                $cols = count($run) === 2 ? '2' : '3';
                $gal = $doc->createElement('div');
                $gal->setAttribute('class', 'ai-image-layout-gallery-' . $cols . '-item ai-image-gallery');
                $grid = $doc->createElement('div');
                $grid->setAttribute('class', 'ai-gallery-grid');
                $gal->appendChild($grid);
                $root->insertBefore($gal, $run[0]);
                foreach ($run as $n) { $grid->appendChild($n); }
            }
            $run = [];
        };
        foreach ($children as $node) {
            if ($node->nodeType === XML_ELEMENT_NODE && strpos(' ' . $node->getAttribute('class') . ' ', ' ai-image-placeholder ') !== false) {
                if ($node->parentNode && strpos(' ' . $node->parentNode->getAttribute('class') . ' ', ' ai-image-gallery ') !== false) continue;
                $run[] = $node;
            } else if ($node->nodeType === XML_TEXT_NODE && trim($node->nodeValue) === '') {
                continue;
            } else {
                $flushRun();
            }
        }
        $flushRun();

        // Return inner HTML of wrapper
        $out = '';
        foreach ($root->childNodes as $n) {
            $out .= $doc->saveHTML($n);
        }
        return $out ?: $html;
    }

    private function fixHtmlStructure(string $content): string
    {
        // First, ensure we don't have premature closing of post-content-inner
        // Look for patterns where content appears after </div><p> which indicates broken structure
        $content = preg_replace('/(<\/div>)(<p>|<h[1-6]>)/', '$2', $content);
        
        // Remove any stray closing divs that don't have opening tags at the beginning of lines
        $content = preg_replace('/^\s*<\/div>\s*$/m', '', $content);
        
        // Fix the specific issue where content breaks out of post-content-inner
        // Look for pattern: </div><p></p> followed by content
        $content = preg_replace('/(<\/div>)(<p><\/p>\s*)(<div class="post-image|<h[1-6]>|<p>)/', '$2$3', $content);
        
        // Remove empty paragraphs that cause structure issues
        $content = preg_replace('/<p>\s*<\/p>/', '', $content);
        
        // Remove multiple consecutive closing divs that aren't needed
        $content = preg_replace('/(<\/div>\s*){3,}/', '</div></div>', $content);
        
        // Ensure proper spacing around block elements
        $content = preg_replace('/(<\/div>)(<figure|<div class="post-image)/', '$1' . "\n\n" . '$2', $content);
        $content = preg_replace('/(<\/figure>)(<div|<p|<h[1-6])/', '$1' . "\n\n" . '$2', $content);
        
        // Move any post-image elements that are outside the content area back inside
        // This fixes placeholders appearing after the article tag
        if (preg_match('/(<\/article>.*?)(<div class="post-image-placeholder.*?<\/div>)/s', $content, $matches)) {
            $outsideContent = $matches[2];
            $content = str_replace($matches[0], $matches[1], $content);
            // Insert the content before the last closing tag of post-content-inner
            $content = preg_replace('/(<\/div>\s*<\/div>\s*<\/article>)/', $outsideContent . '$1', $content);
        }
        
        return $content;
    }

    /**
     * Generate a URL-friendly slug from a string.
     *
     * @param string $string The string to convert to a slug
     * @return string The generated slug
     */
    private function generateSlug(string $string): string
    {
        // Convert to lowercase and remove extra whitespace
        $slug = strtolower(trim($string));

        // Replace non-alphanumeric characters with hyphens
        $slug = preg_replace('/[^a-z0-9]+/', '-', $slug);

        // Remove leading and trailing hyphens
        $slug = trim($slug, '-');

        // Ensure minimum length
        if (strlen($slug) < 3) {
            $slug = 'post-' . time();
        }

        // Ensure slug is unique
        $originalSlug = $slug;
        $count = 1;
        $maxAttempts = 100; // Prevent infinite loop

        while ($this->postModel->findBySlug($slug) && $count <= $maxAttempts) {
            $slug = $originalSlug . '-' . $count;
            $count++;
        }

        // Final safety check
        if ($count > $maxAttempts) {
            $slug = $originalSlug . '-' . time();
        }

        error_log("Generated slug: $slug for title: $string");
        return $slug;
    }

    /**
     * Show the form for editing a post.
     *
     * @param int $id Post ID
     */
    public function edit(int $id): void
    {
        $post = $this->postModel->findById($id);

        if (!$post) {
            Session::set('flash_message', 'Post not found');
            header('Location: /admin/posts');
            exit;
        }

        $categories = $this->categoryModel->findAll();

        // Capture content in buffer to use with layout
        ob_start();
        View::render('admin/posts/edit', [
            'title' => 'Edit Post',
            'post' => $post,
            'categories' => $categories
        ], null);
        $content = ob_get_clean();

        // Render with admin layout
        View::renderLayout('admin', [
            'title' => 'Edit Post',
            'content' => $content,
            'userRole' => Session::get('user_role', '')
        ]);
    }

    /**
     * Show the form for collaborative editing of a post.
     *
     * @param int $id Post ID
     */
    public function collaborativeEdit(int $id): void
    {
        $post = $this->postModel->findById($id);

        if (!$post) {
            Session::set('flash_message', 'Post not found');
            header('Location: /admin/posts');
            exit;
        }

        $categories = $this->categoryModel->findAll();

        // Get current user
        $userId = Session::get('user_id');
        $currentUser = [
            'id' => $userId,
            'username' => Session::get('username', 'User'),
            'avatar' => '/assets/images/avatars/default.jpg'
        ];

        // Capture content in buffer to use with layout
        ob_start();
        View::render('admin/posts/collaborative-edit', [
            'title' => 'Collaborative Edit',
            'post' => $post,
            'categories' => $categories,
            'currentUser' => $currentUser
        ], null);
        $content = ob_get_clean();

        // Render with admin layout
        View::renderLayout('admin', [
            'title' => 'Collaborative Edit: ' . $post['title'],
            'content' => $content,
            'userRole' => Session::get('user_role', '')
        ]);
    }

    /**
     * Show version history for a post
     *
     * @param int $id Post ID
     */
    public function versionHistory(int $id): void
    {
        $post = $this->postModel->findById($id);

        if (!$post) {
            Session::set('flash_message', 'Post not found');
            header('Location: /admin/posts');
            exit;
        }

        // Get versions
        $versionModel = new \Brenzley\Models\VersionModel();
        $versions = $versionModel->getVersionsByDocumentId($id);

        // Capture content in buffer to use with layout
        ob_start();
        View::render('admin/posts/version-history', [
            'title' => 'Version History',
            'post' => $post,
            'versions' => $versions
        ], null);
        $content = ob_get_clean();

        // Render with admin layout
        View::renderLayout('admin', [
            'title' => 'Version History: ' . $post['title'],
            'content' => $content,
            'userRole' => Session::get('user_role', '')
        ]);
    }

    /**
     * Remove the specified post from storage.
     *
     * @param int $id Post ID from URL
     */
    public function delete(int $id): void
    {
        // Use POST for destructive actions, check CSRF
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            http_response_code(405);
            echo "Invalid request method.";
            return;
        }
        if (!Session::verifyCsrfToken($_POST['csrf_token'] ?? '')) {
             http_response_code(403);
             echo "Invalid security token.";
             return;
         }

        // TODO: Add authorization check

        if ($this->postModel->delete($id)) {
            Session::set('flash_message', 'Post deleted successfully!');
        } else {
            Session::set('flash_message', 'Error deleting post.'); // Use a different type of flash later
        }
        header('Location: /admin/posts');
        exit;
    }

    /**
     * API: Generate content with AI
     */
    public function generateContentApi(): void
    {
        // Set content type to JSON
        header('Content-Type: application/json');

        // Check for POST request
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            http_response_code(405);
            echo json_encode(['success' => false, 'message' => 'Method not allowed']);
            return;
        }

        // Verify CSRF token
        if (!Session::verifyCsrfToken($_POST['csrf_token'] ?? '')) {
            http_response_code(403);
            echo json_encode(['success' => false, 'message' => 'Invalid security token']);
            return;
        }

        // Get JSON data
        $json = file_get_contents('php://input');
        $data = json_decode($json, true);

        if (!$data) {
            $data = $_POST; // Fallback to form data if JSON is not provided
        }

        // Validate required fields
        if (empty($data['prompt'])) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Prompt is required']);
            return;
        }

        // In a real implementation, this would call the AI provider
        // For now, we'll simulate a response
        $generatedContent = $this->simulateAiGeneration($data['prompt']);

        // Return the generated content
        echo json_encode([
            'success' => true,
            'content' => $generatedContent,
            'metadata' => [
                'tokens_used' => rand(100, 500),
                'generation_time' => rand(5, 20) / 10 . 's',
                'provider' => 'simulation',
                'model' => 'demo-model'
            ]
        ]);
    }

    /**
     * API: Enhance content with AI
     */
    public function enhanceContentApi(): void
    {
        // Set content type to JSON
        header('Content-Type: application/json');

        // Check for POST request
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            http_response_code(405);
            echo json_encode(['success' => false, 'message' => 'Method not allowed']);
            return;
        }

        // Verify CSRF token
        if (!Session::verifyCsrfToken($_POST['csrf_token'] ?? '')) {
            http_response_code(403);
            echo json_encode(['success' => false, 'message' => 'Invalid security token']);
            return;
        }

        // Get JSON data
        $json = file_get_contents('php://input');
        $data = json_decode($json, true);

        if (!$data) {
            $data = $_POST; // Fallback to form data if JSON is not provided
        }

        // Validate required fields
        if (empty($data['content'])) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Content is required']);
            return;
        }

        if (empty($data['enhancement_type'])) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Enhancement type is required']);
            return;
        }

        // In a real implementation, this would call the AI provider
        // For now, we'll simulate a response
        $enhancedContent = $this->simulateContentEnhancement($data['content'], $data['enhancement_type']);

        // Return the enhanced content
        echo json_encode([
            'success' => true,
            'enhanced_content' => $enhancedContent,
            'changes' => [
                [
                    'original' => 'Original text example',
                    'enhanced' => 'Enhanced text example',
                    'reason' => 'Improved clarity and engagement'
                ]
            ],
            'improvement_score' => rand(5, 15)
        ]);
    }

    /**
     * API: Analyze SEO
     */
    public function analyzeSeoApi(): void
    {
        // Set content type to JSON
        header('Content-Type: application/json');

        // Check for POST request
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            http_response_code(405);
            echo json_encode(['success' => false, 'message' => 'Method not allowed']);
            return;
        }

        // Verify CSRF token
        if (!Session::verifyCsrfToken($_POST['csrf_token'] ?? '')) {
            http_response_code(403);
            echo json_encode(['success' => false, 'message' => 'Invalid security token']);
            return;
        }

        // Get JSON data
        $json = file_get_contents('php://input');
        $data = json_decode($json, true);

        if (!$data) {
            $data = $_POST; // Fallback to form data if JSON is not provided
        }

        // Validate required fields
        if (empty($data['content'])) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Content is required']);
            return;
        }

        // In a real implementation, this would call the AI provider
        // For now, we'll simulate a response
        $seoScore = rand(50, 95);

        // Return the SEO analysis
        echo json_encode([
            'success' => true,
            'overall_score' => $seoScore,
            'analysis' => [
                'keyword_usage' => [
                    'score' => rand(60, 95),
                    'primary_keyword_count' => rand(3, 8),
                    'primary_keyword_density' => rand(10, 25) / 10 . '%',
                    'secondary_keywords_used' => ['keyword1'],
                    'missing_keywords' => ['keyword2'],
                    'suggestions' => ['Add keyword2 to H2 heading']
                ],
                'readability' => [
                    'score' => rand(60, 95),
                    'reading_level' => 'Grade ' . rand(6, 10),
                    'avg_sentence_length' => rand(15, 25),
                    'complex_words_percentage' => rand(8, 15) . '%',
                    'suggestions' => ['Shorten sentences in paragraph 3']
                ],
                'structure' => [
                    'score' => rand(60, 95),
                    'heading_count' => rand(3, 8),
                    'heading_keyword_usage' => rand(40, 80) . '%',
                    'suggestions' => ['Add H3 subheadings under second H2']
                ],
                'content_quality' => [
                    'score' => rand(60, 95),
                    'word_count' => rand(800, 1500),
                    'content_depth' => 'Medium',
                    'suggestions' => ['Expand section on benefits']
                ]
            ]
        ]);
    }

    /**
     * Simulate AI content generation
     *
     * @param string $prompt The prompt to generate content from
     * @return string The generated content
     */
    private function simulateAiGeneration(string $prompt): string
    {
        // Simple simulation of AI-generated content
        $responses = [
            '<p>This is a simulated AI response based on your prompt: "<strong>' . htmlspecialchars($prompt) . '</strong>"</p>',
            '<p>In a real implementation, this would call an AI provider API to generate content based on your prompt.</p>',
            '<h2>Key Points</h2>',
            '<ul>',
            '  <li>This is a demonstration of the AI content generation feature</li>',
            '  <li>The actual implementation would integrate with OpenAI, Google AI, or another provider</li>',
            '  <li>The generated content would be more relevant to your specific prompt</li>',
            '</ul>',
            '<p>For now, this simulated response gives you an idea of how the feature will work when fully implemented.</p>'
        ];

        return implode("\n", $responses);
    }

    /**
     * Simulate content enhancement
     *
     * @param string $content The content to enhance
     * @param string $enhancementType The type of enhancement to perform
     * @return string The enhanced content
     */
    private function simulateContentEnhancement(string $content, string $enhancementType): string
    {
        // Add a prefix based on enhancement type
        $prefix = '<p><em>This content has been enhanced for ' . htmlspecialchars($enhancementType) . ':</em></p>';

        // Return the "enhanced" content
        return $prefix . $content;
    }
}