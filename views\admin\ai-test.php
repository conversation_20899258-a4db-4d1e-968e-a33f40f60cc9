<?php require_once 'partials/header.php'; ?>

<div class="admin-content">
    <div class="admin-content-header">
        <h1><?= $title ?></h1>
        <p>Test your AI provider with streaming responses</p>
    </div>

    <div class="admin-content-body">
        <div class="card">
            <div class="card-header">
                <h2>Test AI Provider</h2>
            </div>
            <div class="card-body">
                <?php if (!$activeProvider || $activeProvider['provider'] === 'none'): ?>
                <div class="alert alert-warning">
                    <p>No active AI provider configured. Please configure an AI provider in the <a href="/admin/settings/ai">AI Settings</a> page.</p>
                </div>
                <?php else: ?>
                <form id="ai-test-form">
                    <div class="form-group">
                        <label for="provider">Provider</label>
                        <select id="provider" name="provider" class="form-control">
                            <option value="">Use Active Provider (<?= $activeProvider['provider'] ?> - <?= $activeProvider['selected_model'] ?? 'No model selected' ?>)</option>
                            <?php foreach ($providers as $provider): ?>
                            <option value="<?= $provider['id'] ?>"><?= ucfirst($provider['provider']) ?> - <?= $provider['selected_model'] ?? 'No model selected' ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="prompt">Prompt</label>
                        <textarea id="prompt" name="prompt" class="form-control" rows="5" placeholder="Enter your prompt here..."></textarea>
                    </div>

                    <div class="form-group">
                        <button type="submit" id="submit-btn" class="btn btn-primary">Test with Streaming</button>
                        <button type="button" id="clear-btn" class="btn btn-secondary">Clear Response</button>
                    </div>
                </form>

                <div class="ai-test-response-container">
                    <h3>Response</h3>
                    <div id="response-status" class="ai-test-status"></div>
                    <div id="response-container" class="ai-test-response"></div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<style>
.ai-test-response-container {
    margin-top: 20px;
    border-top: 1px solid #eee;
    padding-top: 20px;
}

.ai-test-status {
    margin-bottom: 10px;
    font-size: 14px;
    color: #666;
}

.ai-test-response {
    background-color: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 15px;
    min-height: 200px;
    max-height: 500px;
    overflow-y: auto;
    white-space: pre-wrap;
    font-family: monospace;
    line-height: 1.5;
}

.ai-test-response.streaming {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.ai-test-response.error {
    border-color: #dc3545;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

.ai-test-response.success {
    border-color: #28a745;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}

.ai-test-response p {
    margin-bottom: 10px;
}

.ai-test-response code {
    background-color: #eee;
    padding: 2px 4px;
    border-radius: 3px;
}

.ai-test-response pre {
    background-color: #333;
    color: #fff;
    padding: 10px;
    border-radius: 4px;
    overflow-x: auto;
}

.cursor {
    display: inline-block;
    width: 8px;
    height: 16px;
    background-color: #333;
    animation: blink 1s infinite;
    vertical-align: middle;
}

@keyframes blink {
    0% { opacity: 1; }
    50% { opacity: 0; }
    100% { opacity: 1; }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('ai-test-form');
    const submitBtn = document.getElementById('submit-btn');
    const clearBtn = document.getElementById('clear-btn');
    const promptInput = document.getElementById('prompt');
    const providerSelect = document.getElementById('provider');
    const responseContainer = document.getElementById('response-container');
    const responseStatus = document.getElementById('response-status');

    let eventSource = null;
    let cursor = null;

    // Submit form
    form.addEventListener('submit', function(e) {
        e.preventDefault();

        // Validate form
        if (!promptInput.value.trim()) {
            alert('Please enter a prompt');
            return;
        }

        // Disable submit button
        submitBtn.disabled = true;
        submitBtn.textContent = 'Streaming...';

        // Clear previous response
        responseContainer.textContent = '';
        responseContainer.classList.remove('error', 'success');
        responseContainer.classList.add('streaming');

        // Add cursor
        cursor = document.createElement('span');
        cursor.className = 'cursor';
        responseContainer.appendChild(cursor);

        // Update status
        responseStatus.textContent = 'Connecting...';

        // Close previous event source if exists
        if (eventSource) {
            eventSource.close();
        }

        // Create new event source
        const providerId = providerSelect.value;
        const prompt = promptInput.value.trim();

        // Create URL with query parameters
        const url = `/api/admin/ai/test-stream?prompt=${encodeURIComponent(prompt)}&provider_id=${encodeURIComponent(providerId)}`;

        eventSource = new EventSource(url);

        // Event handlers
        eventSource.onopen = function() {
            responseStatus.textContent = 'Connected, waiting for response...';
        };

        eventSource.onmessage = function(event) {
            const data = JSON.parse(event.data);

            switch (data.type) {
                case 'start':
                    responseStatus.textContent = data.message;
                    break;

                case 'chunk':
                    // Remove cursor
                    if (cursor && cursor.parentNode) {
                        cursor.parentNode.removeChild(cursor);
                    }

                    // Append chunk
                    responseContainer.textContent += data.content;

                    // Add cursor at the end
                    cursor = document.createElement('span');
                    cursor.className = 'cursor';
                    responseContainer.appendChild(cursor);

                    // Scroll to bottom
                    responseContainer.scrollTop = responseContainer.scrollHeight;
                    break;

                case 'end':
                    // Remove cursor
                    if (cursor && cursor.parentNode) {
                        cursor.parentNode.removeChild(cursor);
                    }

                    // Update status
                    responseStatus.textContent = 'Completed';
                    responseContainer.classList.remove('streaming');
                    responseContainer.classList.add('success');

                    // Close event source
                    eventSource.close();
                    eventSource = null;

                    // Enable submit button
                    submitBtn.disabled = false;
                    submitBtn.textContent = 'Test with Streaming';
                    break;

                case 'error':
                    // Remove cursor
                    if (cursor && cursor.parentNode) {
                        cursor.parentNode.removeChild(cursor);
                    }

                    // Update status and show detailed error
                    const errorMessage = data.message || 'Unknown streaming error occurred';
                    responseStatus.textContent = 'Error: ' + errorMessage;
                    responseContainer.textContent += '\n\n❌ Error: ' + errorMessage;
                    responseContainer.classList.remove('streaming');
                    responseContainer.classList.add('error');

                    // Close event source
                    eventSource.close();
                    eventSource = null;

                    // Enable submit button
                    submitBtn.disabled = false;
                    submitBtn.textContent = 'Test with Streaming';
                    break;
            }
        };

        eventSource.onerror = function(event) {
            // Remove cursor
            if (cursor && cursor.parentNode) {
                cursor.parentNode.removeChild(cursor);
            }

            // Update status with detailed error
            responseStatus.textContent = 'Connection error - Stream failed';
            responseContainer.textContent += '\n\n❌ Connection Error: Stream connection failed. Please check your API settings and try again.';
            responseContainer.classList.remove('streaming');
            responseContainer.classList.add('error');

            // Log error for debugging
            console.error('EventSource error:', event);

            // Close event source
            eventSource.close();
            eventSource = null;

            // Enable submit button
            submitBtn.disabled = false;
            submitBtn.textContent = 'Test with Streaming';
        };
    });

    // Clear response
    clearBtn.addEventListener('click', function() {
        // Close event source if exists
        if (eventSource) {
            eventSource.close();
            eventSource = null;
        }

        // Clear response
        responseContainer.textContent = '';
        responseContainer.classList.remove('streaming', 'error', 'success');
        responseStatus.textContent = '';

        // Enable submit button
        submitBtn.disabled = false;
        submitBtn.textContent = 'Test with Streaming';
    });
});
</script>

<?php require_once 'partials/footer.php'; ?>
