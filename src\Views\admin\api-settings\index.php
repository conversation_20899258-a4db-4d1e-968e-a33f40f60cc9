<?php
// API Settings View
use Brenzley\Core\Session;

// Helper function to get provider display name
function getProviderDisplayName(string $provider): string {
    $names = [
        'none' => 'None',
        'openrouter' => 'OpenRouter',
        'openai_compatible' => 'OpenAI Compatible'
    ];

    return $names[$provider] ?? ucfirst($provider);
}

// Helper function to get provider icon
function getProviderIcon(string $provider): string {
    $icons = [
        'none' => '<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><line x1="4.93" y1="4.93" x2="19.07" y2="19.07"></line></svg>',
        'openrouter' => '<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="2" y="7" width="20" height="15" rx="2" ry="2"></rect><polyline points="17 2 12 7 7 2"></polyline></svg>',
        'openai_compatible' => '<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"></path></svg>'
    ];

    return $icons[$provider] ?? '<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><line x1="12" y1="8" x2="12" y2="12"></line><line x1="12" y1="16" x2="12.01" y2="16"></line></svg>';
}

// Helper function to check if a provider is active
function isProviderActive(array $apiSettings, string $provider): bool {
    foreach ($apiSettings as $setting) {
        if ($setting['provider'] === $provider && $setting['is_active']) {
            return true;
        }
    }
    return false;
}

// Helper function to get provider settings
function getProviderSettings(array $apiSettings, string $provider): ?array {
    foreach ($apiSettings as $setting) {
        if ($setting['provider'] === $provider) {
            return $setting;
        }
    }
    return null;
}

// Get active provider
$activeProvider = 'none';
foreach ($apiSettings as $setting) {
    if ($setting['is_active']) {
        $activeProvider = $setting['provider'];
        break;
    }
}
?>

<!-- Include jQuery and Select2 -->
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

<div class="ai-settings-container">
    <!-- AI Decorative Elements -->
    <div class="ai-decorative-elements">
        <div class="ai-decorative-circle ai-decorative-circle-1"></div>
        <div class="ai-decorative-circle ai-decorative-circle-2"></div>
        <div class="ai-decorative-circle ai-decorative-circle-3"></div>
        <div class="ai-decorative-dots"></div>
    </div>

    <div class="ai-settings-header">
        <div class="ai-settings-title">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M12 2a10 10 0 0 1 10 10c0 5.523-4.477 10-10 10S2 17.523 2 12 6.477 2 12 2z"></path>
                <circle cx="12" cy="12" r="2"></circle>
                <path d="M12 18c-3.3 0-6-2.7-6-6s2.7-6 6-6 6 2.7 6 6-2.7 6-6 6"></path>
                <path d="M12 8c-2.2 0-4 1.8-4 4s1.8 4 4 4 4-1.8 4-4-1.8-4-4-4"></path>
            </svg>
            AI Settings
        </div>
        <p class="ai-settings-description">Configure AI providers to enable intelligent features throughout your site.</p>
    </div>

    <?php if (Session::has('flash_message')): ?>
        <div class="ai-alert ai-alert-<?= Session::get('flash_type', 'info') ?> show" role="alert">
            <?= Session::get('flash_message') ?>
        </div>
        <?php Session::remove('flash_message'); Session::remove('flash_type'); ?>
    <?php endif; ?>

    <div class="ai-settings-card">
        <div class="ai-settings-card-header">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                <polyline points="22 4 12 14.01 9 11.01"></polyline>
            </svg>
            <h2 class="ai-settings-card-title">Active AI Provider</h2>
        </div>
        <div class="ai-settings-card-body">
            <form action="/admin/api-settings/set-active" method="POST" class="ai-form">
                <input type="hidden" name="csrf_token" value="<?= Session::getCsrfToken() ?>">

                <div class="ai-form-group">
                    <label for="active-provider" class="ai-form-label">Select Active Provider</label>
                    <select class="ai-form-control" id="active-provider" name="provider">
                        <?php foreach ($apiSettings as $setting): ?>
                            <option value="<?= htmlspecialchars($setting['provider']) ?>"
                                <?= $setting['provider'] === $activeProvider ? 'selected' : '' ?>
                                <?= !$setting['is_verified'] && $setting['provider'] !== 'none' ? 'disabled' : '' ?>>
                                <?= htmlspecialchars(getProviderDisplayName($setting['provider'])) ?>
                                <?= !$setting['is_verified'] && $setting['provider'] !== 'none' ? ' (Not Verified)' : '' ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                    <div class="ai-form-text">Only verified providers can be selected. The "None" option disables AI functionality.</div>
                </div>

                <button type="submit" class="ai-btn ai-btn-primary">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z"></path>
                        <polyline points="17 21 17 13 7 13 7 21"></polyline>
                        <polyline points="7 3 7 8 15 8"></polyline>
                    </svg>
                    Save Active Provider
                </button>
            </form>
        </div>
    </div>

    <div class="ai-settings-card">
        <div class="ai-settings-card-header">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"></path>
            </svg>
            <h2 class="ai-settings-card-title">Configure AI Providers</h2>
        </div>
        <div class="ai-settings-card-body p-0">
            <ul class="ai-tabs" id="providerTabs" role="tablist">
                <?php foreach ($apiSettings as $index => $setting): ?>
                    <?php if ($setting['provider'] !== 'none'): ?>
                        <li class="ai-tab-item" role="presentation">
                            <button class="ai-tab-link <?= $index === 1 ? 'active' : '' ?>"
                                    id="<?= $setting['provider'] ?>-tab"
                                    data-bs-toggle="tab"
                                    data-bs-target="#<?= $setting['provider'] ?>-pane"
                                    type="button"
                                    role="tab"
                                    aria-controls="<?= $setting['provider'] ?>-pane"
                                    aria-selected="<?= $index === 1 ? 'true' : 'false' ?>">
                                <span class="ai-tab-icon"><?= getProviderIcon($setting['provider']) ?></span>
                                <?= htmlspecialchars(getProviderDisplayName($setting['provider'])) ?>
                                <?= $setting['is_verified'] ? '<span class="verified-badge"><svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"><polyline points="20 6 9 17 4 12"></polyline></svg></span>' : '' ?>
                            </button>
                        </li>
                    <?php endif; ?>
                <?php endforeach; ?>
            </ul>

            <div class="ai-tab-content" id="providerTabsContent">
                <?php foreach ($apiSettings as $index => $setting): ?>
                    <?php if ($setting['provider'] !== 'none'): ?>
                        <div class="ai-tab-pane <?= $index === 1 ? 'active' : '' ?>"
                             id="<?= $setting['provider'] ?>-pane"
                             role="tabpanel"
                             aria-labelledby="<?= $setting['provider'] ?>-tab">

                            <form id="<?= $setting['provider'] ?>-form" action="/admin/api-settings/update" method="POST" class="ai-form">
                                <input type="hidden" name="csrf_token" value="<?= Session::getCsrfToken() ?>">
                                <input type="hidden" name="provider" value="<?= htmlspecialchars($setting['provider']) ?>">

                                <div class="ai-form-group">
                                    <label for="<?= $setting['provider'] ?>-api-key" class="ai-form-label">API Key</label>
                                    <div class="ai-input-group">
                                        <input type="password" class="ai-form-control api-key-input"
                                               id="<?= $setting['provider'] ?>-api-key"
                                               name="api_key"
                                               placeholder="Enter API key"
                                               value="<?= !empty($setting['api_key']) ? '••••••••••••••••••••••' : '' ?>">
                                        <div class="ai-input-group-append">
                                            <span class="ai-input-group-text toggle-password">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                    <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                                                    <circle cx="12" cy="12" r="3"></circle>
                                                </svg>
                                            </span>
                                        </div>
                                    </div>
                                    <div class="ai-form-text">Your API key is encrypted and stored securely.</div>
                                </div>

                                <?php if ($setting['provider'] === 'openai_compatible'): ?>
                                    <div class="ai-form-group">
                                        <label for="openai_compatible-base-url" class="ai-form-label">Base URL</label>
                                        <input type="url" class="ai-form-control"
                                               id="openai_compatible-base-url"
                                               name="base_url"
                                               placeholder="https://api.example.com"
                                               value="<?= htmlspecialchars($setting['base_url'] ?? '') ?>">
                        <div class="ai-form-error" id="openai_compatible-base-url-error"></div>
                        <div class="ai-form-text">The base URL for your OpenAI compatible API.</div>
                    </div>
                <?php endif; ?>

                                <div class="ai-form-group">
                                    <label for="<?= $setting['provider'] ?>-model" class="ai-form-label">Model</label>
                                    <div class="model-select-wrapper">
                                        <select class="ai-form-control model-select"
                                                id="<?= $setting['provider'] ?>-model"
                                                name="selected_model"
                                                data-provider="<?= htmlspecialchars($setting['provider']) ?>">
                                                <option value="">Select a model</option>
                                            <?php if (!empty($setting['selected_model'])): ?>
                                                <option value="<?= htmlspecialchars($setting['selected_model']) ?>" selected>
                                                    <?= htmlspecialchars($setting['selected_model']) ?>
                                                </option>
                                            <?php endif; ?>
                                        </select>
                                        <button type="button" class="model-refresh-btn" data-provider="<?= htmlspecialchars($setting['provider']) ?>" title="Refresh models list">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                <path d="M23 4v6h-6"></path>
                                                <path d="M1 20v-6h6"></path>
                                                <path d="M3.51 9a9 9 0 0 1 14.85-3.36L23 10"></path>
                                                <path d="M1 14l4.64 4.36A9 9 0 0 0 20.49 15"></path>
                                            </svg>
                                        </button>
                                    </div>
                                    <div class="ai-form-text">Select a model from the available options.</div>
                                </div>

                                <div class="ai-form-group">
                                    <label for="<?= $setting['provider'] ?>-context-window" class="ai-form-label">Context Window (tokens)</label>
                                    <?php if ($setting['provider'] === 'openai_compatible'): ?>
                                        <input type="number" class="ai-form-control context-window-input"
                                               id="<?= $setting['provider'] ?>-context-window"
                                               name="context_window"
                                               min="4096"
                                               max="1005000"
                                               step="1024"
                                               value="<?= (int)($setting['context_window'] ?? 8096) ?>">
                                        <div class="ai-form-text">
                                            Enter the context window size for your model (min: 4096, max: 1,005,000).
                                        </div>
                                    <?php else: ?>
                                        <input type="text" class="ai-form-control context-window-input"
                                               id="<?= $setting['provider'] ?>-context-window"
                                               name="context_window"
                                               value="<?= (int)($setting['context_window'] ?? 8096) ?>"
                                               readonly>
                                        <div class="ai-form-text">
                                            This will be automatically updated when you select a model.
                                        </div>
                                    <?php endif; ?>
                                </div>

                                <div class="ai-form-group">
                                    <div class="ai-form-section-title">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <path d="M12 2v20M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"></path>
                                        </svg>
                                        Rate Limiting Settings
                                    </div>

                                    <div class="ai-form-toggle-group">
                                        <label class="ai-form-label">Enable Rate Limiting</label>
                                        <div class="ai-form-toggle">
                                            <!-- Use a hidden field with a different name to avoid conflicts -->
                                            <input type="hidden" name="settings[enable_rate_limiting_hidden]" value="0">
                                            <input type="checkbox"
                                                   id="<?= $setting['provider'] ?>-enable-rate-limiting"
                                                   name="settings[enable_rate_limiting]"
                                                   value="1"
                                                   class="ai-toggle-input rate-limiting-toggle"
                                                   data-provider="<?= $setting['provider'] ?>"
                                                   <?= (!isset($setting['settings']['enable_rate_limiting']) || $setting['settings']['enable_rate_limiting']) ? 'checked' : '' ?>>
                                            <label for="<?= $setting['provider'] ?>-enable-rate-limiting" class="ai-toggle-label"></label>
                                        </div>
                                    </div>
                                    <div class="ai-form-text">
                                        Enable client-side rate limiting to prevent API rate limit errors.
                                    </div>

                                    <div class="rate-limiting-settings" id="<?= $setting['provider'] ?>-rate-limiting-settings"
                                         style="<?= isset($setting['settings']['enable_rate_limiting']) && $setting['settings']['enable_rate_limiting'] == false ? 'display: none;' : '' ?>">
                                        <div class="ai-form-row">
                                            <div class="ai-form-col">
                                                <label for="<?= $setting['provider'] ?>-requests-per-minute" class="ai-form-label">Requests Per Minute</label>
                                                <input type="number"
                                                       class="ai-form-control"
                                                       id="<?= $setting['provider'] ?>-requests-per-minute"
                                                       name="settings[requests_per_minute]"
                                                       min="1"
                                                       max="1000"
                                                       value="<?= (int)($setting['settings']['requests_per_minute'] ?? 60) ?>">
                                                <div class="ai-form-text">
                                                    Maximum number of API requests per minute.
                                                </div>
                                            </div>
                                            <div class="ai-form-col">
                                                <label for="<?= $setting['provider'] ?>-tokens-per-minute" class="ai-form-label">Tokens Per Minute</label>
                                                <input type="number"
                                                       class="ai-form-control"
                                                       id="<?= $setting['provider'] ?>-tokens-per-minute"
                                                       name="settings[tokens_per_minute]"
                                                       min="1000"
                                                       max="1000000"
                                                       value="<?= (int)($setting['settings']['tokens_per_minute'] ?? 100000) ?>">
                                                <div class="ai-form-text">
                                                    Maximum number of tokens (input + output) per minute.
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="ai-form-group">
                                    <div class="ai-form-section-title">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                                            <polyline points="17 8 12 3 7 8"></polyline>
                                            <line x1="12" y1="3" x2="12" y2="15"></line>
                                        </svg>
                                        Usage Tracking Settings
                                    </div>

                                    <div class="ai-form-toggle-group">
                                        <label class="ai-form-label">Enable Usage Tracking</label>
                                        <div class="ai-form-toggle">
                                            <!-- Use a hidden field with a different name to avoid conflicts -->
                                            <input type="hidden" name="settings[enable_usage_tracking_hidden]" value="0">
                                            <input type="checkbox"
                                                   id="<?= $setting['provider'] ?>-enable-usage-tracking"
                                                   name="settings[enable_usage_tracking]"
                                                   value="1"
                                                   class="ai-toggle-input"
                                                   <?= (!isset($setting['settings']['enable_usage_tracking']) || $setting['settings']['enable_usage_tracking']) ? 'checked' : '' ?>>
                                            <label for="<?= $setting['provider'] ?>-enable-usage-tracking" class="ai-toggle-label"></label>
                                        </div>
                                    </div>
                                    <div class="ai-form-text">
                                        Track API usage for monitoring and billing purposes.
                                    </div>
                                </div>

                                <div class="ai-btn-group">
                                    <button type="submit" class="ai-btn ai-btn-primary">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z"></path>
                                            <polyline points="17 21 17 13 7 13 7 21"></polyline>
                                            <polyline points="7 3 7 8 15 8"></polyline>
                                        </svg>
                                        Save Settings
                                    </button>
                                    <button type="button" class="ai-btn ai-btn-secondary test-connection-btn"
                                            data-provider="<?= htmlspecialchars($setting['provider']) ?>">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                                            <polyline points="22 4 12 14.01 9 11.01"></polyline>
                                        </svg>
                                        Test Connection
                                    </button>
                                </div>

                                <div class="connection-status" id="<?= $setting['provider'] ?>-connection-status"></div>
                            </form>
                        </div>
                    <?php endif; ?>
                <?php endforeach; ?>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Fix Select2 dropdown positioning
    $(document).on('select2:open', () => {
        document.querySelector('.select2-search__field').focus();
    });


    // Custom tab functionality
    const tabLinks = document.querySelectorAll('.ai-tab-link');
    const tabPanes = document.querySelectorAll('.ai-tab-pane');

    tabLinks.forEach(tabLink => {
        tabLink.addEventListener('click', function(e) {
            e.preventDefault();

            // Remove active class from all tabs and panes
            tabLinks.forEach(link => link.classList.remove('active'));
            tabPanes.forEach(pane => pane.classList.remove('active'));

            // Add active class to current tab and pane
            this.classList.add('active');
            const targetId = this.getAttribute('data-bs-target').substring(1);
            document.getElementById(targetId).classList.add('active');
            
            // Auto-fetch models when switching to a provider tab
            const provider = targetId.replace('-pane', '');
            if (provider && provider !== 'none') {
                console.log('Tab clicked for provider:', provider);
                
                // Check if API key is available
                const apiKeyInput = document.getElementById(`${provider}-api-key`);
                if (apiKeyInput && apiKeyInput.value) {
                    let baseUrl = null;
                    if (provider === 'openai_compatible') {
                        const baseUrlInput = document.getElementById('openai_compatible-base-url');
                        if (baseUrlInput && baseUrlInput.value.trim()) {
                            baseUrl = baseUrlInput.value.trim();
                        }
                    }
                    
                    // Fetch models for this provider
                    setTimeout(() => {
                        fetchModels(provider, baseUrl);
                    }, 100); // Small delay to ensure UI is updated
                }
            }
        });
    });

    // Toggle password visibility
    document.querySelectorAll('.toggle-password').forEach(button => {
        button.addEventListener('click', function() {
            const input = this.closest('.ai-input-group').querySelector('input');
            const icon = this.querySelector('svg');

            if (input.type === 'password') {
                input.type = 'text';
                icon.outerHTML = '<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24"></path><line x1="1" y1="1" x2="23" y2="23"></line></svg>';
            } else {
                input.type = 'password';
                icon.outerHTML = '<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path><circle cx="12" cy="12" r="3"></circle></svg>';
            }
        });
    });

    // Initialize active provider select with Select2
    $('#active-provider').select2({
        placeholder: 'Select a provider',
        allowClear: false,
        width: '100%',
        minimumResultsForSearch: Infinity, // Disable search functionality
        selectionCssClass: 'select2-selection--custom',
        dropdownCssClass: 'select2-dropdown--custom',
        templateResult: formatProviderOption,
        templateSelection: formatProviderSelection
    });

    // Initialize model selects with searchable dropdown
    document.querySelectorAll('.model-select').forEach(select => {
        const provider = select.dataset.provider;

        // Only fetch models if API key is set
        const apiKeyInput = document.getElementById(`${provider}-api-key`);
        if (apiKeyInput && apiKeyInput.value) {
            let baseUrl = null;
            if (provider === 'openai_compatible') {
                const baseUrlInput = document.getElementById('openai_compatible-base-url');
                if (baseUrlInput && baseUrlInput.value.trim()) {
                    baseUrl = baseUrlInput.value.trim();
                }
            }
            console.log('Initial model fetch for provider:', provider);
            fetchModels(provider, baseUrl);
        }

        // Initialize select2 with theme support
        $(select).select2({
            placeholder: 'Select a model',
            allowClear: false, // Disable the clear button
            width: '100%',
            templateResult: formatModelOption,
            templateSelection: formatModelSelection,
            minimumResultsForSearch: 5, // Show search only when there are more than 5 options
            selectionCssClass: 'select2-selection--custom', // Add custom class for additional styling
            dropdownCssClass: 'select2-dropdown--custom' // Add custom class for dropdown styling
        });

        // Update context window when model changes
        $(select).on('change', function() {
            updateContextWindow(provider, this.value);
        });

        // Add input event listener to API key field to auto-load models
        if (apiKeyInput) {
            apiKeyInput.addEventListener('input', debounce(function() {
                const currentValue = this.value.trim();
                if (currentValue && currentValue.length > 10 && currentValue !== '••••••••••••••••••••••') {
                    let currentBaseUrl = null;
                    if (provider === 'openai_compatible') {
                        const baseUrlInput = document.getElementById('openai_compatible-base-url');
                        if (baseUrlInput) {
                            currentBaseUrl = baseUrlInput.value.trim();
                        }
                    }
                    console.log('API key input changed, fetching models for:', provider);
                    fetchModels(provider, currentBaseUrl);
                }
            }, 1000));
        }

        // Add input event listener to Base URL field for OpenAI Compatible to auto-load models
        if (provider === 'openai_compatible') {
            const baseUrlInput = document.getElementById('openai_compatible-base-url');
            if (baseUrlInput) {
                let previousBaseUrl = baseUrlInput.value; // Store initial value
                const baseUrlErrorDiv = document.getElementById('openai_compatible-base-url-error');

                baseUrlInput.addEventListener('input', debounce(function() {
                    const currentUrl = this.value.trim();
                    baseUrlErrorDiv.textContent = ''; // Clear previous errors

                    // Only proceed if URL has changed
                    if (currentUrl !== previousBaseUrl) {
                        previousBaseUrl = currentUrl; // Update previous URL
                        
                        // Clear previous errors
                        baseUrlErrorDiv.textContent = '';
                        
                        // Basic validation - but don't block fetching for minor issues
                        let hasValidationWarning = false;
                        
                        if (!currentUrl) {
                            baseUrlErrorDiv.textContent = 'Base URL cannot be empty.';
                            return; // Don't fetch if empty
                        }

                        try {
                            const url = new URL(currentUrl);
                            if (!url.protocol.startsWith('http')) {
                                baseUrlErrorDiv.textContent = 'Warning: URL should start with http:// or https://';
                                hasValidationWarning = true;
                            }
                            if (!currentUrl.endsWith('/v1')) {
                                baseUrlErrorDiv.textContent = 'Warning: URL should typically end with /v1';
                                hasValidationWarning = true;
                            }
                        } catch (e) {
                            baseUrlErrorDiv.textContent = 'Warning: Invalid URL format';
                            hasValidationWarning = true;
                        }
                        
                        // Check if API key is also present before fetching models
                        const apiKeyInput = document.getElementById('openai_compatible-api-key');
                        const apiKeyValue = apiKeyInput ? apiKeyInput.value.trim() : '';
                        if (apiKeyInput && apiKeyValue && apiKeyValue.length > 0 && apiKeyValue !== '••••••••••••••••••••••') {
                             console.log('Base URL changed, fetching models with API key');
                             fetchModels('openai_compatible', currentUrl); // Pass the current URL
                        } else if (apiKeyInput && apiKeyValue === '••••••••••••••••••••••') {
                             // Also fetch with masked API key (stored key)
                             console.log('Base URL changed, fetching models with stored API key');
                             fetchModels('openai_compatible', currentUrl);
                        } else {
                            // Show message asking for API key
                            const statusDiv = document.getElementById('openai_compatible-connection-status');
                             statusDiv.innerHTML = '<div class="ai-alert ai-alert-warning show">Please enter an API key to fetch models.</div>';
                             setTimeout(() => {
                                 const alert = statusDiv.querySelector('.ai-alert');
                                 if (alert) {
                                     alert.classList.remove('show');
                                     alert.classList.add('fade');
                                     setTimeout(() => {
                                         statusDiv.innerHTML = '';
                                     }, 300);
                                 }
                             }, 3000);
                        }
                    }
                }, 1000)); // Debounce with 1 second
            }
        }
    });

    // Refresh model list buttons
    document.querySelectorAll('.model-refresh-btn').forEach(button => {
        button.addEventListener('click', function() {
            const provider = this.dataset.provider;
            const apiKeyInput = document.getElementById(`${provider}-api-key`);
            const statusDiv = document.getElementById(`${provider}-connection-status`);

            let baseUrl = null; // Initialize baseUrl

            if (provider === 'openai_compatible') {
                const baseUrlInput = document.getElementById('openai_compatible-base-url');
                if (baseUrlInput) {
                    baseUrl = baseUrlInput.value.trim();
                    // Add validation here, similar to the input event listener
                    const baseUrlErrorDiv = document.getElementById('openai_compatible-base-url-error');
                    baseUrlErrorDiv.textContent = ''; // Clear previous errors

                    if (!baseUrl) {
                        baseUrlErrorDiv.textContent = 'Base URL cannot be empty.';
                        statusDiv.innerHTML = '<div class="ai-alert ai-alert-danger show">Base URL cannot be empty.</div>';
                        setTimeout(() => fadeOutStatus(statusDiv), 3000);
                        return; // Stop execution if validation fails
                    }

                    // Basic validation - show warnings but don't block execution
                    try {
                        const url = new URL(baseUrl);
                        if (!url.protocol.startsWith('http')) {
                            baseUrlErrorDiv.textContent = 'Warning: URL should start with http:// or https://';
                        }
                        if (!baseUrl.endsWith('/v1')) {
                            baseUrlErrorDiv.textContent = 'Warning: URL should typically end with /v1';
                        }
                    } catch (e) {
                        baseUrlErrorDiv.textContent = 'Warning: Invalid URL format';
                    }
                }
            }

            const apiKeyValue = apiKeyInput ? apiKeyInput.value.trim() : '';
            const hasStoredMask = apiKeyInput && apiKeyInput.value === '••••••••••••••••••••••';
            if (apiKeyInput && (apiKeyValue.length > 0 || hasStoredMask)) {
                this.classList.add('loading');
                console.log('Refresh button clicked, fetching models for:', provider);
                fetchModels(provider, baseUrl, () => { // Correctly pass baseUrl and callback
                    this.classList.remove('loading');
                });
            } else {
                statusDiv.innerHTML = '<div class="ai-alert ai-alert-warning show">Please enter an API key first.</div>';
                setTimeout(() => fadeOutStatus(statusDiv), 3000);
            }
        });
    });

    function fadeOutStatus(statusDiv) {
        const alert = statusDiv.querySelector('.ai-alert');
        if (alert) {
            alert.classList.remove('show');
            alert.classList.add('fade');
            setTimeout(() => { statusDiv.innerHTML = ''; }, 300);
        }
    }

    // Test connection buttons
    document.querySelectorAll('.test-connection-btn').forEach(button => {
        button.addEventListener('click', function() {
            const provider = this.dataset.provider;
            testConnection(provider);
        });
    });

    // Format provider options with better styling
    function formatProviderOption(provider) {
        if (!provider.id) {
            return provider.text;
        }

        // Get provider icon
        let icon = '';
        switch(provider.id) {
            case 'openrouter':
                icon = '<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="2" y="2" width="20" height="8" rx="2" ry="2"></rect><rect x="2" y="14" width="20" height="8" rx="2" ry="2"></rect><line x1="6" y1="6" x2="6.01" y2="6"></line><line x1="6" y1="18" x2="6.01" y2="18"></line></svg>';
                break;
            case 'openai_compatible':
                icon = '<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"></path><line x1="12" y1="17" x2="12.01" y2="17"></line></svg>';
                break;
            case 'none':
                icon = '<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><line x1="8" y1="12" x2="16" y2="12"></line></svg>';
                break;
            default:
                icon = '<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><line x1="12" y1="8" x2="12" y2="12"></line><line x1="12" y1="16" x2="12.01" y2="16"></line></svg>';
        }

        return $(`<div class="provider-option">
            <div class="provider-icon">${icon}</div>
            <div class="provider-name">${provider.text}</div>
        </div>`);
    }

    // Format selected provider
    function formatProviderSelection(provider) {
        if (!provider.id) {
            return provider.text;
        }

        // Get provider icon
        let icon = '';
        switch(provider.id) {
            case 'openrouter':
                icon = '<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="2" y="2" width="20" height="8" rx="2" ry="2"></rect><rect x="2" y="14" width="20" height="8" rx="2" ry="2"></rect><line x1="6" y1="6" x2="6.01" y2="6"></line><line x1="6" y1="18" x2="6.01" y2="18"></line></svg>';
                break;
            case 'openai_compatible':
                icon = '<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"></path><line x1="12" y1="17" x2="12.01" y2="17"></line></svg>';
                break;
            case 'none':
                icon = '<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><line x1="8" y1="12" x2="16" y2="12"></line></svg>';
                break;
            default:
                icon = '<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><line x1="12" y1="8" x2="12" y2="12"></line><line x1="12" y1="16" x2="12.01" y2="16"></line></svg>';
        }

        return $(`<div class="provider-selection">
            <div class="provider-icon">${icon}</div>
            <span class="provider-name">${provider.text}</span>
        </div>`);
    }

    // Format model options with better styling and provider-specific placeholders
    function formatModelOption(model) {
        if (!model.id) {
            return model.text;
        }

        // Extract model family/type if possible (e.g., "gpt-4" from "gpt-4-turbo")
        let modelFamily = '';
        const modelParts = model.text.split('-');
        if (modelParts.length > 1) {
            modelFamily = modelParts.slice(0, 2).join('-');
        }

        return $(`<div class="model-option">
            <div class="model-name">${model.text}</div>
            ${modelFamily ? `<div class="model-description">Family: ${modelFamily}</div>` : ''}
        </div>`);
    }

    // Format selected model
    function formatModelSelection(model) {
        if (!model.id) {
            return model.text;
        }

        // For selected model, just show the name without extra details
        return $(`<span class="selected-model-name">${model.text}</span>`);
    }

    // Debounce function to limit how often a function can be called
    function debounce(func, wait) {
        let timeout;
        return function() {
            const context = this;
            const args = arguments;
            clearTimeout(timeout);
            timeout = setTimeout(() => {
                func.apply(context, args);
            }, wait);
        };
    }

    // Handle form submission
    document.querySelectorAll('.ai-form').forEach(form => {
        form.addEventListener('submit', function(e) {
            const provider = this.querySelector('input[name="provider"]').value;
            const apiKey = this.querySelector('.api-key-input');

            if (apiKey && apiKey.value === '••••••••••••••••••••••') {
                apiKey.disabled = true;
            }
        });
    });

    // Add subtle animation to AI settings cards
    document.querySelectorAll('.ai-settings-card').forEach((card, index) => {
        setTimeout(() => {
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, 100 * index);
    });

    // Handle rate limiting toggle
    document.querySelectorAll('.rate-limiting-toggle').forEach(toggle => {
        toggle.addEventListener('change', function() {
            const provider = this.dataset.provider;
            const settingsDiv = document.getElementById(`${provider}-rate-limiting-settings`);

            if (this.checked) {
                settingsDiv.style.display = 'block';
                // Add a subtle animation
                settingsDiv.style.opacity = '0';
                setTimeout(() => {
                    settingsDiv.style.opacity = '1';
                }, 10);
            } else {
                settingsDiv.style.opacity = '0';
                setTimeout(() => {
                    settingsDiv.style.display = 'none';
                }, 300);
            }
        });
    });
});

// Fetch models for a provider
function fetchModels(provider, baseUrl = null, callback) {
    // Ensure the refresh button gives immediate feedback
    const refreshBtn = document.querySelector(`.model-refresh-btn[data-provider="${provider}"]`);
    if (refreshBtn) refreshBtn.classList.add('loading');
    const select = document.getElementById(`${provider}-model`);
    const statusDiv = document.getElementById(`${provider}-connection-status`);

    if (!select) return;

    // Get API key for the request
    const apiKeyInput = document.getElementById(`${provider}-api-key`);
    let apiKey = null;
    
    // Debug the input field
    console.log('API Key Input:', {
        element: apiKeyInput,
        value: apiKeyInput ? apiKeyInput.value : 'no element',
        valueLength: apiKeyInput ? apiKeyInput.value.length : 0,
        isMasked: apiKeyInput ? (apiKeyInput.value === '••••••••••••••••••••••') : false
    });
    
    if (apiKeyInput && apiKeyInput.value && apiKeyInput.value.trim() !== '') {
        const inputValue = apiKeyInput.value.trim();
        // If it's the masked value, we need to check if there's a real API key stored
        // For now, we'll assume masked value means there IS an API key available
        if (inputValue === '••••••••••••••••••••••') {
            // For masked values, we'll send a special indicator to the backend
            // The backend should use the stored API key from database
            apiKey = 'USE_STORED_KEY';
        } else {
            // User has entered a new API key
            apiKey = inputValue;
        }
    }

    // Debug logging
    console.log('fetchModels called with:', { provider, baseUrl, apiKey: apiKey ? 'present' : 'missing', actualApiKey: apiKey });

    // Determine the URL to fetch models from
    let fetchUrl = `/api/admin/ai-settings/models?provider=${provider}`;
    if (provider === 'openai_compatible') {
        // Always get the current base URL from the input field
        if (!baseUrl) {
            const baseUrlInput = document.getElementById('openai_compatible-base-url');
            if (baseUrlInput && baseUrlInput.value.trim()) {
                baseUrl = baseUrlInput.value.trim();
            }
        }
        
        // Always send base URL if available
        if (baseUrl) {
            fetchUrl += `&base_url=${encodeURIComponent(baseUrl)}`;
        }
        
        // Always send API key if available
        if (apiKey) {
            fetchUrl += `&api_key=${encodeURIComponent(apiKey)}`;
        }
        
        // Show error if API key is missing for OpenAI Compatible
        if (!apiKey) {
            statusDiv.innerHTML = '<div class="ai-alert ai-alert-danger show">Please enter an API key first.</div>';
            if (refreshBtn) refreshBtn.classList.remove('loading');
            if (typeof callback === 'function') callback();
            return;
        }
        
        // Show error if base URL is missing for OpenAI Compatible
        if (!baseUrl) {
            statusDiv.innerHTML = '<div class="ai-alert ai-alert-danger show">Please enter a base URL first.</div>';
            if (refreshBtn) refreshBtn.classList.remove('loading');
            if (typeof callback === 'function') callback();
            return;
        }
    }
    console.log('Final fetch URL:', fetchUrl);

    // Show loading state
    statusDiv.innerHTML = '<div class="ai-alert ai-alert-info show">Fetching available models...</div>';
    if (refreshBtn) refreshBtn.classList.add('loading');

    fetch(fetchUrl)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Clear existing options except the first one
                const firstOption = select.options[0];
                const selectedValue = select.value;
                select.innerHTML = '';
                select.appendChild(firstOption);

                // Add new options
                if (data.models && data.models.length > 0) {
                    // Sort models alphabetically
                    data.models.sort((a, b) => a.name.localeCompare(b.name));

                    data.models.forEach(model => {
                        const option = document.createElement('option');
                        option.value = model.id;
                        option.textContent = model.name;
                        option.dataset.contextWindow = model.context_window;
                        select.appendChild(option);
                    });

                    // Restore selected value if it exists
                    if (selectedValue) {
                        select.value = selectedValue;
                    }

                    // Refresh select2
                    $(select).trigger('change');

                    statusDiv.innerHTML = `<div class="ai-alert ai-alert-success show">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" style="margin-right: 8px;">
                            <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                            <polyline points="22 4 12 14.01 9 11.01"></polyline>
                        </svg>
                        ${data.models.length} models loaded successfully.
                    </div>`;
                } else {
                    // Clear models dropdown when no models found
                    const firstOption = document.createElement('option');
                    firstOption.value = '';
                    firstOption.textContent = 'Select a model';
                    select.innerHTML = '';
                    select.appendChild(firstOption);
                    $(select).trigger('change');
                    
                    statusDiv.innerHTML = '<div class="ai-alert ai-alert-warning show">No models found for this provider.</div>';
                }

                // Hide status after 3 seconds
                setTimeout(() => {
                    const alert = statusDiv.querySelector('.ai-alert');
                    if (alert) {
                        alert.classList.remove('show');
                        alert.classList.add('fade');
                        setTimeout(() => {
                            statusDiv.innerHTML = '';
                        }, 300);
                    }
                }, 3000);
            } else {
                // Clear models dropdown on error
                const firstOption = select.options[0];
                select.innerHTML = '';
                select.appendChild(firstOption);
                $(select).trigger('change');
                
                let friendly = data.message || 'Unknown error';
                if (provider === 'openai_compatible') {
                    friendly += ' Please ensure your API key and Base URL are correct.';
                }
                statusDiv.innerHTML = `<div class="ai-alert ai-alert-danger show">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" style="margin-right: 8px;">
                        <circle cx="12" cy="12" r="10"></circle>
                        <line x1="12" y1="8" x2="12" y2="12"></line>
                        <line x1="12" y1="16" x2="12.01" y2="16"></line>
                    </svg>
                    Failed to fetch models: ${friendly}
                </div>`;
                
                // Auto-hide error after 5 seconds
                setTimeout(() => {
                    const alert = statusDiv.querySelector('.ai-alert');
                    if (alert) {
                        alert.classList.remove('show');
                        alert.classList.add('fade');
                        setTimeout(() => {
                            statusDiv.innerHTML = '';
                        }, 300);
                    }
                }, 5000);
            }
        })
        .catch(error => {
            console.error('fetchModels error:', error);
            
            // Clear models dropdown on network error
            const firstOption = select.options[0];
            select.innerHTML = '';
            select.appendChild(firstOption);
            $(select).trigger('change');
            
            statusDiv.innerHTML = `<div class="ai-alert ai-alert-danger show">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" style="margin-right: 8px;">
                    <circle cx="12" cy="12" r="10"></circle>
                    <line x1="12" y1="8" x2="12" y2="12"></line>
                    <line x1="12" y1="16" x2="12.01" y2="16"></line>
                </svg>
                Network error while fetching models: ${error.message || 'Connection failed'}
            </div>`;
            
            // Auto-hide error after 5 seconds
            setTimeout(() => {
                const alert = statusDiv.querySelector('.ai-alert');
                if (alert) {
                    alert.classList.remove('show');
                    alert.classList.add('fade');
                    setTimeout(() => {
                        statusDiv.innerHTML = '';
                    }, 300);
                }
            }, 5000);
        })
        .finally(() => {
            // Remove loading state from refresh button
            if (refreshBtn) refreshBtn.classList.remove('loading');

            // Execute callback if provided
            if (typeof callback === 'function') {
                callback();
            }
        });
}

// Update context window based on selected model
function updateContextWindow(provider, modelId) {
    if (!modelId) return;

    const select = document.getElementById(`${provider}-model`);
    const contextWindowInput = document.getElementById(`${provider}-context-window`);

    // Find the selected option
    const selectedOption = Array.from(select.options).find(option => option.value === modelId);

    // Only update the context window if it's not OpenAI Compatible
    // For OpenAI Compatible, the user can set their own value
    if (provider !== 'openai_compatible' && selectedOption && selectedOption.dataset.contextWindow) {
        contextWindowInput.value = selectedOption.dataset.contextWindow;
    }
}

// Test connection to a provider
function testConnection(provider) {
    const statusDiv = document.getElementById(`${provider}-connection-status`);
    const testButton = document.querySelector(`.test-connection-btn[data-provider="${provider}"]`);
    const apiKeyInput = document.getElementById(`${provider}-api-key`);
    const baseUrlInput = document.getElementById('openai_compatible-base-url');
    const baseUrlErrorDiv = document.getElementById('openai_compatible-base-url-error');

    // Check if API key is entered
    if (!apiKeyInput || !apiKeyInput.value) {
        statusDiv.innerHTML = `<div class="ai-alert ai-alert-warning show">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <circle cx="12" cy="12" r="10"></circle>
                <line x1="12" y1="8" x2="12" y2="12"></line>
                <line x1="12" y1="16" x2="12.01" y2="16"></line>
            </svg>
            Please enter an API key first.
        </div>`;

        setTimeout(() => {
            const alert = statusDiv.querySelector('.ai-alert');
            if (alert) {
                alert.classList.remove('show');
                alert.classList.add('fade');
                setTimeout(() => {
                    statusDiv.innerHTML = '';
                }, 300);
            }
        }, 3000);

        return;
    }

    // For openai_compatible, validate base URL before testing connection
    if (provider === 'openai_compatible') {
        const currentUrl = baseUrlInput.value.trim();
        baseUrlErrorDiv.textContent = ''; // Clear previous errors

        if (!currentUrl) {
            baseUrlErrorDiv.textContent = 'Base URL cannot be empty.';
            statusDiv.innerHTML = `<div class="ai-alert ai-alert-danger show">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <circle cx="12" cy="12" r="10"></circle>
                    <line x1="15" y1="9" x2="9" y2="15"></line>
                    <line x1="9" y1="9" x2="15" y2="15"></line>
                </svg>
                Connection failed: Base URL is empty.
            </div>`;
            return;
        }

        try {
            const url = new URL(currentUrl);
            if (!url.protocol.startsWith('http')) {
                baseUrlErrorDiv.textContent = 'Invalid URL: Must start with http:// or https://';
                statusDiv.innerHTML = `<div class="ai-alert ai-alert-danger show">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <circle cx="12" cy="12" r="10"></circle>
                        <line x1="15" y1="9" x2="9" y2="15"></line>
                        <line x1="9" y1="9" x2="15" y2="15"></line>
                    </svg>
                    Connection failed: Invalid URL protocol.
                </div>`;
                return;
            }
            if (!currentUrl.endsWith('/v1')) {
                baseUrlErrorDiv.textContent = 'URL must end with /v1';
                statusDiv.innerHTML = `<div class="ai-alert ai-alert-danger show">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <circle cx="12" cy="12" r="10"></circle>
                        <line x1="15" y1="9" x2="9" y2="15"></line>
                        <line x1="9" y1="9" x2="15" y2="15"></line>
                    </svg>
                    Connection failed: URL must end with /v1.
                </div>`;
                return;
            }
        } catch (e) {
            baseUrlErrorDiv.textContent = 'Invalid URL format.';
            statusDiv.innerHTML = `<div class="ai-alert ai-alert-danger show">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <circle cx="12" cy="12" r="10"></circle>
                    <line x1="15" y1="9" x2="9" y2="15"></line>
                    <line x1="9" y1="9" x2="15" y2="15"></line>
                </svg>
                Connection failed: Invalid URL format.
            </div>`;
            return;
        }
    }

    // Disable button and show loading
    testButton.disabled = true;
    const originalButtonText = testButton.innerHTML;
    testButton.innerHTML = `
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="loading-icon">
            <path d="M23 4v6h-6"></path>
            <path d="M1 20v-6h6"></path>
            <path d="M3.51 9a9 9 0 0 1 14.85-3.36L23 10"></path>
            <path d="M1 14l4.64 4.36A9 9 0 0 0 20.49 15"></path>
        </svg>
        Testing...
    `;

    statusDiv.innerHTML = `<div class="ai-alert ai-alert-info show">
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <circle cx="12" cy="12" r="10"></circle>
            <polyline points="12 6 12 12 16 14"></polyline>
        </svg>
        Testing connection to ${getProviderDisplayName(provider)}...
    </div>`;

    let fetchUrl = `/api/admin/ai-settings/test-connection?provider=${provider}`;
    if (provider === 'openai_compatible' && baseUrlInput) {
        fetchUrl += `&base_url=${encodeURIComponent(baseUrlInput.value.trim())}`;
        // Also send the API key for OpenAI Compatible
        const apiKeyValue = apiKeyInput ? apiKeyInput.value.trim() : '';
        if (apiKeyInput && apiKeyValue && apiKeyValue !== '••••••••••••••••••••••') {
            fetchUrl += `&api_key=${encodeURIComponent(apiKeyValue)}`;
        }
    }

    fetch(fetchUrl)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                statusDiv.innerHTML = `<div class="ai-alert ai-alert-success show">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                        <polyline points="22 4 12 14.01 9 11.01"></polyline>
                    </svg>
                    Connection successful! Provider verified.
                </div>`;

                // Add verified badge to tab if not already there
                const tab = document.getElementById(`${provider}-tab`);
                if (tab && !tab.querySelector('.verified-badge')) {
                    tab.innerHTML += '<span class="verified-badge"><svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"><polyline points="20 6 9 17 4 12"></polyline></svg></span>';
                }

                // Fetch models after successful connection
                fetchModels(provider, baseUrlInput ? baseUrlInput.value.trim() : null);
            } else {
                statusDiv.innerHTML = `<div class="ai-alert ai-alert-danger show">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <circle cx="12" cy="12" r="10"></circle>
                        <line x1="15" y1="9" x2="9" y2="15"></line>
                        <line x1="9" y1="9" x2="15" y2="15"></line>
                    </svg>
                    Connection failed: ${data.message}
                </div>`;
            }
        })
        .catch(error => {
            statusDiv.innerHTML = `<div class="ai-alert ai-alert-danger show">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <circle cx="12" cy="12" r="10"></circle>
                    <line x1="15" y1="9" x2="9" y2="15"></line>
                    <line x1="9" y1="9" x2="15" y2="15"></line>
                </svg>
                Error: ${error.message}
            </div>`;
        })
        .finally(() => {
            // Restore button
            testButton.disabled = false;
            testButton.innerHTML = originalButtonText;
        });
}

// Helper function to get provider display name (duplicated from PHP for JS use)
function getProviderDisplayName(provider) {
    const names = {
        'none': 'None',
        'openrouter': 'OpenRouter',
        'openai_compatible': 'OpenAI Compatible'
    };

    return names[provider] || provider.charAt(0).toUpperCase() + provider.slice(1);
}
</script>
