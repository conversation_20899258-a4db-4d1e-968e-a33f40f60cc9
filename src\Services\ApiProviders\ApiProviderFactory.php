<?php

namespace Brenzley\Services\ApiProviders;

use Brenzley\Models\ApiSettingsModel;

/**
 * Factory for creating API provider instances
 */
class ApiProviderFactory
{
    /**
     * Create an API provider instance based on provider name
     *
     * @param string $provider Provider name
     * @param array $settings Provider settings
     * @return ApiProviderInterface|null
     */
    public static function create(string $provider, array $settings = []): ?ApiProviderInterface
    {
        $apiKey = $settings['api_key'] ?? '';
        $selectedModel = $settings['selected_model'] ?? null;
        $contextWindow = (int)($settings['context_window'] ?? 8096);
        $providerSettings = $settings['settings'] ?? [];

        switch ($provider) {
            case 'openai':
                return new OpenAiProvider($apiKey, $selectedModel, $contextWindow, $providerSettings);

            case 'anthropic':
                return new AnthropicProvider($apiKey, $selectedModel, $contextWindow, $providerSettings);


            case 'openrouter':
                return new OpenRouterProvider($apiKey, $selectedModel, $contextWindow, $providerSettings);

            case 'openai_compatible':
                $baseUrl = $settings['base_url'] ?? '';
                return new OpenAiCompatibleProvider($apiKey, $baseUrl, $selectedModel, $contextWindow, $providerSettings);

            default:
                return null;
        }
    }

    /**
     * Create an API provider instance from the database
     *
     * @param string $provider Provider name
     * @param string|null $baseUrl Optional base URL for OpenAI compatible providers
     * @return ApiProviderInterface|null
     */
    public static function createFromDatabase(string $provider, ?string $baseUrl = null): ?ApiProviderInterface
    {
        $apiSettingsModel = new ApiSettingsModel();
        $settings = $apiSettingsModel->findByProvider($provider);

        if (!$settings) {
            return null;
        }

        $apiKey = $apiSettingsModel->getDecryptedApiKey($provider) ?? '';

        // Convert settings from JSON if needed
        if (isset($settings['settings']) && is_string($settings['settings'])) {
            $settings['settings'] = json_decode($settings['settings'], true) ?? [];
        }

        return self::create($provider, [
            'api_key' => $apiKey,
            'selected_model' => $settings['selected_model'] ?? null,
            'context_window' => (int)($settings['context_window'] ?? 8096),
            'base_url' => $baseUrl ?? $settings['base_url'] ?? '', // Use provided baseUrl if available, otherwise from settings
            'settings' => $settings['settings'] ?? []
        ]);
    }
}
