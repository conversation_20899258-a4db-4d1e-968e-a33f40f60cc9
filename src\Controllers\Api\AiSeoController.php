<?php

declare(strict_types=1);

namespace Brenzley\Controllers\Api;

use <PERSON><PERSON><PERSON>y\Core\Session;
use B<PERSON>zley\Services\Prompts\PromptManager;

class AiSeoController extends AiContentController
{
    /**
     * Stream SEO metadata generation using SSE-style messages over a POST fetch
     * Expected JSON body: { title: string, content: string, parameters?: array }
     */
    public function stream(): void
    {
        // Authentication
        if (!Session::isLoggedIn()) {
            send_sse_message('error', 'Authentication required');
            exit;
        }
        // Method
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            send_sse_message('error', 'Invalid request method');
            exit;
        }

        // Headers for SSE-like streaming
        header('Content-Type: text/event-stream');
        header('Cache-Control: no-cache');
        header('Connection: keep-alive');
        header('X-Accel-Buffering: no');
        if (ob_get_level()) ob_end_clean();

        // Parse body
        $data = json_decode(file_get_contents('php://input'), true) ?: [];
        $title = trim($data['title'] ?? '');
        $content = (string)($data['content'] ?? '');
        $parameters = $data['parameters'] ?? [];

        if ($title === '' || $content === '') {
            send_sse_message('error', 'Title and content are required');
            exit;
        }

        // Get active provider
        $activeProvider = $this->apiSettingsModel->getActiveProvider();
        if (!$activeProvider) {
            send_sse_message('error', 'No active AI provider configured');
            exit;
        }

        // Create API provider instance
        $apiProvider = $this->createApiProvider($activeProvider);
        if (!$apiProvider) {
            send_sse_message('error', 'Failed to create API provider');
            exit;
        }

        // Build prompts
        $prompts = PromptManager::getSeoMetadataPrompts($title, $content, $parameters);
        $options = $parameters;
        $options['system_prompt'] = $prompts['system'] ?? '';

        $collected = '';

        try {
            // Try native streaming via provider
            send_sse_message('phase', ['step' => 1, 'message' => 'Analyzing content structure...']);
            $apiProvider->streamCompletion($prompts['user'] ?? '', function($chunk) use (&$collected) {
                $collected .= $chunk;
                send_sse_message('chunk', $chunk);
            }, $options);

            send_sse_message('phase', ['step' => 2, 'message' => 'Extracting metadata...']);
            $seo = $this->parseSeoFromText($collected);

            // Minimal validation
            if (empty($seo['meta_title']) && !empty($title)) { $seo['meta_title'] = mb_substr($title, 0, 60); }
            if (empty($seo['slug']) && !empty($title)) { $seo['slug'] = $this->slugify($title); }

            send_sse_message('end', [ 'seo_metadata' => $seo ]);
        } catch (\Throwable $e) {
            // Fallback: non-streaming generation, then stream the result in small parts
            send_sse_message('phase', ['step' => 1, 'message' => 'Provider streaming unavailable, computing suggestions...']);
            try {
                $generator = \Brenzley\Services\ApiProviders\ContentGeneratorFactory::createFromActiveProvider();
                if (!$generator) {
                    throw new \RuntimeException('No active AI provider configured');
                }
                $fullPrompts = PromptManager::getSeoMetadataPrompts($title, $content, $parameters);
                $fullPrompt = ($fullPrompts['system'] ?? '') . "\n\n" . ($fullPrompts['user'] ?? '');
                $result = $generator->generateSeoMetadata($fullPrompt, $parameters);
                $seo = $result['seo_metadata'] ?? [];
                if (empty($seo) || !is_array($seo)) {
                    throw new \RuntimeException('Failed to generate SEO metadata');
                }
                // Stream synthetic chunks to keep UI responsive
                $jsonOut = json_encode($seo, JSON_UNESCAPED_UNICODE);
                $parts = str_split($jsonOut, 120);
                foreach ($parts as $p) {
                    send_sse_message('chunk', $p);
                    usleep(80000); // 80ms pacing
                }
                send_sse_message('phase', ['step' => 2, 'message' => 'Finalizing results...']);
                send_sse_message('end', ['seo_metadata' => $seo]);
            } catch (\Throwable $inner) {
                send_sse_message('error', 'Error: ' . $inner->getMessage());
            }
        }
        exit;
    }

    // Copy of createApiProvider helper from AiBlogIdeaController
    private function createApiProvider(array $provider): ?\Brenzley\Services\ApiProviders\StreamingApiProviderInterface
    {
        $apiKey = $this->apiSettingsModel->getDecryptedApiKey($provider['provider']) ?? '';
        if (empty($apiKey)) {
            return null;
        }
        $settings = [
            'api_key' => $apiKey,
            'selected_model' => $provider['selected_model'] ?? null,
            'context_window' => (int)($provider['context_window'] ?? 8096),
            'settings' => json_decode($provider['settings'] ?? '{}', true) ?? []
        ];
        return \Brenzley\Services\ApiProviders\ApiProviderFactory::create($provider['provider'], $settings);
    }

    private function parseSeoFromText(string $text): array
    {
        // Try JSON first
        $json = $this->extractJson($text);
        if ($json) {
            $d = json_decode($json, true);
            if (is_array($d)) {
                return [
                    'meta_title' => trim((string)($d['meta_title'] ?? '')),
                    'meta_description' => trim((string)($d['meta_description'] ?? '')),
                    'focus_keywords' => array_values(array_filter(array_map('trim', is_array($d['focus_keywords'] ?? null) ? $d['focus_keywords'] : explode(',', (string)($d['focus_keywords'] ?? ''))))),
                    'slug' => trim((string)($d['slug'] ?? '')),
                ];
            }
        }
        // Fallback: heuristic extraction
        $meta = [ 'meta_title' => '', 'meta_description' => '', 'focus_keywords' => [], 'slug' => '' ];
        if (preg_match('/meta\s*title:?\s*(.+?)(?:\n|$)/i', $text, $m)) { $meta['meta_title'] = trim($m[1]); }
        if (preg_match('/meta\s*description:?\s*(.+?)(?:\n|$)/i', $text, $m)) { $meta['meta_description'] = trim($m[1]); }
        if (preg_match('/focus\s*keywords:?\s*(.+?)(?:\n|$)/i', $text, $m)) { $meta['focus_keywords'] = array_map('trim', explode(',', trim($m[1]))); }
        if (preg_match('/slug:?\s*(.+?)(?:\n|$)/i', $text, $m)) { $meta['slug'] = trim($m[1]); }
        return $meta;
    }

    private function extractJson(string $text): ?string
    {
        $clean = preg_replace('/```[a-zA-Z]*\n?|```/m', '', $text);
        $start = strpos($clean, '{');
        $end = strrpos($clean, '}');
        if ($start === false || $end === false || $end <= $start) {
            return null;
        }
        $json = substr($clean, $start, $end - $start + 1);
        $json = trim($json);
        $json = preg_replace('/,\s*}/', '}', $json);
        $json = preg_replace('/,\s*\]/', ']', $json);
        return $json;
    }

    private function slugify(string $text): string
    {
        $slug = strtolower($text);
        $slug = preg_replace('/[^a-z0-9]+/', '-', $slug) ?? '';
        return trim($slug, '-') ?: 'post';
    }

    /**
     * Save SEO metadata for an AI blog post
     * Expected JSON: { ai_blog_post_id, meta_title, meta_description, slug, focus_keywords }
     */
    public function save(): void
    {
        if (!Session::isLoggedIn()) {
            $this->jsonResponse(['success' => false, 'message' => 'Authentication required'], 401);
            return;
        }
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->jsonResponse(['success' => false, 'message' => 'Invalid request method'], 405);
            return;
        }
        $data = json_decode(file_get_contents('php://input'), true) ?: [];
        $postId = (int)($data['ai_blog_post_id'] ?? 0);
        if ($postId <= 0) {
            $this->jsonResponse(['success' => false, 'message' => 'ai_blog_post_id required'], 400);
            return;
        }
        $userId = Session::getUserId();

        // Basic payload
        $metaTitle = trim((string)($data['meta_title'] ?? ''));
        $metaDescription = trim((string)($data['meta_description'] ?? ''));
        $slug = trim((string)($data['slug'] ?? ''));
        $focusKeywords = $data['focus_keywords'] ?? [];
        if (!is_array($focusKeywords)) {
            // Support CSV
            $focusKeywords = array_values(array_filter(array_map('trim', explode(',', (string)$focusKeywords))));
        }

        // Upsert metadata
        $activeProvider = $this->apiSettingsModel->getActiveProvider();
        $existing = $this->seoModel->findByContent($postId, 'ai_blog_post');
        if ($existing) {
            $ok = $this->seoModel->update((int)$existing['id'], [
                'meta_title' => $metaTitle,
                'meta_description' => $metaDescription,
                'focus_keywords' => $focusKeywords,
                'slug' => $slug,
                'provider' => $activeProvider['provider'] ?? 'manual',
                'model' => $activeProvider['model'] ?? 'manual',
            ]);
            if (!$ok) {
                $this->jsonResponse(['success' => false, 'message' => 'Failed to update SEO metadata'], 500);
                return;
            }
            $id = (int)$existing['id'];
        } else {
            $id = $this->seoModel->create([
                'user_id' => $userId,
                'content_id' => $postId,
                'content_type' => 'ai_blog_post',
                'meta_title' => $metaTitle,
                'meta_description' => $metaDescription,
                'focus_keywords' => $focusKeywords,
                'slug' => $slug,
                'provider' => $activeProvider['provider'] ?? 'manual',
                'model' => $activeProvider['model'] ?? 'manual',
                'parameters' => []
            ]);
            if (!$id) {
                $this->jsonResponse(['success' => false, 'message' => 'Failed to save SEO metadata'], 500);
                return;
            }
        }

        $this->jsonResponse(['success' => true, 'metadata_id' => $id, 'saved_at' => date('c')]);
    }
}
