<?php

namespace Brenzley\Services\ApiProviders;

/**
 * OpenRouter API Provider
 * Uses OpenAI Compatible Provider implementation since OpenRouter follows the same API format
 */
class OpenRouterProvider extends OpenAiCompatibleProvider
{
    /**
     * Constructor
     *
     * @param string $apiKey API key for the provider
     * @param string|null $selectedModel Selected model for the provider
     * @param int $contextWindow Context window size for the model
     * @param array $settings Additional provider-specific settings
     */
    public function __construct(string $apiKey, ?string $selectedModel = null, int $contextWindow = 8096, array $settings = [])
    {
        // Call parent constructor with OpenRouter-specific base URL
        parent::__construct($apiKey, 'https://openrouter.ai/api/v1', $selectedModel, $contextWindow, $settings);
    }

    /**
     * Get the provider name
     *
     * @return string The provider name
     */
    protected function getProviderName(): string
    {
        return 'openrouter';
    }

    /**
     * Get additional headers specific to OpenRouter
     *
     * @return array Additional headers
     */
    protected function getAdditionalHeaders(): array
    {
        $headers = parent::getAdditionalHeaders();
        
        // Add OpenRouter-specific headers
        $headers['HTTP-Referer'] = $this->getSiteUrl();
        $headers['X-Title'] = $this->getSiteName();
        
        return $headers;
    }

    /**
     * Get site name for OpenRouter headers
     *
     * @return string Site name
     */
    private function getSiteName(): string
    {
        return $_ENV['OPENROUTER_SITE_NAME'] ?? 'Brenzley Blog';
    }

    /**
     * Get site URL for OpenRouter headers
     *
     * @return string Site URL
     */
    private function getSiteUrl(): string
    {
        return $_ENV['OPENROUTER_SITE_URL'] ?? 'https://brenzley.com';
    }
}