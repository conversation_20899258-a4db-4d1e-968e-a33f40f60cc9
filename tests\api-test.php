<?php

// Simple test script for API providers
require_once __DIR__ . '/../vendor/autoload.php';

// Load configuration
require_once __DIR__ . '/../config/config.php';

use Brenzley\Services\ApiProviders\ApiProviderFactory;

// Test function
function testProvider(string $provider, array $settings = []): void {
    echo "Testing $provider provider...\n";
    
    // Create provider instance
    $apiProvider = ApiProviderFactory::create($provider, $settings);
    
    if (!$apiProvider) {
        echo "Error: Failed to create provider instance.\n";
        return;
    }
    
    // Test connection
    echo "Testing connection...\n";
    $result = $apiProvider->testConnection();
    
    echo "Success: " . ($result['success'] ? 'Yes' : 'No') . "\n";
    echo "Message: " . $result['message'] . "\n";
    
    if ($result['success']) {
        // Test getting models
        echo "\nFetching models...\n";
        $modelsResult = $apiProvider->getModels();
        
        if ($modelsResult['success']) {
            echo "Found " . count($modelsResult['models']) . " models:\n";
            foreach ($modelsResult['models'] as $index => $model) {
                if ($index < 5) { // Show only first 5 models
                    echo "- {$model['name']} (Context window: {$model['context_window']})\n";
                }
            }
            
            if (count($modelsResult['models']) > 5) {
                echo "... and " . (count($modelsResult['models']) - 5) . " more models\n";
            }
        } else {
            echo "Failed to fetch models: " . $modelsResult['message'] . "\n";
        }
    }
    
    echo "\n";
}

// Get API keys from environment or command line
$openrouterApiKey = $argv[1] ?? getenv('OPENROUTER_API_KEY') ?? '';
$openaiCompatibleApiKey = $argv[2] ?? getenv('OPENAI_COMPATIBLE_API_KEY') ?? '';
$openaiCompatibleBaseUrl = $argv[3] ?? getenv('OPENAI_COMPATIBLE_BASE_URL') ?? 'https://api.openai.com/';

// Test OpenRouter
if (!empty($openrouterApiKey)) {
    testProvider('openrouter', [
        'api_key' => $openrouterApiKey
    ]);
} else {
    echo "Skipping OpenRouter test (no API key provided)\n\n";
}

// Test OpenAI Compatible
if (!empty($openaiCompatibleApiKey) && !empty($openaiCompatibleBaseUrl)) {
    testProvider('openai_compatible', [
        'api_key' => $openaiCompatibleApiKey,
        'base_url' => $openaiCompatibleBaseUrl
    ]);
} else {
    echo "Skipping OpenAI Compatible test (no API key or base URL provided)\n\n";
}

echo "Tests completed.\n";
