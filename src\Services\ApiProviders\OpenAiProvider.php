<?php

namespace Brenzley\Services\ApiProviders;

/**
 * OpenAI API Provider
 */
class OpenAiProvider extends AbstractApiProvider
{
    /**
     * API base URL
     */
    private const API_BASE_URL = 'https://api.openai.com/v1';

    /**
     * Get the provider name
     *
     * @return string The provider name
     */
    protected function getProviderName(): string
    {
        return 'openai';
    }

    /**
     * Test the connection to the OpenAI API
     *
     * @return array Success status and message
     */
    public function testConnection(): array
    {
        $url = self::API_BASE_URL . '/models';
        $headers = [
            'Content-Type: application/json',
            'Authorization: Bearer ' . $this->apiKey
        ];

        $result = $this->makeRequest($url, 'GET', $headers);

        if (!empty($result['error'])) {
            return [
                'success' => false,
                'message' => 'Connection error: ' . $result['error'],
                'details' => [
                    'provider' => 'openai',
                    'error_type' => 'connection',
                    'error' => $result['error']
                ]
            ];
        }

        if ($result['http_code'] >= 200 && $result['http_code'] < 300) {
            return [
                'success' => true,
                'message' => 'Successfully connected to OpenAI API',
                'details' => [
                    'provider' => 'openai',
                    'http_code' => $result['http_code']
                ]
            ];
        }

        $errorMessage = $this->parseErrorResponse($result['response']);
        return [
            'success' => false,
            'message' => 'API error: ' . $errorMessage,
            'details' => [
                'provider' => 'openai',
                'error_type' => 'api',
                'http_code' => $result['http_code'],
                'error' => $errorMessage
            ]
        ];
    }

    /**
     * Get available models from the OpenAI API
     *
     * @return array Success status, message, and models list
     */
    public function getModels(): array
    {
        $url = self::API_BASE_URL . '/models';
        $headers = [
            'Content-Type: application/json',
            'Authorization: Bearer ' . $this->apiKey
        ];

        $result = $this->makeRequest($url, 'GET', $headers);

        if (!empty($result['error'])) {
            return [
                'success' => false,
                'message' => 'Connection error: ' . $result['error']
            ];
        }

        if ($result['http_code'] >= 200 && $result['http_code'] < 300) {
            $data = json_decode($result['response'], true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                return [
                    'success' => false,
                    'message' => 'Invalid response from API'
                ];
            }

            if (!isset($data['data']) || !is_array($data['data'])) {
                return [
                    'success' => false,
                    'message' => 'Invalid response format from API'
                ];
            }

            // Filter and format models
            $models = [];
            foreach ($data['data'] as $model) {
                // Only include chat models
                if (strpos($model['id'], 'gpt') !== false) {
                    $models[] = [
                        'id' => $model['id'],
                        'name' => $model['id'],
                        'description' => $model['id'],
                        'context_window' => $this->getContextWindowForModel($model['id'])
                    ];
                }
            }

            return [
                'success' => true,
                'message' => 'Successfully retrieved models',
                'models' => $models
            ];
        }

        $errorMessage = $this->parseErrorResponse($result['response']);
        return [
            'success' => false,
            'message' => 'Failed to fetch models: ' . $errorMessage
        ];
    }

    /**
     * Generate a completion using the OpenAI API
     *
     * @param string $prompt The prompt to generate a completion for
     * @param array $options Additional options for the completion
     * @return array Success status, message, and completion result
     */
    public function generateCompletion(string $prompt, array $options = []): array
    {
        if (empty($this->selectedModel)) {
            return [
                'success' => false,
                'message' => 'No model selected'
            ];
        }

        $url = self::API_BASE_URL . '/chat/completions';
        $headers = [
            'Content-Type: application/json',
            'Authorization: Bearer ' . $this->apiKey
        ];

        // Prepare request data
        $data = [
            'model' => $this->selectedModel,
            'messages' => [
                [
                    'role' => 'user',
                    'content' => $prompt
                ]
            ],
            'temperature' => $options['temperature'] ?? 0.7,
            'top_p' => $options['top_p'] ?? 1.0,
            'frequency_penalty' => $options['frequency_penalty'] ?? 0.0,
            'presence_penalty' => $options['presence_penalty'] ?? 0.0
        ];

        $data = array_filter($data, function ($value) {
            return $value !== null;
        });

        $result = $this->makeRequest($url, 'POST', $headers, $data, 'chat/completions', $this->estimatePromptTokens($prompt));

        if (!empty($result['error'])) {
            return [
                'success' => false,
                'message' => 'Connection error: ' . $result['error']
            ];
        }

        if ($result['http_code'] >= 200 && $result['http_code'] < 300) {
            $data = json_decode($result['response'], true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                return [
                    'success' => false,
                    'message' => 'Invalid response from API'
                ];
            }

            if (!isset($data['choices'][0]['message']['content'])) {
                return [
                    'success' => false,
                    'message' => 'Invalid response format from API'
                ];
            }

            $completion = $data['choices'][0]['message']['content'];
            $usage = $data['usage'] ?? null;

            return [
                'success' => true,
                'message' => 'Successfully generated completion',
                'completion' => $completion,
                'usage' => $usage
            ];
        }

        $errorMessage = $this->parseErrorResponse($result['response']);
        return [
            'success' => false,
            'message' => 'Failed to generate completion: ' . $errorMessage
        ];
    }

    /**
     * Stream a completion using the OpenAI API
     *
     * @param string $prompt The prompt to generate a completion for
     * @param callable $callback Callback function to handle each chunk of the stream
     * @param array $options Additional options for the completion
     * @return void
     */
    public function streamCompletion(string $prompt, callable $callback, array $options = []): void
    {
        if (empty($this->selectedModel)) {
            throw new \RuntimeException('No model selected');
        }

        $url = self::API_BASE_URL . '/chat/completions';
        $headers = [
            'Content-Type: application/json',
            'Authorization: Bearer ' . $this->apiKey
        ];

        // Prepare request data
        $data = [
            'model' => $this->selectedModel,
            'messages' => [
                [
                    'role' => 'user',
                    'content' => $prompt
                ]
            ],
            'temperature' => $options['temperature'] ?? 0.7,
            'top_p' => $options['top_p'] ?? 1.0,
            'frequency_penalty' => $options['frequency_penalty'] ?? 0.0,
            'presence_penalty' => $options['presence_penalty'] ?? 0.0,
            'stream' => true
        ];

        $data = array_filter($data, function ($value) {
            return $value !== null;
        });

        // Execute streaming request
        $this->executeStreamingRequest($url, $data, $headers, $callback);
    }

    /**
     * Process a chunk of streaming data from OpenAI
     *
     * @param string $chunk The data chunk
     * @param callable $callback Callback function to handle the processed chunk
     * @return void
     */
    protected function processStreamChunk(string $chunk, callable $callback): void
    {
        if ($chunk === '') {
            return;
        }

        // OpenAI sends "data: " prefixed chunks
        if (strpos($chunk, 'data: ') === 0) {
            $chunk = substr($chunk, 6); // Remove "data: " prefix

            // Skip "[DONE]" message
            if (trim($chunk) === '[DONE]') {
                // Log completion
                Logger::debug('OpenAI stream completed', [
                    'provider' => $this->getProviderName(),
                    'message' => 'Received [DONE] message'
                ]);
                return;
            }

            // Parse JSON
            $data = json_decode($chunk, true);
            if (json_last_error() === JSON_ERROR_NONE) {
                // Log the parsed data for debugging
                Logger::debug('OpenAI chunk parsed', [
                    'provider' => $this->getProviderName(),
                    'data' => $data
                ]);

                if (isset($data['choices'][0]['delta']['content'])) {
                    $content = $data['choices'][0]['delta']['content'];
                    $callback($content);
                } else {
                    // Log when we receive a chunk without content
                    Logger::debug('OpenAI chunk without content', [
                        'provider' => $this->getProviderName(),
                        'data' => $data
                    ]);

                    // This might be a role or other metadata, not an error
                    if (isset($data['choices'][0]['delta']['role'])) {
                        Logger::debug('OpenAI role delta received', [
                            'provider' => $this->getProviderName(),
                            'role' => $data['choices'][0]['delta']['role']
                        ]);
                    }
                }
            } else {
                // Log JSON parsing errors
                Logger::warning('OpenAI JSON parse error', [
                    'provider' => $this->getProviderName(),
                    'error' => json_last_error_msg(),
                    'chunk' => $chunk
                ]);
            }
        } else {
            // Log unexpected format
            Logger::warning('OpenAI unexpected chunk format', [
                'provider' => $this->getProviderName(),
                'chunk_start' => substr($chunk, 0, 50)
            ]);
        }
    }

    /**
     * Estimate the number of tokens in a prompt
     *
     * @param string $prompt The prompt
     * @return int Estimated number of tokens
     */
    private function estimatePromptTokens(string $prompt): int
    {
        // Rough estimate: 1 token per 4 characters
        return (int)(strlen($prompt) / 4);
    }

    /**
     * Get the context window size for a model
     *
     * @param string $modelId The model ID
     * @return int The context window size
     */
    private function getContextWindowForModel(string $modelId): int
    {
        $contextWindows = [
            'gpt-4' => 8192,
            'gpt-4-32k' => 32768,
            'gpt-4-turbo' => 128000,
            'gpt-4-turbo-preview' => 128000,
            'gpt-4-vision-preview' => 128000,
            'gpt-3.5-turbo' => 4096,
            'gpt-3.5-turbo-16k' => 16384
        ];

        // Check for exact match
        if (isset($contextWindows[$modelId])) {
            return $contextWindows[$modelId];
        }

        // Check for partial match
        foreach ($contextWindows as $model => $window) {
            if (strpos($modelId, $model) === 0) {
                return $window;
            }
        }

        // Default
        return 4096;
    }
}
