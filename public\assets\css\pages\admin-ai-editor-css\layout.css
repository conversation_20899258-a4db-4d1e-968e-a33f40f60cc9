/**
 * AI Editor - Layout & Container Components
 * Contains main container, header, and layout structure styles
 */

/* ========================================
   MAIN CONTAINER & LAYOUT COMPONENTS
   ======================================== */

/* Main Container */
.ai-editor-container {
    display: flex;
    flex-direction: column;
    background-color: var(--admin-bg);
    border-radius: 0.5rem;
    box-shadow: var(--ai-shadow-sm);
    overflow: hidden;
    margin-bottom: 2rem;
}

/* Header */

/* Completion Toast */
.ai-completion-toast { position: fixed; right: 16px; bottom: 16px; z-index: 9999; background: var(--admin-surface); border:1px solid var(--admin-border); border-radius: var(--border-radius-md); box-shadow: var(--ai-shadow-sm); padding: .75rem 1rem; max-width: 380px; display: flex; }
.ai-completion-toast.hidden { display: none; }
.ai-toast-content { display:flex; align-items:center; gap:.75rem; }
.ai-toast-text { color: var(--admin-text-primary); font-size: .95rem; }
.ai-toast-close { background: transparent; border: none; color: var(--admin-text-secondary); font-size: 1.1rem; cursor: pointer; }
.ai-toast-close:hover { color: var(--admin-text-primary); }

/* Header */
.ai-editor-header {
    display: none; /* Initially hidden until content step */
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background-color: var(--admin-surface);
    border-bottom: 1px solid var(--admin-border);
    position: relative;
    flex-wrap: wrap;
    gap: 0.5rem;
    transition: all 0.3s ease;
}

/* Show header when content step is active */
.ai-editor-header.show-header {
    display: flex;
}

.ai-editor-header-left,
.ai-editor-header-right {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.ai-editor-header-center {
    font-size: 0.875rem;
    color: var(--admin-text-secondary);
    flex: 1;
    text-align: center;
    min-width: 0;
}

/* Mobile header hamburger menu */
.ai-header-mobile-menu {
    display: none;
    background: none;
    border: none;
    color: var(--admin-text-primary);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 0.375rem;
    transition: background-color 0.2s;
}

.ai-header-mobile-menu:hover {
    background-color: var(--admin-surface-alt);
}

.ai-header-mobile-menu svg {
    width: 20px;
    height: 20px;
}

/* Mobile header dropdown */
.ai-header-mobile-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background-color: var(--admin-surface);
    border: 1px solid var(--admin-border);
    border-top: none;
    border-radius: 0 0 0.5rem 0.5rem;
    box-shadow: var(--ai-shadow-lg);
    z-index: 1000;
    display: none;
    padding: 1rem;
    gap: 0.75rem;
    flex-direction: column;
}

.ai-header-mobile-dropdown.show {
    display: flex;
}

.ai-header-mobile-actions {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    justify-content: center;
}

/* Main Content Area */
.ai-editor-main {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1rem;
    padding: 1rem;
    height: calc(100vh - 200px);
    min-height: 600px;
}

/* Article Creation Process */
.ai-creation-process {
    display: flex;
    flex-direction: column;
    background-color: var(--admin-surface);
    border-radius: 0.5rem;
    border: 1px solid var(--admin-border);
    overflow: hidden;
}

/* Step Indicator */
.ai-process-steps {
    display: flex;
    justify-content: space-between;
    padding: 1rem;
    background-color: var(--admin-bg);
    border-bottom: 1px solid var(--admin-border);
}

.ai-process-step {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    width: 100%;
}

.ai-process-step:not(:last-child)::after {
    content: '';
    position: absolute;
    top: 1rem;
    right: -50%;
    width: 100%;
    height: 2px;
    background-color: var(--admin-border);
    z-index: 1;
}

.ai-process-step.active:not(:last-child)::after {
    background-color: var(--ai-primary);
}

.ai-process-step.completed:not(:last-child)::after {
    background-color: var(--ai-success);
}

.ai-step-number {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2rem;
    height: 2rem;
    border-radius: 50%;
    background-color: var(--admin-surface);
    border: 2px solid var(--admin-border);
    color: var(--admin-text-secondary);
    font-weight: 600;
    margin-bottom: 0.5rem;
    z-index: 2;
}

.ai-process-step.active .ai-step-number {
    background-color: var(--ai-primary);
    border-color: var(--ai-primary);
    color: white;
}

.ai-process-step.completed .ai-step-number {
    background-color: var(--ai-success);
    border-color: var(--ai-success);
    color: white;
}

.ai-step-label {
    font-size: 0.75rem;
    font-weight: 500;
    color: var(--admin-text-secondary);
}

.ai-process-step.active .ai-step-label {
    color: var(--ai-primary);
    font-weight: 600;
}

.ai-process-step.completed .ai-step-label {
    color: var(--ai-success);
}

/* Step Content */
.ai-process-content {
    flex: 1;
    overflow-y: auto;
    padding: 1rem;
}

.ai-process-panel {
    display: none;
}

.ai-process-panel.active {
    display: block;
    animation: fadeIn 0.3s ease;
}

.ai-panel-header {
    margin-bottom: 1.5rem;
    text-align: center;
}

.ai-panel-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--admin-text-primary);
    margin-bottom: 0.5rem;
}

.ai-panel-description {
    font-size: 0.875rem;
    color: var(--admin-text-secondary);
}

/* Panel Actions */
.ai-panel-actions {
    display: flex;
    justify-content: space-between;
    gap: 1rem;
    margin-top: 1.5rem;
}

/* Title Section */
.ai-title-section {
    margin-bottom: 1rem;
}

.ai-title-input {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--admin-border);
    border-radius: 0.375rem;
    background-color: var(--admin-bg);
    color: var(--admin-text-primary);
    font-size: 1.25rem;
    font-weight: 600;
    transition: border-color 0.2s, box-shadow 0.2s;
}

.ai-title-input:focus {
    outline: none;
    border-color: var(--ai-primary);
    box-shadow: 0 0 0 2px rgba(var(--ai-primary-rgb), 0.3);
}

/* Featured Image Section */
.ai-featured-image-section {
    margin-bottom: 1.5rem;
}

.ai-section-title {
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 0.75rem;
    color: var(--admin-text-primary);
}

.featured-image-preview-container {
    margin-bottom: 1rem;
    min-height: 0;
}

.featured-image-preview {
    border: 1px solid var(--admin-border);
    border-radius: 0.375rem;
    overflow: hidden;
    background-color: var(--admin-surface-alt);
    margin-bottom: 1rem;
}

.featured-image-preview img {
    width: 100%;
    max-height: 300px;
    object-fit: contain;
    display: block;
}

.featured-image-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0.75rem;
    background-color: var(--admin-surface);
    border-top: 1px solid var(--admin-border);
}

.featured-image-name {
    font-size: 0.875rem;
    color: var(--admin-text-secondary);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 80%;
}

.remove-featured-image-btn {
    background: none;
    border: none;
    color: var(--ai-danger);
    font-size: 1.25rem;
    line-height: 1;
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 0.25rem;
    transition: background-color 0.2s;
}

.remove-featured-image-btn:hover {
    background-color: rgba(var(--ai-danger-rgb), 0.1);
}

/* Content Area (Editable) */
.ai-content-area {
    min-height: 300px;
    max-height: 500px;
    overflow-y: auto;
    padding: 1rem;
    border: 1px solid var(--admin-border);
    border-radius: 0.375rem;
    background-color: var(--admin-bg);
    color: var(--admin-text-primary);
    font-size: 0.875rem;
    line-height: 1.6;
}

.ai-content-area code {
    background-color: rgba(var(--admin-border-rgb), 0.4);
    padding: 0.2em 0.4em;
    margin: 0;
    font-size: 85%;
    border-radius: 3px;
    font-family: "SFMono-Regular", Consolas, "Liberation Mono", Menlo, Courier, monospace;
}

.ai-content-area blockquote {
    padding: 0 1em;
    color: var(--admin-text-secondary);
    border-left: 0.25em solid var(--admin-border);
    margin-left: 0;
    margin-right: 0;
}

.ai-content-area ul,
.ai-content-area ol {
    padding-left: 2em;
}

.ai-content-area:focus {
    outline: none;
    border-color: var(--ai-primary);
    box-shadow: 0 0 0 2px rgba(var(--ai-primary-rgb), 0.3);
}

.ai-content-area[data-placeholder]:empty:before {
    content: attr(data-placeholder);
    color: var(--admin-text-secondary);
    font-style: italic;
}

.ai-content-area::after {
    content: "";
    clear: both;
    display: table;
}

/* Modern clearfix for content area */
.ai-content-area {
    display: flow-root;
}