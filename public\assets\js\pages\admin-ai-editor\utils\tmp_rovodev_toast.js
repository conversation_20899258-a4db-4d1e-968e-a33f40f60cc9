export function mountCompletionToast(container) {
  if (!container) return null;
  if (container.querySelector('.ai-completion-toast')) return container.querySelector('.ai-completion-toast');

  const toast = document.createElement('div');
  toast.className = 'ai-completion-toast';
  toast.innerHTML = `
    <div class="ai-toast-content">
      <div class="ai-toast-text">All steps are complete. Publish functionality to be implemented.</div>
      <button class="ai-toast-close" aria-label="Close">×</button>
    </div>
  `;
  container.appendChild(toast);
  const close = toast.querySelector('.ai-toast-close');
  close.addEventListener('click', () => toast.classList.add('hidden'));
  return toast;
}
