<?php

declare(strict_types=1);

use Phinx\Migration\AbstractMigration;
use Phinx\Db\Adapter\MysqlAdapter;

final class AlterAiBlogPostsAddMissingColumns extends AbstractMigration
{
    public function change(): void
    {
        if (!$this->hasTable('ai_blog_posts')) {
            return;
        }
        $table = $this->table('ai_blog_posts');

        if (!$table->hasColumn('blog_idea_id')) {
            $table->addColumn('blog_idea_id', 'integer', ['signed' => false, 'null' => true]);
        }
        if (!$table->hasColumn('outline_id')) {
            $table->addColumn('outline_id', 'integer', ['signed' => false, 'null' => true]);
        }
        if (!$table->hasColumn('content_html')) {
            $table->addColumn('content_html', 'text', ['null' => true, 'limit' => MysqlAdapter::TEXT_LONG]);
        }

        $table->save();
    }
}
