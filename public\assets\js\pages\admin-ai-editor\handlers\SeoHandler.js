import { showNotification, stripHtml } from '../utils/EditorUtils.js';

/**
 * Handles the SEO Optimization step.
 */
export class SeoHandler {
    openImageZoomModal(src) {
        if (!src) return;
        const overlay = document.createElement('div');
        overlay.className = 'ai-image-zoom-overlay';
        overlay.style.cssText = 'position:fixed;inset:0;background:rgba(0,0,0,.65);display:flex;align-items:center;justify-content:center;z-index:10000;';
        const wrap = document.createElement('div');
        wrap.style.cssText = 'max-width:90vw;max-height:90vh;position:relative;';
        const img = document.createElement('img');
        img.src = src;
        img.alt = 'Preview';
        img.style.cssText = 'max-width:90vw;max-height:90vh;border-radius:8px;box-shadow:0 6px 20px rgba(0,0,0,.4);';
        const close = document.createElement('button');
        close.textContent = '×';
        close.setAttribute('aria-label', 'Close');
        close.style.cssText = 'position:absolute;top:-12px;right:-12px;width:32px;height:32px;border:none;border-radius:50%;background:white;color:#111;font-size:20px;cursor:pointer;box-shadow:0 2px 8px rgba(0,0,0,.3);';
        wrap.appendChild(img); wrap.appendChild(close); overlay.appendChild(wrap); document.body.appendChild(overlay);
        const closeFn = () => { try { document.body.removeChild(overlay); } catch {} };
        close.addEventListener('click', closeFn);
        overlay.addEventListener('click', (e) => { if (e.target === overlay) closeFn(); });
        document.addEventListener('keydown', function onKey(e){ if(e.key==='Escape'){ closeFn(); document.removeEventListener('keydown', onKey); } });
    }
    constructor(uiManager, stateManager, chatFeature) {
        this.uiManager = uiManager;
        this.stateManager = stateManager;
        this.chatFeature = chatFeature;
        this.elements = uiManager.elements;
        this.hasRunOnce = false;
    }

    init() {
        if (!this.elements.seoRunBtn) return;
        this.elements.seoRunBtn.addEventListener('click', () => this.runSeo());
        if (this.elements.seoApplyBtn) {
            this.elements.seoApplyBtn.addEventListener('click', () => this.applySeoToForm());
        }
        // Tabs logic
        if (this.elements.seoTabs) {
            this.elements.seoTabs.querySelectorAll('.ai-tab').forEach(tab => {
                tab.addEventListener('click', () => this.switchSeoTab(tab.dataset.tab));
            });
        }
        if (this.elements.seoTabContinueToAlts) {
            this.elements.seoTabContinueToAlts.addEventListener('click', () => {
                this.switchSeoTab('alts');
                // If we have no images, show a friendly empty state
                const hasItems = this.elements.seoImageAlts && this.elements.seoImageAlts.children.length > 0;
                if (!hasItems && this.elements.seoNoImages) {
                    this.elements.seoNoImages.style.display = 'block';
                }
            });
        }
    }

    /**
     * Prepare mock data and run SEO when the step is opened directly for testing.
     */
    autoRunIfNeeded() {
        if (this.hasRunOnce) return;
        // If we jumped directly to SEO via dev shortcut, auto-run.
        const step = this.stateManager.getStateValue('currentStep');
        if (step === 'seo') {
            this.runSeo();
            this.hasRunOnce = true;
        }
    }

    async runSeo() {
       // Hydrate saved SEO meta and alt suggestions when available
       try {
           const aiId = this.stateManager.getStateValue('aiBlogPostId');
           if (aiId) {
               const res = await fetch(`/api/admin/ai/blog-posts/get?id=${aiId}`);
               const data = await res.json().catch(() => ({}));
               if (data?.success) {
                   const seo = data.seo_metadata || {};
                   if (this.elements.seoMetaTitle) this.elements.seoMetaTitle.value = seo.meta_title || '';
                   if (this.elements.seoMetaDescription) this.elements.seoMetaDescription.value = seo.meta_description || '';
                   if (this.elements.seoSlug) this.elements.seoSlug.value = seo.slug || '';
                   if (Array.isArray(seo.focus_keywords) && this.elements.seoKeywordsChips) {
                       this.elements.seoKeywordsChips.innerHTML = '';
                       seo.focus_keywords.forEach(kw => {
                           const chip = document.createElement('span');
                           chip.className = 'ai-chip';
                           chip.textContent = kw;
                           this.elements.seoKeywordsChips.appendChild(chip);
                       });
                   }
                   // If post.parameters.image_layouts exists, pre-render alts list with any existing alt_text
                   const layouts = (data.post?.parameters?.image_layouts) || [];
                   if (Array.isArray(layouts) && layouts.length && this.elements.seoImageAlts) {
                       const hydrated = layouts.map((l, i) => ({
                           placeholderId: l.id || `image-${i+1}`,
                           layout: l.layout || 'standard',
                           description: l.description || '',
                           alt: l.alt_text || (l.description || ''),
                           imageUrl: null,
                           existingAlt: l.alt_text || '',
                           mediaId: l.media_id || null
                       }));
                       this.renderSeoResults({
                           metaTitle: this.elements.seoMetaTitle?.value || '',
                           metaDescription: this.elements.seoMetaDescription?.value || '',
                           focusKeywords: Array.from(this.elements.seoKeywordsChips?.querySelectorAll('.ai-chip') || []).map(c => c.textContent),
                           slug: this.elements.seoSlug?.value || ''
                       }, hydrated);
                   }
               }
           }
       } catch (e) {
           // Non-fatal hydration errors
       }
        this.isStreaming = false;
        // Reset UI
        this.resetSeoUI();
        this.showProgress();

        const title = this.stateManager.getStateValue('title') || 'Comprehensive Markdown and Image Layout Test';
        const contentHtml = this.getContentOrMock();

        // Extract image descriptions to generate alt texts
        const imageInfos = this.extractImageInfos(contentHtml);

        // Simulate streaming progress UI
        const phases = [
            'Analyzing content structure...',
            'Extracting keywords and entities...',
            'Generating meta title and description...',
            'Optimizing URL slug...',
            'Generating image alt texts...'
        ];
        this.streamProgress(phases);

        // Try streaming API first; fallback to non-streaming JSON; else to local mock
        let seoResult = null;
        try {
            seoResult = await this.requestSeoStreaming(title, stripHtml(contentHtml));
            this.isStreaming = true;
        } catch (e) {
            console.warn('SEO streaming failed, trying JSON endpoint:', e);
            try {
                seoResult = await this.requestSeoFromBackend(title, stripHtml(contentHtml));
            } catch (e2) {
                console.warn('SEO JSON failed, falling back to mock:', e2);
            }
        }
        if (!seoResult) {
            seoResult = this.generateMockSeoResult(title, stripHtml(contentHtml));
        }

        // Generate alt texts from image descriptions; include imageUrl and existingAlt if available
        const altTexts = imageInfos.map((info, idx) => ({
            placeholderId: info.id,
            layout: info.layout,
            description: info.description,
            alt: this.createAltFromDescription(info.description, idx),
            imageUrl: info.imageUrl || null,
            existingAlt: info.existingAlt || '',
            mediaId: info.mediaId || null
        }));

        // Small delay to complete the streaming feel when not using SSE
        if (!this.isStreaming) {
            await new Promise(r => setTimeout(r, 600));
        }
        // Render meta first
        this.renderSeoResults(seoResult, []);
        this.chatFeature.addAssistantMessage('SEO meta generated. Now generating image alt texts...');

        // Auto-switch to alt texts tab and generate them
        setTimeout(() => {
            this.switchSeoTab('alts');
            if (imageInfos && imageInfos.length > 0) {
                const descriptions = imageInfos.map(info => info.description || '');
                this.generateAltTextsByDescriptions(descriptions).then(generatedAlts => {
                    const finalAltList = imageInfos.map((info, i) => ({
                        placeholderId: info.id,
                        layout: info.layout,
                        description: info.description,
                        alt: generatedAlts[i] || this.createAltFromDescription(info.description, i),
                        imageUrl: info.imageUrl || null,
                        existingAlt: info.existingAlt || '',
                        mediaId: info.mediaId || null
                    }));
                    this.renderSeoResults(seoResult, finalAltList);
                });
            } else {
                if (this.elements.seoNoImages) this.elements.seoNoImages.style.display = 'block';
            }
        }, 500); // Small delay for smooth transition

        // Mark SEO step complete
        this.stateManager.updateState('seoCompleted', true);
        const headerStep = this.stateManager.getStateValue('currentStep');
        // Trigger header visibility update to potentially show Publish now
        window.aiArticleCreator?.updateHeaderVisibility?.(headerStep);
        this.chatFeature.addAssistantMessage('SEO optimization is complete.');
    }

    getContentOrMock() {
        // Prefer current editor content if available
        const contentEl = this.elements.contentArea;
        if (contentEl && contentEl.innerHTML.trim()) {
            return contentEl.innerHTML;
        }
        // Fallback mock content including IMAGE tags
        return `
        <h2>Introduction</h2>
        <p>This article showcases the editor and image layouts.</p>
        <p>
        <IMAGE alt_text="Sunrise over mountains" layout="standard">
          <description>Upload a sunrise mountain landscape with warm light and misty valleys.</description>
        </IMAGE>
        </p>
        <h2>Section One</h2>
        <p>Content about features.</p>
        <p>
        <IMAGE alt_text="Diagram of workflow" layout="left">
          <description>Upload a clean flowchart explaining the content creation workflow.</description>
          <additional_content>Text that wraps around the floated image describing steps.</additional_content>
        </IMAGE>
        </p>
        <h2>Gallery</h2>
        <p>
        <IMAGE alt_text="Two product views" layout="gallery-2-item">
          <description>Upload a wide product shot on a white background.</description>
          <description>Upload a close-up highlighting texture and details.</description>
        </IMAGE>
        </p>`;
    }

    extractImageInfos(html) {
        // Prefer real placeholders from content area if present
        const infos = [];
        const placeholders = this.elements.contentArea ? this.elements.contentArea.querySelectorAll('.ai-image-placeholder') : [];
        // Try to resolve a usable image URL for each placeholder: prefer <img.generated-image>, else attached media by ID

        if (placeholders && placeholders.length > 0) {
            placeholders.forEach((el, idx) => {
                const description = (el.dataset.description || '').trim();
                const layout = (el.dataset.layout || 'standard').trim();
                const id = el.id || `seo-image-${Date.now()}-${idx+1}`;
                let imgEl = el.querySelector('img.generated-image');
                let imageUrl = imgEl ? imgEl.src : null;
                let existingAlt = imgEl && imgEl.alt ? imgEl.alt.trim() : '';
                const mediaId = el.dataset.mediaId ? Number(el.dataset.mediaId) : null;
                // If no <img> present but we have a mediaId, try to resolve from global media cache if available
                if (!imageUrl && mediaId && window.__mediaCache && window.__mediaCache[mediaId]) {
                    imageUrl = window.__mediaCache[mediaId].url || null;
                    existingAlt = window.__mediaCache[mediaId].alt_text || existingAlt || '';
                }
                if (description) {
                    infos.push({ id, layout, description, imageUrl, existingAlt, mediaId });
                }
            });
            if (infos.length > 0) return infos;
        }
        // Fallback to parsing IMAGE tags from provided HTML
        const fallbackInfos = [];
        const imageRegex = /<IMAGE\s+([^>]*?)>([\s\S]*?)<\/IMAGE>/g;
        let m; let count = 0;
        while ((m = imageRegex.exec(html)) !== null) {
            count++;
            const attrs = m[1] || '';
            const inner = m[2] || '';
            const id = `seo-image-${Date.now()}-${count}`;
            const layout = (attrs.match(/layout\s*=\s*["']([^"']+)["']/) || [])[1] || 'standard';
            const descMatch = inner.match(/<description>([\s\S]*?)<\/description>/);
            const description = descMatch ? stripHtml(descMatch[1].trim()) : '';
            if (description) fallbackInfos.push({ id, layout, description, imageUrl: null, existingAlt: '', mediaId: null });
        }
        return fallbackInfos;
    }

    async streamAltTexts(items) {
        // Ensure alts tab active and UI prepared
        this.switchSeoTab('alts');
        if (this.elements.seoNoImages) this.elements.seoNoImages.style.display = 'none';
        if (this.elements.seoImageAlts) {
            this.elements.seoImageAlts.innerHTML = '';
            items.forEach((item, idx) => {
                const li = document.createElement('li');
                li.className = 'ai-image-alt-item';
                li.dataset.index = String(idx);
                const previewHtml = item.imageUrl ? `<img class=\"ai-image-alt-thumb\" src=\"${item.imageUrl}\" alt=\"preview\" />` : `<div class=\"ai-image-alt-thumb ai-thumb-empty\">📷</div>`;
                const initialValue = item.existingAlt || 'Generating...';
                const disabled = item.existingAlt ? '' : 'disabled';
                li.innerHTML = `
                    <div class="ai-image-alt-left">
                        ${previewHtml}
                    </div>
                    <div class="ai-image-alt-center">
                        <input class="ai-image-alt-input" type="text" value="${initialValue.replace(/"/g, '&quot;')}" ${disabled} />
                        <div class="ai-image-alt-meta">${item.description}</div>
                    </div>
                    <div class="ai-image-alt-right">
                        <button class="ai-image-alt-regenerate ai-btn ai-btn-sm ai-btn-secondary" type="button" data-index="${idx}">Regenerate</button>
                        <span class="ai-image-alt-status">${item.existingAlt ? 'From image' : 'Generating...'}</span>
                    </div>`;
                this.elements.seoImageAlts.appendChild(li);
            });
        }
        const response = await fetch('/api/admin/ai/image-metadata/stream-alts', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json', 'X-Requested-With': 'XMLHttpRequest' },
            body: JSON.stringify({ images: items.map(it => ({ media_id: this.getMediaIdForPlaceholder(it.placeholderId), description: it.description })), parameters: { temperature: 0.2 } })
        });
        if (!response.ok) throw new Error(`HTTP ${response.status}`);
        const reader = response.body.getReader();
        const decoder = new TextDecoder();
        let buffer = '';
        let done = false;
        while (!done) {
            const { value, done: readerDone } = await reader.read();
            done = readerDone;
            buffer += decoder.decode(value || new Uint8Array(), { stream: true });
            let eolIndex;
            while ((eolIndex = buffer.indexOf('\n\n')) !== -1) {
                const message = buffer.substring(0, eolIndex).trim();
                buffer = buffer.substring(eolIndex + 2);
                if (!message.startsWith('data:')) continue;
                const jsonString = message.substring(5).trim();
                if (!jsonString) continue;
                let data;
                try { data = JSON.parse(jsonString); } catch { continue; }
                if (data.type === 'item') {
                    const idx = data.index;
                    const li = this.elements.seoImageAlts?.querySelector(`.ai-image-alt-item[data-index="${idx}"]`);
                    if (!li) continue;
                    const input = li.querySelector('.ai-image-alt-input');
                    const statusEl = li.querySelector('.ai-image-alt-status');
                    const regenBtn = li.querySelector('.ai-image-alt-regenerate');
                    if (data.status === 'ok') {
                        if (input) {
                            input.value = data.alt_text || '';
                            input.disabled = false;
                        }
                        if (statusEl) statusEl.textContent = data.media_id ? 'Saved' : 'Ready';
                        // No save button: autosave occurs in backend when media_id is present
                        if (regenBtn) {
                            regenBtn.onclick = async () => {
                                statusEl.textContent = 'Regenerating...';
                                await this.regenerateSingleAltByDescription(idx, items);
                            };
                        }
                    } else if (data.status === 'skipped') {
                        if (statusEl) statusEl.textContent = 'Skipped (no description)';
                    } else if (data.status === 'error') {
                        if (statusEl) statusEl.textContent = 'Error';
                    }
                }
            }
        }
        return true;
    }

    getMediaIdForPlaceholder(placeholderId) {
        const placeholder = this.elements.contentArea?.querySelector(`#${placeholderId}`);
        return placeholder?.dataset?.mediaId ? Number(placeholder.dataset.mediaId) : null;
    }

    async saveAltTextsToBackend(altTexts) {
        this._lastAltBatch = altTexts.map(a => ({...a}));
        // Map suggestions back to placeholders, check for attached media IDs
        const results = await Promise.all(altTexts.map(async (item, idx) => {
            const placeholder = this.elements.contentArea?.querySelector(`#${item.placeholderId}`);
            const mediaId = placeholder?.dataset?.mediaId || null;
            const statusEl = this.elements.seoImageAlts?.querySelector(`.ai-image-alt-item[data-index="${idx}"] .ai-image-alt-status`);

            // If no mediaId, we still allow alt generation and keep status informative
            if (!mediaId) {
                if (statusEl) statusEl.textContent = 'Ready';
                return { saved: false, reason: 'no_media' };
            }
            try {
                const res = await fetch('/api/admin/ai/image-metadata/generate-alt-text', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json', 'X-Requested-With': 'XMLHttpRequest' },
                    body: JSON.stringify({ media_id: Number(mediaId), image_description: item.description, parameters: { temperature: 0.2 } })
                });
                const data = await res.json().catch(() => ({}));
                if (res.ok && data.success) {
                    if (statusEl) statusEl.textContent = 'Saved';
                    return { saved: true };
                } else {
                    if (statusEl) statusEl.textContent = 'Save failed';
                    return { saved: false, reason: 'api_error', detail: data?.message };
                }
            } catch (e) {
                if (statusEl) statusEl.textContent = 'Save failed';
                return { saved: false, reason: 'network_error' };
            }
        }));
        return results;
    }

    renderSeoRetryAltAll(altTexts) {
        // Add or update a retry-all container below the alt list
        if (!this.elements.seoImageAlts) return;
        let container = this.elements.seoImageAlts.parentElement.querySelector('.ai-retry-container');
        if (!container) {
            container = document.createElement('div');
            container.className = 'ai-retry-container';
            this.elements.seoImageAlts.parentElement.appendChild(container);
        }
        container.innerHTML = '';
        if (altTexts && altTexts.length) {
            const btn = document.createElement('button');
            btn.className = 'ai-btn ai-btn-secondary';
            btn.textContent = 'Save All Again';
            btn.addEventListener('click', () => this.saveAltTextsToBackend(this._lastAltBatch || altTexts));
            container.appendChild(btn);
        }
    }

    async regenerateSingleAltByDescription(index, currentAltBatch) {
        try {
            const item = (currentAltBatch && currentAltBatch[index]) || null;
            const description = item?.description || '';
            const li = this.elements.seoImageAlts?.querySelector(`.ai-image-alt-item[data-index="${index}"]`);
            const input = li?.querySelector('.ai-image-alt-input');
            const statusEl = li?.querySelector('.ai-image-alt-status');
            if (statusEl) statusEl.textContent = 'Regenerating...';
            const alts = await this.generateAltTextsByDescriptions([description]);
            const altText = (alts && alts[0]) || this.createAltFromDescription(description, index);
            if (input) input.value = altText;
            if (statusEl) statusEl.textContent = 'Ready';
            return { ok: true };
        } catch (e) {
            const li = this.elements.seoImageAlts?.querySelector(`.ai-image-alt-item[data-index="${index}"]`);
            const statusEl = li?.querySelector('.ai-image-alt-status');
            if (statusEl) statusEl.textContent = 'Error';
            return { ok: false, error: e?.message };
        }
    }

    async generateAltTextsByDescriptions(descriptions) {
        // Use streaming API for real-time updates
        const self = this;
        return new Promise((resolve, reject) => {
            const results = new Array(descriptions.length);
            let completed = 0;
            
            const eventSource = new EventSource('/api/admin/ai/image-metadata/stream-alts', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json', 'X-Requested-With': 'XMLHttpRequest' },
                body: JSON.stringify({ images: descriptions.map((d, i) => ({ index: i, description: d })) })
            });
            
            // Use fetch with streaming for POST data
            fetch('/api/admin/ai/image-metadata/stream-alts', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json', 'X-Requested-With': 'XMLHttpRequest' },
                body: JSON.stringify({ images: descriptions.map((d, i) => ({ index: i, description: d })) })
            }).then(response => {
                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                
                function readStream() {
                    reader.read().then(({ done, value }) => {
                        if (done) {
                            // Fill any missing results with fallback
                            for (let i = 0; i < descriptions.length; i++) {
                                if (!results[i]) {
                                    results[i] = self.createAltFromDescription(descriptions[i], i);
                                    self.updateSingleAltInUI(i, results[i]);
                                }
                            }
                            resolve(results);
                            return;
                        }
                        
                        const chunk = decoder.decode(value);
                        const lines = chunk.split('\n');
                        
                        lines.forEach(line => {
                            if (line.startsWith('data: ')) {
                                try {
                                    const data = JSON.parse(line.slice(6));
                                    if (data.type === 'item' && typeof data.index === 'number') {
                                        if (data.status === 'ok') {
                                            results[data.index] = data.alt_text || self.createAltFromDescription(descriptions[data.index], data.index);
                                            completed++;
                                            
                                            // Update UI in real-time
                                            self.updateSingleAltInUI(data.index, results[data.index], 'Ready');
                                        } else if (data.status === 'error') {
                                            // Use provided alt_text from error response if available, otherwise create fallback
                                            results[data.index] = data.alt_text || self.createAltFromDescription(descriptions[data.index], data.index);
                                            completed++;
                                            
                                            // Check if it's a rate limit error
                                            const isRateLimit = data.message && (
                                                data.message.includes('rate limit') || 
                                                data.message.includes('请求数限制') ||
                                                data.message.includes('请求频率')
                                            );
                                            
                                            // Update UI with fallback and show error status
                                            self.updateSingleAltInUI(data.index, results[data.index], isRateLimit ? 'Rate Limited' : 'Generated (Fallback)');
                                            
                                            // Log the error for debugging
                                            console.warn(`Alt text generation error for index ${data.index}:`, data.message);
                                        } else if (data.status === 'skipped') {
                                            // Handle skipped items
                                            results[data.index] = self.createAltFromDescription(descriptions[data.index], data.index);
                                            completed++;
                                            self.updateSingleAltInUI(data.index, results[data.index], 'Skipped');
                                        }
                                    } else if (data.type === 'end') {
                                        // Fill any missing results
                                        for (let i = 0; i < descriptions.length; i++) {
                                            if (!results[i]) {
                                                results[i] = self.createAltFromDescription(descriptions[i], i);
                                                self.updateSingleAltInUI(i, results[i]);
                                            }
                                        }
                                        resolve(results);
                                        return;
                                    }
                                } catch (e) {
                                    console.error('Error parsing SSE data:', e);
                                }
                            }
                        });
                        
                        readStream();
                    }).catch(reject);
                }
                
                readStream();
            }).catch(e => {
                // Fallback to local generation
                const fallbackResults = descriptions.map((d, i) => self.createAltFromDescription(d, i));
                fallbackResults.forEach((alt, i) => self.updateSingleAltInUI(i, alt));
                resolve(fallbackResults);
            });
        });
    }
    
    updateSingleAltInUI(index, altText, status = 'Ready') {
        const li = this.elements.seoImageAlts?.querySelector(`.ai-image-alt-item[data-index="${index}"]`);
        if (li) {
            const input = li.querySelector('.ai-image-alt-input');
            if (input) input.value = altText;
        }
    }

    async retrySaveSingleAlt(index, newAlt, currentAltBatch) {
        // Update local batch so subsequent retries use latest values
        if (!this._lastAltBatch || !Array.isArray(this._lastAltBatch)) this._lastAltBatch = currentAltBatch || [];
        if (this._lastAltBatch[index]) {
            this._lastAltBatch[index].alt = newAlt;
        }
        const item = this._lastAltBatch[index];
        const li = this.elements.seoImageAlts?.querySelector(`.ai-image-alt-item[data-index="${index}"]`);
        const statusEl = li?.querySelector('.ai-image-alt-status');
        const placeholder = this.elements.contentArea?.querySelector(`#${item.placeholderId}`);
        const mediaId = placeholder?.dataset?.mediaId || null;
        if (!mediaId) {
            if (statusEl) statusEl.textContent = 'No media attached yet';
            return { saved: false, reason: 'no_media' };
        }
        try {
            const res = await fetch('/api/admin/ai/image-metadata/generate-alt-text', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json', 'X-Requested-With': 'XMLHttpRequest' },
                body: JSON.stringify({ media_id: Number(mediaId), image_description: item.description, parameters: { temperature: 0.2, override_alt: newAlt } })
            });
            const data = await res.json().catch(() => ({}));
            if (res.ok && data.success) {
                if (statusEl) statusEl.textContent = 'Saved';
                return { saved: true };
            } else {
                if (statusEl) statusEl.textContent = 'Save failed';
                return { saved: false, reason: 'api_error', detail: data?.message };
            }
        } catch (e) {
            if (statusEl) statusEl.textContent = 'Save failed';
            return { saved: false, reason: 'network_error' };
        }
    }

    createAltFromDescription(description, idx) {
        if (!description) return `Descriptive image ${idx + 1}`;
        // Basic heuristic: trim and cap length
        let alt = description.replace(/\s+/g, ' ').trim();
        // Remove leading verbs like Upload etc.
        alt = alt.replace(/^Upload\s+/i, '');
        if (alt.length > 120) alt = alt.slice(0, 117) + '...';
        // Lowercase first char not necessary; keep natural language
        return alt;
    }

    async requestSeoFromBackend(title, contentText) {
        try {
            const response = await fetch('/api/admin/content/generate-seo-metadata', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json', 'X-Requested-With': 'XMLHttpRequest' },
                body: JSON.stringify({ title, content: contentText, parameters: { temperature: 0.4 } })
            });
            if (!response.ok) throw new Error(`HTTP ${response.status}`);
            const data = await response.json();
            if (data.success && data.seo_metadata) {
                const meta = data.seo_metadata;
                return {
                    metaTitle: meta.meta_title || title,
                    metaDescription: meta.meta_description || '',
                    focusKeywords: Array.isArray(meta.focus_keywords) ? meta.focus_keywords : (typeof meta.focus_keywords === 'string' ? meta.focus_keywords.split(',').map(s => s.trim()).filter(Boolean) : []),
                    slug: meta.slug || this.slugify(title)
                };
            }
            // If no success or missing payload, throw with message
            throw new Error(data.message || 'SEO API returned no data');
        } catch (e) {
            // Surface inline error
            showNotification(e.message || 'SEO request failed', 'error');
            if (this.elements.seoResults) this.elements.seoResults.classList.remove('visible');
            if (this.elements.seoProgressContainer) this.elements.seoProgressContainer.classList.remove('visible');
            const container = this.elements.seoTabMeta || this.elements.seoResults || this.elements.seoProgressContainer;
            if (container) {
                container.innerHTML = `<div class="ai-error">Error: ${e.message || 'SEO request failed'}</div>`;
                const retryBtn = document.createElement('button');
                retryBtn.className = 'ai-btn ai-btn-secondary';
                retryBtn.textContent = 'Try Again';
                retryBtn.addEventListener('click', () => this.runSeo());
                container.appendChild(retryBtn);
            }
            throw e; // rethrow so caller can fallback
        }
        const response = await fetch('/api/admin/content/generate-seo-metadata', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json', 'X-Requested-With': 'XMLHttpRequest' },
            body: JSON.stringify({ title, content: contentText, parameters: { temperature: 0.4 } })
        });
        if (!response.ok) throw new Error(`HTTP ${response.status}`);
        const data = await response.json();
        if (data.success && data.seo_metadata) {
            const meta = data.seo_metadata;
            return {
                metaTitle: meta.meta_title || title,
                metaDescription: meta.meta_description || '',
                focusKeywords: Array.isArray(meta.focus_keywords) ? meta.focus_keywords : (typeof meta.focus_keywords === 'string' ? meta.focus_keywords.split(',').map(s => s.trim()).filter(Boolean) : []),
                slug: meta.slug || this.slugify(title)
            };
        }
        throw new Error(data.message || 'SEO API returned error');
    }

    async requestSeoStreaming(title, contentText) {
        try {
        // Show a temporary status message for streaming
        if (this.elements.seoProgressContainer) {
            // already showing
        }
        const response = await fetch('/api/admin/ai/seo/stream', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json', 'X-Requested-With': 'XMLHttpRequest' },
            body: JSON.stringify({ title, content: contentText, parameters: { temperature: 0.4 } })
        });
        if (!response.ok) throw new Error(`HTTP ${response.status}`);

        const reader = response.body.getReader();
        const decoder = new TextDecoder();
        let buffer = '';
        let done = false;
        let finalResult = null;

        while (!done) {
            const { value, done: readerDone } = await reader.read();
            done = readerDone;
            buffer += decoder.decode(value || new Uint8Array(), { stream: true });

            let eolIndex;
            while ((eolIndex = buffer.indexOf('\n\n')) !== -1) {
                const message = buffer.substring(0, eolIndex).trim();
                buffer = buffer.substring(eolIndex + 2);

                if (!message.startsWith('data:')) continue;
                const jsonString = message.substring(5).trim();
                if (!jsonString) continue;
                let data;
                try { data = JSON.parse(jsonString); } catch { continue; }

                switch (data.type) {
                    case 'phase':
                        // Optionally update a specific progress row
                        break;
                    case 'chunk':
                        // No-op: UI already shows progress rows animation
                        break;
                    case 'end':
                        finalResult = data.content.seo_metadata;
                        break;
                    case 'error':
                        throw new Error(data.message || 'Streaming error');
                }
            }
        }
        if (!finalResult) throw new Error('Streaming ended without result');
        return {
            metaTitle: finalResult.meta_title,
            metaDescription: finalResult.meta_description,
            focusKeywords: finalResult.focus_keywords,
            slug: finalResult.slug
        };
        } catch (e) {
            // Inline error handling for stream
            showNotification(e.message || 'SEO streaming failed', 'error');
            if (this.elements.seoProgressContainer) this.elements.seoProgressContainer.classList.remove('visible');
            const container = this.elements.seoTabMeta || this.elements.seoResults;
            if (container) {
                container.innerHTML = `<div class="ai-error">Error: ${e.message || 'SEO stream error'}</div>`;
                const retryBtn = document.createElement('button');
                retryBtn.className = 'ai-btn ai-btn-secondary';
                retryBtn.textContent = 'Try Again';
                retryBtn.addEventListener('click', () => this.runSeo());
                container.appendChild(retryBtn);
            }
            throw e;
        }
    }

    generateMockSeoResult(title, contentText) {
        // Simple mock generation using heuristics
        const words = title.split(/\s+/).filter(Boolean);
        const primary = words.slice(0, 3).join(' ');
        const metaTitle = title.length <= 60 ? title : (title.slice(0, 57) + '...');
        const summary = (contentText || '').replace(/\s+/g, ' ').trim().slice(0, 130);
        const metaDescription = summary ? `${summary}...` : `Read our guide on ${title}.`;
        const keywords = [primary, words[0], words.slice(-1)[0], 'guide', 'tips'].filter(Boolean);
        return {
            metaTitle,
            metaDescription: metaDescription.slice(0, 155),
            focusKeywords: Array.from(new Set(keywords)).slice(0, 6),
            slug: this.slugify(title)
        };
    }

    slugify(text) {
        return (text || '')
            .toLowerCase()
            .replace(/[^a-z0-9\s-]/g, '')
            .replace(/[\s_-]+/g, '-')
            .replace(/^-+|-+$/g, '');
    }

    streamProgress(messages) {
        if (!this.elements.seoProgress) return;
        this.elements.seoProgress.innerHTML = '';
        messages.forEach((msg, i) => {
            const row = document.createElement('div');
            row.className = 'ai-seo-progress-row';
            row.innerHTML = `
                <div class="ai-seo-progress-badge">${i + 1}</div>
                <div class="ai-seo-progress-text">${msg}</div>
            `;
            this.elements.seoProgress.appendChild(row);
        });
        // Animate in sequence
        const rows = Array.from(this.elements.seoProgress.querySelectorAll('.ai-seo-progress-row'));
        rows.forEach((row, idx) => {
            row.style.opacity = '0';
            row.style.transform = 'translateY(6px)';
            setTimeout(() => {
                row.style.transition = 'all 220ms var(--transition-normal)';
                row.style.opacity = '1';
                row.style.transform = 'translateY(0)';
            }, 200 * idx);
        });
    }

    showProgress() {
        if (this.elements.seoResults) {
            this.elements.seoResults.classList.remove('visible');
        }
        if (this.elements.seoProgressContainer) {
            this.elements.seoProgressContainer.classList.add('visible');
        }
    }

    switchSeoTab(tab) {
        // Ensure lists visibility toggles and no-images message reset
        if (this.elements.seoNoImages) this.elements.seoNoImages.style.display = 'none';
        const meta = this.elements.seoTabMeta;
        const alts = this.elements.seoTabAlts;
        const tabs = this.elements.seoTabs?.querySelectorAll('.ai-tab') || [];
        tabs.forEach(t => t.classList.toggle('active', t.dataset.tab === tab));
        if (meta && alts) {
            meta.classList.toggle('active', tab === 'meta');
            alts.classList.toggle('active', tab === 'alts');
        }
    }

    async renderSeoResults(seo, altTexts) {
        if (this.elements.seoProgressContainer) {
            this.elements.seoProgressContainer.classList.remove('visible');
        }
        if (this.elements.seoResults) {
            this.elements.seoResults.classList.add('visible');
        }
        if (this.elements.seoMetaTitle) this.elements.seoMetaTitle.value = seo.metaTitle || '';
        if (this.elements.seoMetaDescription) this.elements.seoMetaDescription.value = seo.metaDescription || '';
        if (this.elements.seoSlug) this.elements.seoSlug.value = seo.slug || '';
        if (this.elements.seoKeywordsChips) {
            this.elements.seoKeywordsChips.innerHTML = '';
            (seo.focusKeywords || []).forEach(kw => {
                const chip = document.createElement('span');
                chip.className = 'ai-chip';
                chip.textContent = kw;
                this.elements.seoKeywordsChips.appendChild(chip);
            });
        }
        if (this.elements.seoImageAlts) {
            this.elements.seoImageAlts.innerHTML = '';
            if (!altTexts || altTexts.length === 0) {
                if (this.elements.seoNoImages) this.elements.seoNoImages.style.display = 'block';
            } else {
                altTexts.forEach((item, idx) => {
                    const li = document.createElement('li');
                    li.className = 'ai-image-alt-item';
                    li.dataset.index = String(idx);
                    const thumb = item.imageUrl ? `<img class=\"ai-image-alt-thumb\" src=\"${item.imageUrl}\" alt=\"preview\" \/>` : `<div class=\"ai-thumb-empty\">Image not uploaded<\/div>`;
                    li.innerHTML = `
                        <div class=\"ai-image-alt-left\">
                          <div class=\"ai-image-alt-thumb-wrapper\">${thumb}<\/div>
                        <\/div>
                        <div class=\"ai-image-alt-center\">
                          <div class=\"ai-image-alt-input-wrapper\">
                            <textarea class=\"ai-image-alt-input\" placeholder=\"Enter descriptive alt text...\">${item.alt.replace(/\\\"/g, '&quot;')}<\/textarea>
                          <\/div>
                        <\/div>
                        <div class=\"ai-image-alt-right\">
                          <div class=\"ai-image-alt-actions\">
                            <button class=\"ai-image-alt-regenerate ai-btn ai-btn-sm ai-btn-secondary\" type=\"button\" data-index=\"${idx}\">
                              <span>🔄</span> Regenerate
                            <\/button>
                          <\/div>
                        <\/div>`;
                    this.elements.seoImageAlts.appendChild(li);
                    const imgEl2 = li.querySelector('.ai-image-alt-thumb');
                    if (imgEl2) { imgEl2.addEventListener('click', () => this.openImageZoomModal(imgEl2.src)); }
                });
                // Bind retry handlers
                this.elements.seoImageAlts.querySelectorAll('.ai-image-alt-retry').forEach(btn => {
                    btn.addEventListener('click', async (e) => {
                        const index = Number(e.currentTarget.dataset.index);
                        const li = e.currentTarget.closest('.ai-image-alt-item');
                        if (!li) return;
                        const input = li.querySelector('.ai-image-alt-input');
                        const statusEl = li.querySelector('.ai-image-alt-status');
                        const newAlt = input?.value?.trim() || '';
                        statusEl.textContent = 'Saving...';
                        await this.retrySaveSingleAlt(index, newAlt, altTexts);
                    });
                });
            }
        }
        // Keep result in state for apply
        this.stateManager.updateState('metaTitle', seo.metaTitle || '');
        this.stateManager.updateState('metaDescription', seo.metaDescription || '');
        this.stateManager.updateState('focusKeyword', (seo.focusKeywords && seo.focusKeywords[0]) || '');
        this.stateManager.updateState('secondaryKeywords', (seo.focusKeywords || []).slice(1));
        showNotification('SEO suggestions ready!', 'success');

        // Try to persist alt texts to backend for placeholders that have media IDs
        if (altTexts && altTexts.length) {
           try {
               // Persist alt texts for placeholders without media_id into ai_blog_posts.parameters
               const aiId = this.stateManager.getStateValue('aiBlogPostId');
               if (aiId) {
                   const updates = altTexts.filter(x => !x.mediaId).map(x => ({ id: x.placeholderId, alt_text: x.alt }));
                   if (updates.length) {
                       await fetch('/api/admin/ai/blog-posts/update-parameters', {
                           method: 'POST', headers: { 'Content-Type': 'application/json', 'X-Requested-With': 'XMLHttpRequest' },
                           body: JSON.stringify({ ai_blog_post_id: aiId, image_layouts: updates })
                       });
                   }
               }
           } catch (e) { console.warn('Non-media alt persistence failed', e); }
           this.saveAltTextsToBackend(altTexts)
               .then(() => this.switchSeoTab('alts'))
                .catch(err => {
                    console.warn('Saving alt texts failed:', err);
                    this.renderSeoRetryAltAll(altTexts);
                    this.switchSeoTab('alts');
                });
        } else {
            this.renderSeoRetryAltAll([]); // Ensure retry UI is reset/hidden when no items
            this.switchSeoTab('alts');
            if (this.elements.seoNoImages) this.elements.seoNoImages.style.display = 'block';
        }
    }

    async applySeoToForm() {
        try {
            const aiId = this.stateManager.getStateValue('aiBlogPostId');
            if (aiId) {
                const metaTitle = this.elements.seoMetaTitle?.value || '';
                const metaDescription = this.elements.seoMetaDescription?.value || '';
                const slug = this.elements.seoSlug?.value || '';
                const chips = Array.from(this.elements.seoKeywordsChips?.querySelectorAll('.ai-chip') || []);
                const focusKeywords = chips.map(c => c.textContent.trim()).filter(Boolean);
                await fetch('/api/admin/ai/seo/save', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json', 'X-Requested-With': 'XMLHttpRequest' },
                    body: JSON.stringify({ ai_blog_post_id: aiId, meta_title: metaTitle, meta_description: metaDescription, slug, focus_keywords: focusKeywords })
                });
            }
        } catch (e) {}

        // Update hidden fields for submit
        const state = this.stateManager.getState();
        if (this.elements.hiddenMetaTitle) this.elements.hiddenMetaTitle.value = state.metaTitle;
        if (this.elements.hiddenMetaDescription) this.elements.hiddenMetaDescription.value = state.metaDescription;
        if (this.elements.hiddenFocusKeywords) {
            this.elements.hiddenFocusKeywords.value = JSON.stringify({ focus: state.focusKeyword, secondary: state.secondaryKeywords });
        }
        showNotification('Applied SEO metadata to the post form.', 'success');
        this.chatFeature.addAssistantMessage('Applied SEO metadata to the form. You can now Save Draft. (Publishing functionality will be implemented later)');
    }

    resetSeoUI() {
        if (this.elements.seoKeywordsChips) this.elements.seoKeywordsChips.innerHTML = '';
        if (this.elements.seoImageAlts) this.elements.seoImageAlts.innerHTML = '';
        if (this.elements.seoMetaTitle) this.elements.seoMetaTitle.value = '';
        if (this.elements.seoMetaDescription) this.elements.seoMetaDescription.value = '';
        if (this.elements.seoSlug) this.elements.seoSlug.value = '';
    }
}
