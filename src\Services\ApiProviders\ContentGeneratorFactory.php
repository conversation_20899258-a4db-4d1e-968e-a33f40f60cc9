<?php

namespace Brenzley\Services\ApiProviders;

use Brenzley\Models\ApiSettingsModel;

/**
 * Factory for creating content generator instances
 */
class ContentGeneratorFactory
{
    /**
     * Create a content generator instance based on provider name
     *
     * @param string $provider Provider name
     * @param ApiProviderInterface $apiProvider API provider instance
     * @return ContentGenerationInterface|null
     */
    public static function create(string $provider, ApiProviderInterface $apiProvider): ?ContentGenerationInterface
    {
        switch ($provider) {
                
            case 'openrouter':
                if ($apiProvider instanceof OpenRouterProvider) {
                    return new OpenRouterContentGenerator($apiProvider);
                }
                break;
                
                
            case 'openai_compatible':
                if ($apiProvider instanceof OpenAiCompatibleProvider) {
                    return new OpenAiCompatibleContentGenerator($apiProvider);
                }
                break;
                
            default:
                return null;
        }
        
        return null;
    }
    
    /**
     * Create a content generator instance from the database
     *
     * @param string $provider Provider name
     * @return ContentGenerationInterface|null
     */
    public static function createFromDatabase(string $provider): ?ContentGenerationInterface
    {
        $apiProvider = ApiProviderFactory::createFromDatabase($provider);
        
        if (!$apiProvider) {
            return null;
        }
        
        return self::create($provider, $apiProvider);
    }
    
    /**
     * Create a content generator instance for the active provider
     *
     * @return ContentGenerationInterface|null
     */
    public static function createFromActiveProvider(): ?ContentGenerationInterface
    {
        $apiSettingsModel = new ApiSettingsModel();
        $activeProvider = $apiSettingsModel->getActiveProvider();
        
        if (!$activeProvider || $activeProvider['provider'] === 'none') {
            return null;
        }
        
        return self::createFromDatabase($activeProvider['provider']);
    }
}
