import { StateManager } from './admin-ai-editor/core/StateManager.js';
import { UIManager } from './admin-ai-editor/core/UIManager.js';
import { IdeaHandler } from './admin-ai-editor/handlers/IdeaHandler.js';
import { OutlineHandler } from './admin-ai-editor/handlers/OutlineHandler.js';
import { ContentHandler } from './admin-ai-editor/handlers/ContentHandler.js';
import { SeoHandler } from './admin-ai-editor/handlers/SeoHandler.js';
import { ContentEditorHandler } from './admin-ai-editor/handlers/ContentEditorHandler.js';
import { ChatFeature } from './admin-ai-editor/features/ChatFeature.js';
import { ImageFeature } from './admin-ai-editor/features/ImageFeature.js';
import { SavePublishFeature } from './admin-ai-editor/features/SavePublishFeature.js';
import { MobileUtils } from './admin-ai-editor/utils/MobileUtils.js';
import { showNotification } from './admin-ai-editor/utils/EditorUtils.js';

/**
 * AI-Powered Article Creator
 * A streamlined, step-by-step process for creating AI-generated articles
 */
class AIArticleCreator {
    createOrGetTooltip() {
        let tooltip = document.querySelector('.ai-tooltip');
        if (!tooltip) {
            tooltip = document.createElement('div');
            tooltip.className = 'ai-tooltip';
            tooltip.setAttribute('role', 'tooltip');
            tooltip.style.position = 'absolute';
            tooltip.style.zIndex = '10000';
            document.body.appendChild(tooltip);
        }
        return tooltip;
    }

    constructor() {
        this.stateManager = new StateManager();
        this.uiManager = new UIManager(this.stateManager);
        this.chatFeature = new ChatFeature(this.uiManager, this.stateManager);
        this.mobileUtils = new MobileUtils();
        
        this.contentEditorHandler = new ContentEditorHandler(this.uiManager, this.stateManager);
        
        this.imageFeature = new ImageFeature(this.uiManager, this.stateManager, this.chatFeature);

        this.ideaHandler = new IdeaHandler(this.uiManager, this.stateManager, this.chatFeature);
        this.outlineHandler = new OutlineHandler(this.uiManager, this.stateManager, this.chatFeature);
        this.contentHandler = new ContentHandler(this.uiManager, this.stateManager, this.chatFeature, this.contentEditorHandler, this.imageFeature);
        this.seoHandler = new SeoHandler(this.uiManager, this.stateManager, this.chatFeature);
        
        this.saveDraftFeature = new SavePublishFeature(this.uiManager.elements, this.stateManager, this.chatFeature);
    }

    init() {
        this.setupPublishTooltip();
        this.uiManager.updateStepUI(this.stateManager.getStateValue('currentStep'));
        this.updateHeaderVisibility(this.stateManager.getStateValue('currentStep'));
        this.chatFeature.init();
        this.contentEditorHandler.init();
        
        this.imageFeature.init();
        this.saveDraftFeature.init();
        // Mount completion toast if steps already complete
        this.maybeShowCompletionToast();
        this.mobileUtils.init();

        this.ideaHandler.init();
        this.outlineHandler.init();
        this.contentHandler.init();
        this.seoHandler.init();
        
        // Listener for step changes
        document.addEventListener('aieditor:stepChange', (event) => {
            const newStep = event.detail.step;
            this.goToStep(newStep);
        });

        // Mobile-specific optimizations
        if (this.mobileUtils.isMobileDevice()) {
            this.mobileUtils.optimizeScrolling();
            this.setupMobileOptimizations();
        }

        console.log('AI Article Creator initialized (Modular)');
        this.chatFeature.addAssistantMessage("Welcome! Let's create an amazing article.");
        // Initial UI setup based on the first step
        this.performStepSpecificActions(this.stateManager.getStateValue('currentStep'));
    }

    goToStep(step) {
        this.stateManager.setCurrentStep(step);
        this.uiManager.updateStepUI(step);
        this.updateHeaderVisibility(step);
        this.performStepSpecificActions(step);
        this.chatFeature.addAssistantMessage(this.chatFeature.getStepWelcomeMessage(step));
    }

    updateHeaderVisibility(step) {
        this.maybeShowCompletionToast();
        const header = document.querySelector('.ai-editor-header');
        if (header) {
            // Show header for content and seo steps, keep hidden for idea/outline
            if (step === 'content' || step === 'seo') {
                header.classList.add('show-header');
            } else {
                header.classList.remove('show-header');
            }
            // Publish functionality has been completely removed - show placeholder message
        }
    }

    performStepSpecificActions(step) {
        switch (step) {
            case 'idea':
                // IdeaHandler's init should cover this, or add specific UI updates if needed
                break;
            case 'outline':
                this.uiManager.renderSelectedIdea(); // Renders the selected idea at the top
                if (!this.stateManager.getStateValue('outline')?.length) {
                    this.outlineHandler.generateOutline();
                } else {
                    this.outlineHandler.renderOutline();
                    this.outlineHandler.renderOutlineMap();
                }
                break;
            case 'content':
                 // Auto-set title from selected idea if not already set
                if (!this.stateManager.getStateValue('title') && this.stateManager.getStateValue('selectedIdea')?.title) {
                    this.contentEditorHandler.setTitle(this.stateManager.getStateValue('selectedIdea').title);
                }
                if (!this.stateManager.getStateValue('content')) {
                    this.contentHandler.generateContent();
                }
                break;
            case 'seo':
                this.seoHandler.autoRunIfNeeded();
                break;
        }
    }

   maybeShowCompletionToast() {
       try {
           const seoDone = this.stateManager.getStateValue('seoCompleted');
           const container = document.querySelector('.ai-editor-container');
           let toast = container?.querySelector('.ai-completion-toast');
           if (seoDone && container) {
               if (!toast) {
                   toast = document.createElement('div');
                   toast.className = 'ai-completion-toast';
                   toast.innerHTML = `
                       <div class="ai-toast-content">
                         <div class="ai-toast-text">All steps are complete. Ready to publish when functionality is implemented.</div>
                         <button class="ai-toast-close" aria-label="Close">×</button>
                       </div>`;
                   container.appendChild(toast);
                   toast.querySelector('.ai-toast-close').addEventListener('click', () => toast.classList.add('hidden'));
               } else {
                   toast.classList.remove('hidden');
               }
           }
       } catch (e) {}
   }

    setupPublishTooltip() {
        // Publish functionality has been completely removed
        const publishBtn = document.getElementById('ai-publish');
        const publishMobileBtn = document.getElementById('ai-publish-mobile');
        
        if (publishBtn) {
            publishBtn.addEventListener('click', () => {
                alert('Publish functionality to be implemented.');
            });
            publishBtn.style.opacity = '0.7';
            publishBtn.title = 'Publish functionality to be implemented';
        }
        
        if (publishMobileBtn) {
            publishMobileBtn.addEventListener('click', () => {
                alert('Publish functionality to be implemented.');
            });
            publishMobileBtn.style.opacity = '0.7';
            publishMobileBtn.title = 'Publish functionality to be implemented';
        }
    }

    setupMobileOptimizations() {
        // Add haptic feedback to important buttons
        const importantButtons = document.querySelectorAll('#ai-generate-ideas, #ai-approve-outline, #ai-generate-initial-content');
        importantButtons.forEach(button => {
            this.mobileUtils.addHapticFeedback(button, 'medium');
        });

        // Add light haptic feedback to regular buttons
        const regularButtons = document.querySelectorAll('.ai-btn:not(#ai-generate-ideas):not(#ai-approve-outline):not(#ai-generate-initial-content)');
        regularButtons.forEach(button => {
            this.mobileUtils.addHapticFeedback(button, 'light');
        });

        // Optimize touch scrolling for content areas
        const scrollAreas = document.querySelectorAll('.ai-process-content, .ai-ideas-container, .ai-outline-container');
        scrollAreas.forEach(area => {
            area.style.webkitOverflowScrolling = 'touch';
            area.style.overscrollBehavior = 'contain';
        });

        // Add mobile-specific event listeners
        this.setupMobileGestures();
    }

    setupMobileGestures() {
        // Add swipe gestures for step navigation on mobile
        if (this.mobileUtils.isMobileDevice()) {
            let startX = 0;
            let startY = 0;
            
            const processContent = document.querySelector('.ai-process-content');
            if (processContent) {
                processContent.addEventListener('touchstart', (e) => {
                    startX = e.touches[0].clientX;
                    startY = e.touches[0].clientY;
                });

                processContent.addEventListener('touchend', (e) => {
                    const endX = e.changedTouches[0].clientX;
                    const endY = e.changedTouches[0].clientY;
                    const diffX = startX - endX;
                    const diffY = startY - endY;

                    // Only trigger swipe if horizontal movement is greater than vertical
                    if (Math.abs(diffX) > Math.abs(diffY) && Math.abs(diffX) > 50) {
                        if (diffX > 0) {
                            // Swipe left - next step
                            this.handleSwipeNavigation('next');
                        } else {
                            // Swipe right - previous step
                            this.handleSwipeNavigation('prev');
                        }
                    }
                });
            }
        }
    }

    handleSwipeNavigation(direction) {
        const currentStep = this.stateManager.getStateValue('currentStep');
        const steps = ['idea', 'outline', 'content', 'seo'];
        const currentIndex = steps.indexOf(currentStep);

        let newIndex;
        if (direction === 'next' && currentIndex < steps.length - 1) {
            newIndex = currentIndex + 1;
        } else if (direction === 'prev' && currentIndex > 0) {
            newIndex = currentIndex - 1;
        }

        if (newIndex !== undefined) {
            this.goToStep(steps[newIndex]);
            // Show a subtle notification about the step change
            showNotification(`Moved to ${steps[newIndex]} step`, 'info', 2000);
        }
    }
}

document.addEventListener('DOMContentLoaded', function() {
    if (document.querySelector('.ai-editor-container')) { // Check if on the AI editor page
        const aiArticleCreator = new AIArticleCreator();
        aiArticleCreator.init();
        window.aiArticleCreator = aiArticleCreator; // Optional: for debugging
    }
});
