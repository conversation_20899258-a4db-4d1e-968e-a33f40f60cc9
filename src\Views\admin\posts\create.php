<?php
// View data: $title, $errors (optional), $old_input (optional), $categories
$errors = $errors ?? [];
$old_input = $old_input ?? [];
?>

<div class="admin-form-container">
    <div class="admin-card">
        <div class="admin-card-header">
            <h2 class="admin-card-title">Create New Post</h2>
        </div>
        <div class="admin-card-body">
            <form action="/admin/posts/store" method="POST" id="post-form">
                <input type="hidden" name="csrf_token" value="<?= Brenzley\Core\Session::getCsrfToken() ?>">
                
                <div class="admin-form-group">
                    <label for="title" class="admin-form-label">Title</label>
                    <input type="text" id="title" name="title" class="admin-form-input" 
                           value="<?= htmlspecialchars($old_input['title'] ?? '') ?>" required>
                    <?php if (isset($errors['title'])): ?>
                        <div class="admin-form-error"><?= htmlspecialchars($errors['title']) ?></div>
                    <?php endif; ?>
                </div>
                
                <div class="admin-form-group">
                    <label for="slug" class="admin-form-label">Slug (URL)</label>
                    <input type="text" id="slug" name="slug" class="admin-form-input" 
                           value="<?= htmlspecialchars($old_input['slug'] ?? '') ?>">
                    <div class="admin-form-text">Leave empty to generate automatically from title.</div>
                    <?php if (isset($errors['slug'])): ?>
                        <div class="admin-form-error"><?= htmlspecialchars($errors['slug']) ?></div>
                    <?php endif; ?>
                </div>
                
                <div class="admin-form-group">
                    <label for="category_id" class="admin-form-label">Category</label>
                    <select id="category_id" name="category_id" class="admin-form-select">
                        <option value="">-- Select Category --</option>
                        <?php foreach ($categories as $category): ?>
                            <option value="<?= (int)$category['id'] ?>" 
                                <?= (isset($old_input['category_id']) && $old_input['category_id'] == $category['id']) ? 'selected' : '' ?>>
                                <?= htmlspecialchars($category['name']) ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                    <?php if (isset($errors['category_id'])): ?>
                        <div class="admin-form-error"><?= htmlspecialchars($errors['category_id']) ?></div>
                    <?php endif; ?>
                </div>
                
                <div class="admin-form-group">
                    <label for="content" class="admin-form-label">Content</label>
                    <textarea id="content" name="content" class="admin-form-textarea" rows="15"><?= htmlspecialchars($old_input['content'] ?? '') ?></textarea>
                    <?php if (isset($errors['content'])): ?>
                        <div class="admin-form-error"><?= htmlspecialchars($errors['content']) ?></div>
                    <?php endif; ?>
                </div>
                
                <!-- SEO Fields -->
                <div class="admin-form-section">
                    <h3 class="admin-form-section-title">SEO Settings</h3>
                    
                    <div class="admin-form-group">
                        <label for="meta_title" class="admin-form-label">Meta Title</label>
                        <input type="text" id="meta_title" name="meta_title" class="admin-form-input" 
                               value="<?= htmlspecialchars($old_input['meta_title'] ?? '') ?>">
                        <div class="admin-form-text">Leave empty to use the post title.</div>
                        <?php if (isset($errors['meta_title'])): ?>
                            <div class="admin-form-error"><?= htmlspecialchars($errors['meta_title']) ?></div>
                        <?php endif; ?>
                    </div>
                    
                    <div class="admin-form-group">
                        <label for="meta_description" class="admin-form-label">Meta Description</label>
                        <textarea id="meta_description" name="meta_description" class="admin-form-textarea" rows="3"><?= htmlspecialchars($old_input['meta_description'] ?? '') ?></textarea>
                        <div class="admin-form-text">Recommended length: 150-160 characters.</div>
                        <?php if (isset($errors['meta_description'])): ?>
                            <div class="admin-form-error"><?= htmlspecialchars($errors['meta_description']) ?></div>
                        <?php endif; ?>
                    </div>
                    
                    <div class="admin-form-group">
                        <label for="focus_keywords" class="admin-form-label">Focus Keywords</label>
                        <input type="text" id="focus_keywords" name="focus_keywords" class="admin-form-input" 
                               value="<?= htmlspecialchars($old_input['focus_keywords'] ?? '') ?>">
                        <div class="admin-form-text">Separate multiple keywords with commas.</div>
                        <?php if (isset($errors['focus_keywords'])): ?>
                            <div class="admin-form-error"><?= htmlspecialchars($errors['focus_keywords']) ?></div>
                        <?php endif; ?>
                    </div>
                    
                    <div class="admin-form-group">
                        <label class="admin-form-checkbox-label">
                            <input type="checkbox" name="table_of_contents_enabled" value="1" 
                                   <?= (isset($old_input['table_of_contents_enabled']) && $old_input['table_of_contents_enabled']) ? 'checked' : '' ?>>
                            Enable Table of Contents
                        </label>
                        <div class="admin-form-text">Automatically generate a table of contents from headings.</div>
                    </div>
                </div>
                
                <div class="admin-form-group">
                    <label for="status" class="admin-form-label">Status</label>
                    <select id="status" name="status" class="admin-form-select">
                        <option value="draft" <?= (isset($old_input['status']) && $old_input['status'] === 'draft') ? 'selected' : '' ?>>Draft</option>
                        <option value="published" disabled>Published (functionality to be implemented)</option>
                    </select>
                    <?php if (isset($errors['status'])): ?>
                        <div class="admin-form-error"><?= htmlspecialchars($errors['status']) ?></div>
                    <?php endif; ?>
                </div>
                
                <div class="admin-form-actions">
                    <button type="submit" class="admin-btn admin-btn-primary">Create Post</button>
                    <a href="/admin/posts" class="admin-btn admin-btn-secondary">Cancel</a>
                </div>
            </form>
        </div>
    </div>
</div>

<script src="/assets/js/pages/admin-post-editor.js" defer></script>
