<?php

namespace Brenzley\Services\ApiProviders;

/**
 * OpenAI Compatible API Provider
 */
class OpenAiCompatibleProvider extends AbstractApiProvider
{
    /**
     * Base URL for the OpenAI Compatible API
     */
    private string $baseUrl;

    /**
     * Stream buffer manager for handling partial chunks
     */
    private StreamBufferManager $bufferManager;

    /**
     * Chunk buffer for processing partial SSE data
     */
    private string $chunkBuffer = '';


    /**
     * Get the provider name
     *
     * @return string The provider name
     */
    protected function getProviderName(): string
    {
        return 'openai_compatible';
    }

    /**
     * Constructor
     *
     * @param string $apiKey API key for the provider
     * @param string $baseUrl Base URL for the OpenAI Compatible API
     * @param string|null $selectedModel Selected model for the provider
     * @param int $contextWindow Context window size for the model
     * @param array $settings Additional provider-specific settings
     */
    public function __construct(string $apiKey, string $baseUrl, ?string $selectedModel = null, int $contextWindow = 8096, array $settings = [])
    {
        parent::__construct($apiKey, $selectedModel, $contextWindow, $settings);

        // Ensure base URL ends with a slash
        $this->baseUrl = rtrim($baseUrl, '/') . '/';
        
        // Initialize stream buffer manager
        $this->bufferManager = new StreamBufferManager();
    }

    /**
     * Set the base URL for the OpenAI Compatible API
     *
     * @param string $baseUrl Base URL for the OpenAI Compatible API
     * @return void
     */
    public function setBaseUrl(string $baseUrl): void
    {
        // Ensure base URL ends with a slash
        $this->baseUrl = rtrim($baseUrl, '/') . '/';
    }

    /**
     * Test the connection to the OpenAI Compatible API
     *
     * @return array Success status and message
     */
    public function testConnection(): array
    {
        if (empty($this->baseUrl)) {
            return [
                'success' => false,
                'message' => 'Base URL is required for OpenAI compatible APIs.',
                'details' => [
                    'provider' => 'openai_compatible',
                    'error_type' => 'configuration',
                    'error' => 'Missing base URL'
                ]
            ];
        }

        $url = $this->baseUrl . 'models';
        $headers = [
            'Content-Type: application/json',
            'Authorization: Bearer ' . $this->apiKey
        ];

        $result = $this->makeRequest($url, 'GET', $headers);

        if (!empty($result['error'])) {
            return [
                'success' => false,
                'message' => 'Connection error: ' . $result['error'],
                'details' => [
                    'provider' => 'openai_compatible',
                    'error_type' => 'connection',
                    'error' => $result['error']
                ]
            ];
        }

        if ($result['http_code'] >= 200 && $result['http_code'] < 300) {
            return [
                'success' => true,
                'message' => 'Successfully connected to OpenAI Compatible API',
                'details' => [
                    'provider' => 'openai_compatible',
                    'http_code' => $result['http_code']
                ]
            ];
        }

        $errorMessage = $this->parseErrorResponse($result['response']);
        return [
            'success' => false,
            'message' => 'API error: ' . $errorMessage,
            'details' => [
                'provider' => 'openai_compatible',
                'error_type' => 'api',
                'http_code' => $result['http_code'],
                'error' => $errorMessage
            ]
        ];
    }

    /**
     * Get available models from the OpenAI Compatible API
     *
     * @return array Success status, message, and models list
     */
    public function getModels(): array
    {
        if (empty($this->baseUrl)) {
            return [
                'success' => false,
                'message' => 'Base URL is required for OpenAI compatible APIs.'
            ];
        }

        $url = $this->baseUrl . 'models';
        $headers = [
            'Content-Type: application/json',
            'Authorization: Bearer ' . $this->apiKey
        ];

        $result = $this->makeRequest($url, 'GET', $headers);

        if (!empty($result['error'])) {
            return [
                'success' => false,
                'message' => 'Connection error: ' . $result['error']
            ];
        }

        if ($result['http_code'] >= 200 && $result['http_code'] < 300) {
            $data = json_decode($result['response'], true);
            $models = [];

            if (isset($data['data']) && is_array($data['data'])) {
                foreach ($data['data'] as $model) {
                    if (isset($model['id'])) {
                        // OpenAI compatible APIs might not provide context window
                        // Using default values based on model type or user-provided value
                        $contextWindow = $this->contextWindow;

                        $models[] = [
                            'id' => $model['id'],
                            'name' => $model['id'],
                            'context_window' => $contextWindow
                        ];
                    }
                }
            }

            return [
                'success' => true,
                'models' => $models
            ];
        }

        $errorMessage = $this->parseErrorResponse($result['response']);
        return [
            'success' => false,
            'message' => 'Failed to fetch models: ' . $errorMessage
        ];
    }

    /**
     * Generate a completion using the OpenAI Compatible API
     *
     * @param string $prompt The prompt to generate a completion for
     * @param array $options Additional options for the completion
     * @return array Success status, message, and completion result
     */
    public function generateCompletion(string $prompt, array $options = []): array
    {
        if (empty($this->baseUrl)) {
            return [
                'success' => false,
                'message' => 'Base URL is required for OpenAI compatible APIs.'
            ];
        }

        if (empty($this->selectedModel)) {
            return [
                'success' => false,
                'message' => 'No model selected'
            ];
        }

        // Use the base URL directly and append 'chat/completions'
        $url = $this->baseUrl . 'chat/completions';
        $headers = [
            'Content-Type: application/json',
            'Authorization: Bearer ' . $this->apiKey
        ];

        $messages = [];
        if (!empty($options['system_prompt'])) {
            $messages[] = ['role' => 'system', 'content' => $options['system_prompt']];
        }
        $messages[] = ['role' => 'user', 'content' => $prompt];

        $data = [
            'model' => $this->selectedModel,
            'messages' => $messages,
            'temperature' => $options['temperature'] ?? 0.7,
            'top_p' => $options['top_p'] ?? 0.95,
            'stream' => false
        ];

        $result = $this->makeRequest($url, 'POST', $headers, $data);

        if (!empty($result['error'])) {
            return [
                'success' => false,
                'message' => 'Connection error: ' . $result['error']
            ];
        }

        if ($result['http_code'] >= 200 && $result['http_code'] < 300) {
            $data = json_decode($result['response'], true);

            if (isset($data['choices'][0]['message']['content'])) {
                return [
                    'success' => true,
                    'text' => $data['choices'][0]['message']['content']
                ];
            }

            return [
                'success' => false,
                'message' => 'Invalid response format'
            ];
        }

        $errorMessage = $this->parseErrorResponse($result['response']);
        return [
            'success' => false,
            'message' => 'Failed to generate completion: ' . $errorMessage
        ];
    }

    /**
     * Stream a completion using the OpenAI Compatible API
     *
     * @param string $prompt The prompt to generate a completion for
     * @param callable $callback Callback function to handle each chunk of the stream
     * @param array $options Additional options for the completion
     * @return void
     */
    public function streamCompletion(string $prompt, callable $callback, array $options = []): void
    {
        if (empty($this->baseUrl)) {
            throw new \RuntimeException('Base URL is required for OpenAI compatible APIs.');
        }

        if (empty($this->selectedModel)) {
            throw new \RuntimeException('No model selected');
        }

        // Use the base URL directly and append 'chat/completions'
        $url = $this->baseUrl . 'chat/completions';
        $headers = [
            'Content-Type: application/json',
            'Authorization: Bearer ' . $this->apiKey
        ];

        $messages = [];
        if (!empty($options['system_prompt'])) {
            $messages[] = ['role' => 'system', 'content' => $options['system_prompt']];
        }
        $messages[] = ['role' => 'user', 'content' => $prompt];

        $data = [
            'model' => $this->selectedModel,
            'messages' => $messages,
            'temperature' => $options['temperature'] ?? 0.7,
            'top_p' => $options['top_p'] ?? 0.95,
            'stream' => true
        ];

        // Pass through streaming options
        $streamingOptions = [
            'debug' => $options['debug'] ?? false,
            'raw_stream' => $options['raw_stream'] ?? false
        ];

        // Reset chunk buffer for new streaming request
        $this->chunkBuffer = '';
        
        // Execute streaming request
        $this->executeStreamingRequest($url, $data, $headers, $callback, $streamingOptions);
        
        // Clean up buffer manager after streaming completes
        $this->bufferManager->reset();
        $this->chunkBuffer = '';
    }

    /**
     * Process a chunk of streaming data from OpenAI Compatible API
     *
     * @param string $chunk The data chunk
     * @param callable $callback Callback function to handle the processed chunk
     * @return void
     */
    protected function processStreamChunk(string $chunk, callable $callback): void
    {
        // Skip empty chunks
        if (empty($chunk)) {
            return;
        }

        // Add chunk to instance buffer for processing
        $this->chunkBuffer .= $chunk;

        // Process complete lines from buffer
        while (($pos = strpos($this->chunkBuffer, "\n")) !== false) {
            $line = substr($this->chunkBuffer, 0, $pos);
            $this->chunkBuffer = substr($this->chunkBuffer, $pos + 1);
            
            // Remove carriage return if present
            $line = rtrim($line, "\r");
            
            // Skip empty lines
            if ($line === '') {
                continue;
            }
            
            // Check for HTTP error responses in the chunk (non-SSE format)
            if (strpos($line, 'HTTP/') === 0 || (strpos($line, '{') === 0 && strpos($line, '"error"') !== false)) {
                // This might be an HTTP error response
                $errorData = json_decode($line, true);
                if (json_last_error() === JSON_ERROR_NONE && isset($errorData['error'])) {
                    $errorMessage = is_array($errorData['error']) ? 
                        ($errorData['error']['message'] ?? 'API Error') : 
                        $errorData['error'];
                    throw new \RuntimeException("API Error: $errorMessage");
                }
            }
            
            // Process SSE data lines
            if (strpos($line, 'data: ') === 0) {
                $jsonData = substr($line, 6);
                
                // Skip [DONE] messages
                if (trim($jsonData) === '[DONE]') {
                    continue;
                }
                
                // Parse JSON and extract content or handle errors
                $data = json_decode($jsonData, true);
                if (json_last_error() === JSON_ERROR_NONE) {
                    // Check for errors in the SSE data
                    if (isset($data['error'])) {
                        $errorMessage = is_array($data['error']) ? 
                            ($data['error']['message'] ?? 'Stream Error') : 
                            $data['error'];
                        throw new \RuntimeException("Stream Error: $errorMessage");
                    }
                    
                    $content = null;
                    
                    // Check for delta content (OpenAI streaming format)
                    if (isset($data['choices'][0]['delta']['content'])) {
                        $content = $data['choices'][0]['delta']['content'];
                    }
                    // Check for message content (complete message format)
                    elseif (isset($data['choices'][0]['message']['content'])) {
                        $content = $data['choices'][0]['message']['content'];
                    }
                    // Check for other content formats
                    elseif (isset($data['content'])) {
                        $content = $data['content'];
                    }
                    elseif (isset($data['text'])) {
                        $content = $data['text'];
                    }
                    
                    // Forward content immediately if available
                    if ($content !== null && $content !== '') {
                        $callback($content);
                    }
                } else {
                    // JSON parsing failed - check if this is an error message
                    if (strpos($jsonData, 'error') !== false || strpos($jsonData, 'Error') !== false) {
                        throw new \RuntimeException("Parse Error: Invalid JSON in stream - $jsonData");
                    }
                    // If not an error, just log and continue
                    \Brenzley\Services\Logging\Logger::warning('Failed to parse JSON in stream chunk', [
                        'provider' => $this->getProviderName(),
                        'chunk' => substr($jsonData, 0, 200),
                        'json_error' => json_last_error_msg()
                    ]);
                }
            }
        }
    }

    /**
     * Extract content from a parsed SSE event
     *
     * @param array $event Parsed SSE event
     * @return string|null Extracted content or null if no content
     */
    private function extractContentFromEvent(array $event): ?string
    {
        // If content was already extracted by buffer manager, use it
        if (isset($event['content']) && $event['content'] !== null) {
            return $event['content'];
        }

        // If we have parsed JSON data, extract content from it
        if (isset($event['parsed']) && is_array($event['parsed'])) {
            $data = $event['parsed'];

            // Check for delta content (streaming format)
            if (isset($data['choices'][0]['delta']['content'])) {
                return $data['choices'][0]['delta']['content'];
            }

            // Check for message content (complete message format)
            if (isset($data['choices'][0]['message']['content'])) {
                return $data['choices'][0]['message']['content'];
            }

            // Check for text field (alternative format)
            if (isset($data['text'])) {
                return $data['text'];
            }

            // Check for content field (generic format)
            if (isset($data['content'])) {
                return $data['content'];
            }
        }

        // If no structured content found, try to parse raw data as JSON
        if (!empty($event['data'])) {
            $data = json_decode($event['data'], true);
            if (json_last_error() === JSON_ERROR_NONE) {
                // Check for delta content
                if (isset($data['choices'][0]['delta']['content'])) {
                    return $data['choices'][0]['delta']['content'];
                }

                // Check for message content
                if (isset($data['choices'][0]['message']['content'])) {
                    return $data['choices'][0]['message']['content'];
                }
            }
        }

        return null;
    }
}
