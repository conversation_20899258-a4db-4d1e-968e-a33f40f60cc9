<?php

declare(strict_types=1);

namespace Brenzley\Models;

use Brenzley\Core\Database;
use PDO;

class PostModel
{
    private PDO $db;

    public function __construct()
    {
        $this->db = Database::getInstance();
    }

    /**
     * Find a post by its ID.
     *
     * @param int $id
     * @return array|false Post data or false if not found.
     */
    public function findById(int $id): array|false
    {
        // TODO: Join with users/categories table later if needed for display
        $stmt = $this->db->prepare("SELECT * FROM posts WHERE id = :id LIMIT 1");
        $stmt->bindParam(':id', $id, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * Get all posts (or paginated posts later).
     *
     * @return array List of posts.
     */
    public function findAll(): array
    {
        // TODO: Add pagination, ordering, filtering
        // TODO: Join with users/categories table later if needed for display
        $stmt = $this->db->query("SELECT id, title, status, created_at FROM posts ORDER BY created_at DESC");
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Create a new post.
     *
     * @param array $data Associative array of post data (
     *  'user_id', 'category_id' (optional), 'title', 'slug', 'content', 'status', 
     *  'meta_title' (optional), 'meta_description' (optional), 'focus_keywords' (optional), 
     *  'table_of_contents_enabled' (optional) 
     * )
     * @return int|false The ID of the newly created post or false on failure.
     */
    public function create(array $data): int|false
    {
        // TODO: Add proper validation/sanitization before this point
        // TODO: Generate slug automatically if not provided
        // DB Migration Needed: Add meta_title, meta_description, focus_keywords, table_of_contents_enabled, last_saved_at columns to posts table
        
        $sql = "INSERT INTO posts (user_id, category_id, title, slug, content, status, published_at, 
                             meta_title, meta_description, focus_keywords, table_of_contents_enabled, last_saved_at) 
                VALUES (:user_id, :category_id, :title, :slug, :content, :status, :published_at, 
                        :meta_title, :meta_description, :focus_keywords, :table_of_contents_enabled, :last_saved_at)";
        $stmt = $this->db->prepare($sql);

        $published_at = ($data['status'] ?? 'draft') === 'published' ? date('Y-m-d H:i:s') : null;
        $last_saved_at = date('Y-m-d H:i:s'); // Always update on create/update

        $stmt->bindParam(':user_id', $data['user_id'], PDO::PARAM_INT);
        $stmt->bindParam(':category_id', $data['category_id'], PDO::PARAM_INT); // Consider null if not provided
        $stmt->bindParam(':title', $data['title']);
        $stmt->bindParam(':slug', $data['slug']); // Needs validation/generation
        $stmt->bindParam(':content', $data['content']);
        $stmt->bindParam(':status', $data['status']);
        $stmt->bindParam(':published_at', $published_at);
        $stmt->bindParam(':meta_title', $data['meta_title']);
        $stmt->bindParam(':meta_description', $data['meta_description']);
        $stmt->bindParam(':focus_keywords', $data['focus_keywords']); // Store as JSON or comma-separated? Decide and validate.
        $stmt->bindValue(':table_of_contents_enabled', $data['table_of_contents_enabled'] ?? true, PDO::PARAM_BOOL);
        $stmt->bindParam(':last_saved_at', $last_saved_at);
        
        if ($stmt->execute()) {
            return (int)$this->db->lastInsertId();
        } else {
            // Log error in production
            return false;
        }
    }

    /**
     * Update an existing post.
     *
     * @param int $id The ID of the post to update.
     * @param array $data Associative array of post data to update (
     *  'category_id' (optional), 'title', 'slug', 'content', 'status', 
     *  'meta_title' (optional), 'meta_description' (optional), 'focus_keywords' (optional), 
     *  'table_of_contents_enabled' (optional) 
     * )
     * @return bool True on success, false on failure.
     */
    public function update(int $id, array $data): bool
    {
        // TODO: Add proper validation/sanitization
        // TODO: Generate slug automatically
        // TODO: Handle user_id check (only author/admin can update?)
        // DB Migration Needed: Add meta_title, meta_description, focus_keywords, table_of_contents_enabled, last_saved_at columns to posts table (if not already done)
        
        // Determine fields to update
        $fields = [];
        $params = ['id' => $id];
        // Add new fields to allowed list
        $allowedFields = [
            'category_id', 'title', 'slug', 'content', 'status', 
            'meta_title', 'meta_description', 'focus_keywords', 'table_of_contents_enabled'
        ];

        foreach ($allowedFields as $field) {
            if (isset($data[$field])) {
                $fields[] = "{$field} = :{$field}";
                $params[$field] = $data[$field];
            }
        }

        // Always update last_saved_at on any update
        $fields[] = "last_saved_at = :last_saved_at";
        $params['last_saved_at'] = date('Y-m-d H:i:s');

        if (isset($data['status'])) {
            $fields[] = "published_at = :published_at";
            $params['published_at'] = ($data['status'] === 'published') ? date('Y-m-d H:i:s') : null;
        }

        if (count($fields) <= 1) { // Only last_saved_at was added
            // Check if only last_saved_at needs updating (e.g., auto-save with no other changes)
            // If other fields were intended but not set, this prevents an unnecessary update query only for last_saved_at
            // Decide if an update purely for last_saved_at is desired on every auto-save ping, even without content change.
            // For now, let's assume an update requires at least one other field change besides last_saved_at.
             if (!isset($data['force_save'])) { // Add a flag if you want to force save just the timestamp
                 // Log or handle the case where no actual data changed, maybe return true?
                 return true; 
             }
        }

        $sql = "UPDATE posts SET " . implode(', ', $fields) . " WHERE id = :id";
        $stmt = $this->db->prepare($sql);

        // Bind parameters
        foreach ($params as $key => &$value) { // Use reference for bindParam
             $paramType = PDO::PARAM_STR; // Default to string
            if ($key === 'id' || $key === 'category_id') {
                $paramType = PDO::PARAM_INT;
            } elseif ($key === 'table_of_contents_enabled') {
                 $paramType = PDO::PARAM_BOOL;
                 $value = (bool)$value; // Ensure boolean type for binding
            } elseif (($key === 'published_at' || $key === 'meta_title' || $key === 'meta_description' || $key === 'focus_keywords') && $value === null) {
                $paramType = PDO::PARAM_NULL;
            }
             $stmt->bindParam(":{$key}", $value, $paramType);
        }
        unset($value); // Unset reference

        return $stmt->execute();
    }

    /**
     * Delete a post by its ID.
     *
     * @param int $id
     * @return bool True on success, false on failure.
     */
    public function delete(int $id): bool
    {
        // TODO: Add authorization check (only author/admin?)
        $sql = "DELETE FROM posts WHERE id = :id";
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':id', $id, PDO::PARAM_INT);
        return $stmt->execute();
    }

    /**
     * Find a specified number of published posts with author and category details.
     *
     * @param int $limit Maximum number of posts to return.
     * @param int $offset Number of posts to skip (for pagination).
     * @return array List of published posts with details.
     */
    public function findPublishedWithDetails(int $limit = 10, int $offset = 0): array
    {
        $sql = "SELECT 
                    p.id, p.title, p.slug, p.content, p.status, p.published_at, p.created_at, 
                    u.username AS author_username, 
                    c.name AS category_name, c.slug AS category_slug
                FROM 
                    posts p
                JOIN 
                    users u ON p.user_id = u.id
                LEFT JOIN 
                    categories c ON p.category_id = c.id
                WHERE 
                    p.status = 'published'
                ORDER BY 
                    p.published_at DESC
                LIMIT :limit OFFSET :offset";
        
        try {
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
            $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
            $stmt->execute();
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (\PDOException $e) {
            // Log error in production
            error_log("Error fetching published posts: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get the total count of published posts.
     *
     * @return int Total count of published posts.
     */
    public function countPublished(): int
    {
         try {
            $stmt = $this->db->query("SELECT COUNT(*) FROM posts WHERE status = 'published'");
            return (int)$stmt->fetchColumn();
        } catch (\PDOException $e) {
            error_log("Error counting published posts: " . $e->getMessage());
            return 0;
        }
    }

    /**
     * Find a single published post by its slug, including author and category details.
     *
     * @param string $slug The URL slug of the post.
     * @return array|false Post data with details or false if not found/not published.
     */
    public function findPublishedBySlugWithDetails(string $slug): array|false
    {
        $sql = "SELECT 
                    p.id, p.title, p.slug, p.content, p.status, p.published_at, p.created_at, 
                    p.user_id, u.username AS author_username, 
                    p.category_id, c.name AS category_name, c.slug AS category_slug
                FROM 
                    posts p
                JOIN 
                    users u ON p.user_id = u.id
                LEFT JOIN 
                    categories c ON p.category_id = c.id
                WHERE 
                    p.slug = :slug
                    AND p.status = 'published'
                LIMIT 1";

        try {
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':slug', $slug, PDO::PARAM_STR);
            $stmt->execute();
            return $stmt->fetch(PDO::FETCH_ASSOC);
        } catch (\PDOException $e) {
            error_log("Error fetching post by slug {$slug}: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get the total count of all posts.
     *
     * @return int
     */
    public function countAll(): int
    {
        try {
            $stmt = $this->db->query("SELECT COUNT(*) FROM posts");
            return (int)$stmt->fetchColumn();
        } catch (\PDOException $e) {
            error_log("Error counting all posts: " . $e->getMessage());
            return 0;
        }
    }

    /**
     * Find the most recent posts.
     *
     * @param int $limit
     * @return array
     */
    public function findRecent(int $limit = 5): array
    {
        try {
            // Selecting basic info needed for the dashboard table
            $sql = "SELECT id, title, status, created_at, slug 
                    FROM posts 
                    ORDER BY created_at DESC 
                    LIMIT :limit";
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
            $stmt->execute();
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (\PDOException $e) {
            error_log("Error fetching recent posts: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get the comment count for a specific post
     *
     * @param int $postId
     * @return int
     */
    public function getCommentCount(int $postId): int
    {
        try {
            $stmt = $this->db->prepare("SELECT COUNT(*) FROM comments WHERE post_id = :post_id");
            $stmt->bindParam(':post_id', $postId, PDO::PARAM_INT);
            $stmt->execute();
            return (int)$stmt->fetchColumn();
        } catch (\PDOException $e) {
            error_log("Error counting comments for post {$postId}: " . $e->getMessage());
            return 0;
        }
    }

    /**
     * Get the view count for a specific post
     *
     * @param int $postId
     * @return int
     */
    public function getViewCount(int $postId): int
    {
        try {
            $stmt = $this->db->prepare("SELECT views FROM posts WHERE id = :id");
            $stmt->bindParam(':id', $postId, PDO::PARAM_INT);
            $stmt->execute();
            return (int)$stmt->fetchColumn();
        } catch (\PDOException $e) {
            error_log("Error getting view count for post {$postId}: " . $e->getMessage());
            return 0;
        }
    }

    /**
     * Increment the view count for a post
     *
     * @param int $postId
     * @return bool
     */
    public function incrementViewCount(int $postId): bool
    {
        try {
            $stmt = $this->db->prepare("UPDATE posts SET views = views + 1 WHERE id = :id");
            $stmt->bindParam(':id', $postId, PDO::PARAM_INT);
            return $stmt->execute();
        } catch (\PDOException $e) {
            error_log("Error incrementing view count for post {$postId}: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Find all posts with additional stats (comment count, view count)
     *
     * @return array
     */
    public function findAllWithStats(): array
    {
        try {
            $sql = "SELECT p.*, 
                    (SELECT COUNT(*) FROM comments c WHERE c.post_id = p.id) AS comment_count
                    FROM posts p
                    ORDER BY p.created_at DESC";
            $stmt = $this->db->query($sql);
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (\PDOException $e) {
            error_log("Error fetching posts with stats: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Find post by slug
     *
     * @param string $slug
     * @return array|null
     */
    public function findBySlug(string $slug): ?array
    {
        try {
            $sql = "SELECT * FROM posts WHERE slug = ? LIMIT 1";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$slug]);
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            return $result ?: null;
        } catch (\PDOException $e) {
            error_log("Error finding post by slug: " . $e->getMessage());
            return null;
        }
    }

    // TODO: Add methods for finding posts by category, tag, user etc.
} 