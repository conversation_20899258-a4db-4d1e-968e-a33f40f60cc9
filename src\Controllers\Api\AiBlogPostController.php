<?php

declare(strict_types=1);

namespace Brenzley\Controllers\Api;

use B<PERSON>zley\Core\Session;
use B<PERSON>zley\Services\ApiProviders\ApiService;
use B<PERSON>zley\Models\AiBlogPostModel;
use Brenzley\Models\PostModel;
use B<PERSON>zley\Services\ApiProviders\StreamingApiProviderInterface;
use Brenzley\Services\ApiProviders\ApiProviderFactory;
use Brenzley\Services\Prompts\PromptManager;
use Exception;
use Error;
use PDO;

/**
 * Controller for AI blog post generation
 */
class AiBlogPostController extends AiContentController
{
    private AiBlogPostModel $aiBlogPostModel;
    // Other models like $this->ideaModel, $this->outlineModel, $this->sessionModel, 
    // $this->apiSettingsModel, $this->seoModel are expected to be initialized 
    // by the parent AiContentController's constructor.

    public function __construct()
    {
        parent::__construct(); // This should initialize models from AiContentController
        $this->aiBlogPostModel = new AiBlogPostModel();
    }

    public function streamFullContent(): void
    {
        if (!Session::isLoggedIn()) {
            $this->sendSseError('Authentication required', 401);
            return;
        }

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->sendSseError('Invalid request method. This endpoint requires POST.', 405);
            return;
        }

        // Set headers for streaming response
        header('Content-Type: text/event-stream');
        header('Cache-Control: no-cache');
        header('Connection: keep-alive');
        header('X-Accel-Buffering: no'); // Disable Nginx buffering
        header('Access-Control-Allow-Origin: *');
        header('Access-Control-Allow-Headers: Cache-Control');

        // Disable output buffering completely
        while (ob_get_level()) {
            ob_end_clean();
        }

        // Disable implicit flush and set up for immediate output
        ini_set('implicit_flush', '1');
        ini_set('output_buffering', '0');
        
        // If using FastCGI, finish the request to avoid buffering
        if (function_exists('fastcgi_finish_request')) {
            // We'll call this after headers but before streaming
            register_shutdown_function('fastcgi_finish_request');
        }

        $data = json_decode(file_get_contents('php://input'), true);

        $title = $data['title'] ?? '';
        $outline = $data['outline'] ?? [];
        $ideaPreset = $data['ideaPreset'] ?? 'professional';
        $blogIdeaId = !empty($data['blog_idea_id']) ? (int)$data['blog_idea_id'] : null;
        $outlineId = !empty($data['outline_id']) ? (int)$data['outline_id'] : null;
        $userId = Session::get('user_id');

        if (empty($title) || empty($outline) || !$userId) {
            $this->sendSseError('Missing required parameters (title, outline, or user session).');
            return;
        }
        if (!is_array($outline)) {
            $this->sendSseError('Invalid outline format. Expected JSON array.');
            return;
        }

        $prompts = PromptManager::getFullArticlePrompts($title, $outline, $ideaPreset);
        
        $fullGeneratedContent = '';
        $activeProviderSettings = $this->apiSettingsModel->getActiveProvider();

        if (!$activeProviderSettings || $activeProviderSettings['provider'] === 'none') {
            $this->sendSseError('No active AI provider configured.');
            return;
        }

        $apiProvider = $this->createApiProvider($activeProviderSettings);

        if (!$apiProvider) {
            $this->sendSseError('Failed to create API provider instance.');
            return;
        }

        try {
            $aiParameters = [
                'system_prompt' => $prompts['system']
            ];

            // Send start message
            echo "data: " . json_encode(['type' => 'start', 'message' => 'Starting content generation...']) . "\n\n";
            flush();

            // Call streamCompletion on the specific provider instance
            $apiProvider->streamCompletion($prompts['user'], function($chunk) use (&$fullGeneratedContent) {
                $fullGeneratedContent .= $chunk;
                if ($chunk !== null && $chunk !== '') {
                    // Send chunk in the format expected by frontend with immediate flush
                    echo "data: " . json_encode(['type' => 'chunk', 'content' => $chunk]) . "\n\n";
                    
                    // Force immediate output
                    if (ob_get_level()) {
                        ob_flush();
                    }
                    flush();
                }
            }, $aiParameters);

            $postId = $this->aiBlogPostModel->create([
                'user_id' => $userId,
                'blog_idea_id' => $blogIdeaId,
                'outline_id' => $outlineId,
                'title' => $title,
                'content' => $fullGeneratedContent, // Add the missing content field
                'content_html' => $fullGeneratedContent,
                'provider' => $activeProviderSettings['provider'] ?? 'unknown',
                'model' => $activeProviderSettings['selected_model'] ?? 'unknown',
                'parameters' => $aiParameters // Store parameters used for generation
            ]);

            if (!$postId) {
                error_log("Failed to save the AI generated blog post to the database. User ID: {$userId}, Title: {$title}");
                echo "data: " . json_encode(['type' => 'end', 'content' => ['full_content' => $fullGeneratedContent, 'post_id' => null, 'message' => 'Content generated but failed to save.']]) . "\n\n";
                flush();
                return;
            }

            echo "data: " . json_encode(['type' => 'end', 'content' => ['full_content' => $fullGeneratedContent, 'post_id' => $postId]]) . "\n\n";
            flush();

        } catch (\Throwable $e) {
            error_log("Error during content streaming API: " . $e->getMessage() . "\n" . $e->getTraceAsString());
            
            // Determine error type and provide specific handling
            $errorType = 'unknown';
            $errorMessage = $e->getMessage();
            
            if (strpos($errorMessage, 'Base URL is required') !== false) {
                $errorType = 'configuration';
                $errorMessage = 'OpenAI compatible provider requires a base URL to be configured.';
            } elseif (strpos($errorMessage, 'No model selected') !== false) {
                $errorType = 'configuration';
                $errorMessage = 'No model has been selected for this provider.';
            } elseif (strpos($errorMessage, 'Connection') !== false || strpos($errorMessage, 'cURL') !== false) {
                $errorType = 'network';
                $errorMessage = 'Network connection error: ' . $errorMessage;
            } elseif (strpos($errorMessage, 'timeout') !== false) {
                $errorType = 'timeout';
                $errorMessage = 'Request timed out. The AI provider may be experiencing high load.';
            } elseif (strpos($errorMessage, 'Unauthorized') !== false || strpos($errorMessage, '401') !== false) {
                $errorType = 'authentication';
                $errorMessage = 'Authentication failed. Please check your API key.';
            } elseif (strpos($errorMessage, 'Rate limit') !== false || strpos($errorMessage, '429') !== false) {
                $errorType = 'rate_limit';
                $errorMessage = 'Rate limit exceeded. Please wait before making another request.';
            }

            // Send detailed error in SSE format
            echo "data: " . json_encode([
                'type' => 'error', 
                'message' => $errorMessage,
                'error_type' => $errorType,
                'provider' => $activeProviderSettings['provider'] ?? 'unknown'
            ]) . "\n\n";
            flush();
        } finally {
            if (ob_get_level() > 0) {
                ob_end_flush();
            }
            flush();
            exit;
        }
    }

    private function sendSseError(string $message, int $httpStatusCode = 500): void
    {
        if (!headers_sent() && $httpStatusCode !== 200) {
             http_response_code($httpStatusCode);
        }
        if (!headers_sent()) {
            header('Content-Type: text/event-stream');
            header('Cache-Control: no-cache');
        }
        echo "data: " . json_encode(['type' => 'error', 'message' => $message]) . "\n\n";
        if (ob_get_level() > 0) {
            ob_flush();
        }
        flush();
        exit;
    }

    /**
     * Create an API provider instance from provider settings
     *
     * @param array $providerSettings Provider settings from the database
     * @return StreamingApiProviderInterface|null
     */
    private function createApiProvider(array $providerSettings): ?StreamingApiProviderInterface
    {
        if (empty($providerSettings['provider']) || $providerSettings['provider'] === 'none') {
            error_log('Attempted to create API provider with no provider specified.');
            return null;
        }

        // Get API key
        $apiKey = $this->apiSettingsModel->getDecryptedApiKey($providerSettings['provider']);
        if (empty($apiKey)) {
            error_log('Failed to get decrypted API key for provider: ' . $providerSettings['provider']);
            return null;
        }

        $settings = [
            'api_key' => $apiKey,
            'selected_model' => $providerSettings['selected_model'] ?? null,
            'context_window' => (int)($providerSettings['context_window'] ?? 8096),
            'base_url' => $providerSettings['base_url'] ?? '', // Include base_url from database settings
            'settings' => json_decode($providerSettings['settings'] ?? '{}', true) ?? []
        ];
        
        try {
            $providerInstance = ApiProviderFactory::create($providerSettings['provider'], $settings);
            if ($providerInstance instanceof StreamingApiProviderInterface) {
                return $providerInstance;
            } else {
                error_log('API Provider created by factory does not implement StreamingApiProviderInterface: ' . $providerSettings['provider']);
                return null;
            }
        } catch (\Throwable $e) {
            error_log('Error creating API provider instance: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Generate a complete blog post (Non-streaming placeholder)
     */
    public function generate(): void
    {
        if (!Session::isLoggedIn()) {
            $this->jsonResponse(['success' => false, 'message' => 'Authentication required'], 401);
            return;
        }
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->jsonResponse(['success' => false, 'message' => 'Invalid request method'], 405);
            return;
        }
        $data = json_decode(file_get_contents('php://input'), true);

        if (!isset($data['title']) || empty($data['title'])) {
            $this->jsonResponse(['success' => false, 'message' => 'Title is required'], 400);
            return;
        }
        if (!isset($data['outline']) || empty($data['outline'])) {
            $this->jsonResponse(['success' => false, 'message' => 'Outline is required'], 400);
            return;
        }

        $title = $data['title'];
        $outline = $data['outline']; 
        $parameters = $data['parameters'] ?? [];
        $blogIdeaId = $data['blog_idea_id'] ?? null;
        $blogOutlineId = isset($data['blog_outline_id']) ? (int)$data['blog_outline_id'] : null;
        $sessionId = isset($data['session_id']) ? (int)$data['session_id'] : null;
        $userId = Session::getUserId();

        if ($blogOutlineId) {
            $outlineObjFromDb = $this->outlineModel->findById($blogOutlineId);
            if (!$outlineObjFromDb || $outlineObjFromDb['user_id'] !== $userId) {
                $this->jsonResponse(['success' => false, 'message' => 'Unauthorized or invalid outline ID'], 403);
                return;
            }
        }
        
        $apiService = new ApiService();
        $activeProvider = $this->apiSettingsModel->getActiveProvider();

        $prompts = PromptManager::getFullArticlePrompts($title, $outline, $parameters['ideaPreset'] ?? 'professional');
        $systemPrompt = $prompts['system'];
        $userPrompt = $prompts['user'];

        // This generateBlogPost method in ApiService would need to be non-streaming
        $result = $apiService->generateBlogPost($systemPrompt, $userPrompt, $parameters); 

        if (!$result['success'] || !isset($result['blog_post'])) {
            $this->jsonResponse($result ?: ['success' => false, 'message' => 'Failed to generate blog post content.']);
            return;
        }

        $postData = [
            'user_id' => $userId,
            'blog_idea_id' => $blogIdeaId, 
            'outline_id' => $blogOutlineId,
            'title' => $title,
            'content_html' => $result['blog_post'],
            'provider' => $activeProvider['provider'] ?? 'unknown',
            'model' => $activeProvider['selected_model'] ?? 'unknown',
            'parameters' => $parameters
        ];

        $postId = $this->aiBlogPostModel->create($postData);

        if (!$postId) {
            $this->jsonResponse(['success' => false, 'message' => 'Failed to save blog post', 'blog_post' => $result['blog_post']], 500);
            return;
        }

        if ($sessionId) {
            $session = $this->sessionModel->findById($sessionId);
            if ($session && $session['user_id'] === $userId) {
                $this->sessionModel->update($sessionId, ['post_id' => $postId, 'status' => 'completed']);
            }
        }

        $this->jsonResponse(['success' => true, 'blog_post' => $result['blog_post'], 'post_id' => $postId]);
    }

    public function generateFromSections(): void
    {
        if (!Session::isLoggedIn()) {
            $this->jsonResponse(['success' => false, 'message' => 'Authentication required'], 401);
            return;
        }
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->jsonResponse(['success' => false, 'message' => 'Invalid request method'], 405);
            return;
        }
        $data = json_decode(file_get_contents('php://input'), true);

        if (!isset($data['blog_outline_id']) || empty($data['blog_outline_id'])) {
            $this->jsonResponse(['success' => false, 'message' => 'Blog outline ID is required'], 400);
            return;
        }

        $blogOutlineId = (int)$data['blog_outline_id'];
        $sessionId = isset($data['session_id']) ? (int)$data['session_id'] : null;
        $userId = Session::getUserId();

        $outline = $this->outlineModel->findById($blogOutlineId);
        if (!$outline || $outline['user_id'] !== $userId) {
            $this->jsonResponse(['success' => false, 'message' => 'Unauthorized or invalid blog outline'], 403);
            return;
        }

        // $this->sectionModel is not defined in this controller or parent AiContentController
        // Assuming it should be $this->aiBlogSectionModel if such a model exists, or this logic needs review
        // For now, commenting out the part that uses $this->sectionModel
        /*
        $sections = $this->sectionModel->findByOutlineId($blogOutlineId); 
        if (empty($sections)) {
            $this->jsonResponse(['success' => false, 'message' => 'No sections found for this outline'], 404);
            return;
        }
        usort($sections, function($a, $b) {
            return ($a['section_order'] ?? 0) <=> ($b['section_order'] ?? 0);
        });

        $contentHtml = ""; 
        foreach ($sections as $section) {
            $contentHtml .= "<h2>" . htmlspecialchars($section['title'] ?? 'Untitled Section') . "</h2>\n";
            $contentHtml .= "<div>" . ($section['content'] ?? '') . "</div>\n\n";
        }
        */
        // Placeholder content since sectionModel is not available here
        $contentHtml = "<p>Content generated from sections would appear here.</p>";
        
        $title = $outline['title'];
        $activeProvider = $this->apiSettingsModel->getActiveProvider();
        $postData = [
            'user_id' => $userId,
            'blog_idea_id' => $outline['blog_idea_id'] ?? null,
            'outline_id' => $blogOutlineId,
            'title' => $title,
            'content_html' => $contentHtml,
            'provider' => $activeProvider['provider'] ?? 'manual_sections',
            'model' => $activeProvider['model'] ?? 'from_sections',
            'parameters' => ['generated_from_sections' => true]
        ];

        $postId = $this->aiBlogPostModel->create($postData);

        if (!$postId) {
            $this->jsonResponse(['success' => false, 'message' => 'Failed to save blog post from sections', 'blog_post' => $contentHtml], 500);
            return;
        }

        if ($sessionId) {
            $session = $this->sessionModel->findById($sessionId);
            if ($session && $session['user_id'] === $userId) {
                $this->sessionModel->update($sessionId, ['post_id' => $postId, 'status' => 'completed']);
            }
        }
        
        $this->jsonResponse(['success' => true, 'blog_post' => $contentHtml, 'post_id' => $postId]);
    }

    public function get(): void
    {
        if (!Session::isLoggedIn()) {
            $this->jsonResponse(['success' => false, 'message' => 'Authentication required'], 401);
            return;
        }
        if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
            $this->jsonResponse(['success' => false, 'message' => 'Invalid request method'], 405);
            return;
        }
        $postId = isset($_GET['id']) ? (int)$_GET['id'] : null;
        if (!$postId) {
            $this->jsonResponse(['success' => false, 'message' => 'Post ID is required'], 400);
            return;
        }

        $post = $this->aiBlogPostModel->findById($postId);

        if (!$post || $post['user_id'] !== Session::getUserId()) {
            $this->jsonResponse(['success' => false, 'message' => 'Post not found or unauthorized'], ($post ? 403 : 404));
            return;
        }
        
        $seoMetadata = $this->seoModel->findByContent($postId, 'ai_blog_post');
        $this->jsonResponse(['success' => true, 'post' => $post, 'seo_metadata' => $seoMetadata ?: null]);
    }

    public function list(): void
    {
        if (!Session::isLoggedIn()) {
            $this->jsonResponse(['success' => false, 'message' => 'Authentication required'], 401);
            return;
        }
        if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
            $this->jsonResponse(['success' => false, 'message' => 'Invalid request method'], 405);
            return;
        }
        $page = isset($_GET['page']) ? max(1, (int)$_GET['page']) : 1;
        $limit = isset($_GET['limit']) ? min(50, max(1, (int)$_GET['limit'])) : 10;
        $offset = ($page - 1) * $limit;
        $userId = Session::getUserId();

        $posts = $this->aiBlogPostModel->findByUserId($userId, $limit, $offset);
        $total = $this->aiBlogPostModel->countByUserId($userId);

        $this->jsonResponse([
            'success' => true,
            'posts' => $posts,
            'pagination' => [
                'page' => $page,
                'limit' => $limit,
                'total' => $total,
                'total_pages' => $total > 0 ? ceil($total / $limit) : 0
            ]
        ]);
    }

    public function saveState(): void
    {
        if (!Session::isLoggedIn()) {
            $this->jsonResponse(['success' => false, 'message' => 'Authentication required'], 401);
            return;
        }
        if (!in_array($_SERVER['REQUEST_METHOD'], ['POST'])) {
            $this->jsonResponse(['success' => false, 'message' => 'Invalid request method'], 405);
            return;
        }
        $data = json_decode(file_get_contents('php://input'), true) ?: [];

        $userId = Session::getUserId();
        $postId = isset($data['ai_blog_post_id']) ? (int)$data['ai_blog_post_id'] : null;
        $title = trim((string)($data['title'] ?? ''));
        $markdown = (string)($data['markdown'] ?? '');
        $html = (string)($data['html'] ?? '');
        $featuredImageId = isset($data['featured_image_id']) ? (int)$data['featured_image_id'] : null;
        $imageLayouts = is_array($data['image_layouts'] ?? null) ? $data['image_layouts'] : [];

        if ($postId) {
            $post = $this->aiBlogPostModel->findById($postId);
            if (!$post || ($post['user_id'] ?? null) !== $userId) {
                $this->jsonResponse(['success' => false, 'message' => 'Post not found or unauthorized'], 403);
                return;
            }
        }

        // Upsert ai_blog_posts
        if ($postId) {
            $params = is_array($post['parameters'] ?? null) ? $post['parameters'] : [];
            // Merge parameters
            $params['markdown'] = $markdown;
            $params['featured_image_id'] = $featuredImageId;
            $params['image_layouts'] = $imageLayouts;
            $update = [ 'content_html' => $html, 'parameters' => $params ];
            if ($title !== '') { $update['title'] = $title; }
            $ok = $this->aiBlogPostModel->update($postId, $update);
            if (!$ok) {
                $this->jsonResponse(['success' => false, 'message' => 'Failed to update ai_blog_post'], 500);
                return;
            }
        } else {
            $params = [
                'markdown' => $markdown,
                'featured_image_id' => $featuredImageId,
                'image_layouts' => $imageLayouts,
            ];
            $postId = $this->aiBlogPostModel->create([
                'user_id' => $userId,
                'title' => $title !== '' ? $title : 'Untitled',
                'content_html' => $html,
                'parameters' => $params,
            ]);
            if (!$postId) {
                $this->jsonResponse(['success' => false, 'message' => 'Failed to create ai_blog_post'], 500);
                return;
            }
        }

        // Create or update a content session marking transition to SEO
        try {
            $activeSessions = $this->sessionModel->findActiveByUserId($userId);
            $session = null;
            foreach ($activeSessions as $s) {
                if ((int)($s['post_id'] ?? 0) === (int)$postId) { $session = $s; break; }
            }
            if ($session) {
                $this->sessionModel->update((int)$session['id'], [ 'post_id' => $postId, 'session_type' => 'seo', 'status' => 'in_progress' ]);
            } else {
                $this->sessionModel->create([ 'user_id' => $userId, 'session_type' => 'seo', 'status' => 'in_progress', 'post_id' => $postId ]);
            }
        } catch (\Throwable $e) { /* non-fatal */ }

        $this->jsonResponse(['success' => true, 'ai_blog_post_id' => $postId, 'saved_at' => date('c')]);
    }

    public function update(): void
    {
        if (!Session::isLoggedIn()) {
            $this->jsonResponse(['success' => false, 'message' => 'Authentication required'], 401);
            return;
        }
        if (!in_array($_SERVER['REQUEST_METHOD'], ['PUT', 'POST'])) {
            $this->jsonResponse(['success' => false, 'message' => 'Invalid request method'], 405);
            return;
        }
        $data = json_decode(file_get_contents('php://input'), true);
        $postId = isset($data['id']) ? (int)$data['id'] : null;

        if (!$postId) {
            $this->jsonResponse(['success' => false, 'message' => 'Post ID is required'], 400);
            return;
        }

        $post = $this->aiBlogPostModel->findById($postId);
        if (!$post || $post['user_id'] !== Session::getUserId()) {
            $this->jsonResponse(['success' => false, 'message' => 'Post not found or unauthorized'], ($post ? 403 : 404));
            return;
        }

        $updateData = [];
        if (isset($data['title'])) $updateData['title'] = $data['title'];
        if (isset($data['content_html'])) $updateData['content_html'] = $data['content_html'];

        if (empty($updateData)) {
            $this->jsonResponse(['success' => false, 'message' => 'No data to update'], 400);
            return;
        }

        if (!$this->aiBlogPostModel->update($postId, $updateData)) {
            $this->jsonResponse(['success' => false, 'message' => 'Failed to update post'], 500);
            return;
        }
        $updatedPost = $this->aiBlogPostModel->findById($postId);
        $this->jsonResponse(['success' => true, 'message' => 'Post updated successfully', 'post' => $updatedPost]);
    }

    public function convertToRegularPost(): void
    {
        $this->jsonResponse(['success' => false, 'message' => 'Conversion to regular post not fully implemented for new AI post structure.'], 501);
        return; 
    }

    public function updateParameters(): void
    {
        if (!Session::isLoggedIn()) {
            $this->jsonResponse(['success' => false, 'message' => 'Authentication required'], 401);
            return;
        }
        if (!in_array($_SERVER['REQUEST_METHOD'], ['POST'])) {
            $this->jsonResponse(['success' => false, 'message' => 'Invalid request method'], 405);
            return;
        }
        $data = json_decode(file_get_contents('php://input'), true) ?: [];
        $postId = (int)($data['ai_blog_post_id'] ?? 0);
        if ($postId <= 0) {
            $this->jsonResponse(['success' => false, 'message' => 'ai_blog_post_id is required'], 400);
            return;
        }
        $post = $this->aiBlogPostModel->findById($postId);
        if (!$post || ($post['user_id'] ?? null) !== Session::getUserId()) {
            $this->jsonResponse(['success' => false, 'message' => 'Post not found or unauthorized'], 403);
            return;
        }
        $params = is_array($post['parameters'] ?? null) ? $post['parameters'] : [];
        $currentLayouts = is_array($params['image_layouts'] ?? null) ? $params['image_layouts'] : [];
        $updates = is_array($data['image_layouts'] ?? null) ? $data['image_layouts'] : [];
        if (empty($updates)) {
            $this->jsonResponse(['success' => false, 'message' => 'image_layouts updates required'], 400);
            return;
        }
        // Index current layouts by id for easy merge
        $index = [];
        foreach ($currentLayouts as $i => $item) {
            if (isset($item['id'])) $index[$item['id']] = $i;
        }
        foreach ($updates as $u) {
            $id = $u['id'] ?? null;
            if (!$id) continue;
            $alt = $u['alt_text'] ?? null;
            if (isset($index[$id])) {
                $currentLayouts[$index[$id]]['alt_text'] = $alt;
            } else {
                $currentLayouts[] = [ 'id' => $id, 'layout' => 'standard', 'media_id' => null, 'alt_text' => $alt, 'description' => null, 'additional_content_html' => null, 'gallery_parent_id' => null, 'order_index' => 0 ];
            }
        }
        $params['image_layouts'] = $currentLayouts;
        $ok = $this->aiBlogPostModel->update($postId, [ 'parameters' => $params ]);
        if (!$ok) {
            $this->jsonResponse(['success' => false, 'message' => 'Failed to update parameters'], 500);
            return;
        }
        $this->jsonResponse(['success' => true, 'ai_blog_post_id' => $postId, 'saved_at' => date('c')]);
    }

    public function delete(): void
    {
        if (!Session::isLoggedIn()) {
            $this->jsonResponse(['success' => false, 'message' => 'Authentication required'], 401);
            return;
        }
         if (!in_array($_SERVER['REQUEST_METHOD'], ['DELETE', 'POST'])) {
            $this->jsonResponse(['success' => false, 'message' => 'Invalid request method'], 405);
            return;
        }
        $data = json_decode(file_get_contents('php://input'), true);
        $postId = isset($data['id']) ? (int)$data['id'] : null;

        if (!$postId) {
            $this->jsonResponse(['success' => false, 'message' => 'Post ID is required'], 400);
            return;
        }
        $post = $this->aiBlogPostModel->findById($postId);
        if (!$post || $post['user_id'] !== Session::getUserId()) {
            $this->jsonResponse(['success' => false, 'message' => 'Post not found or unauthorized'], ($post ? 403 : 404));
            return;
        }
        if (!$this->aiBlogPostModel->delete($postId)) {
            $this->jsonResponse(['success' => false, 'message' => 'Failed to delete post'], 500);
            return;
        }
        $this->jsonResponse(['success' => true, 'message' => 'Post deleted successfully']);
    }



    /**
     * Generate a URL-friendly slug from title
     */
    private function generateSlug($title)
    {
        $slug = strtolower(trim($title));
        $slug = preg_replace('/[^a-z0-9-]/', '-', $slug);
        $slug = preg_replace('/-+/', '-', $slug);
        $slug = trim($slug, '-');
        return $slug ?: 'untitled-post';
    }

    /**
     * Ensure slug is unique by appending number if needed
     */
    private function ensureUniqueSlug($slug)
    {
        $postModel = new PostModel();
        $originalSlug = $slug;
        $counter = 1;

        while ($postModel->findBySlug($slug)) {
            $slug = $originalSlug . '-' . $counter;
            $counter++;
        }

        return $slug;
    }

    /**
     * Publish functionality placeholder - to be implemented
     */
    public function publish()
    {
        header('Content-Type: application/json');
        http_response_code(501); // Not Implemented
        echo json_encode([
            'success' => false,
            'message' => 'Publish functionality to be implemented.'
        ]);
    }

    // Publish helper methods removed - functionality to be implemented

    /**
     * Convert parameters to array safely
     */
    // Removed getParametersArray - publish functionality to be implemented
    private function getParametersArray_removed($parameters)
    {
        if (is_string($parameters)) {
            $decoded = json_decode($parameters, true);
            return is_array($decoded) ? $decoded : [];
        }
        return is_array($parameters) ? $parameters : [];
    }

    /**
     * Convert image layouts to array safely
     */
    // Removed getImageLayoutsArray - publish functionality to be implemented
    private function getImageLayoutsArray_removed($imageLayouts)
    {
        if (is_string($imageLayouts)) {
            $decoded = json_decode($imageLayouts, true);
            return is_array($decoded) ? $decoded : [];
        }
        return is_array($imageLayouts) ? $imageLayouts : [];
    }

    /**
     * Convert markdown to HTML (basic conversion)
     */
    // Removed convertMarkdownToHtml - publish functionality to be implemented
    private function convertMarkdownToHtml_removed($markdown)
    {
        $html = $markdown;

        // Convert headers
        $html = preg_replace('/^### (.+)$/m', '<h3>$1</h3>', $html);
        $html = preg_replace('/^## (.+)$/m', '<h2>$1</h2>', $html);
        $html = preg_replace('/^# (.+)$/m', '<h1>$1</h1>', $html);

        // Convert paragraphs
        $paragraphs = preg_split('/\n\s*\n/', $html);
        $htmlParagraphs = [];

        foreach ($paragraphs as $paragraph) {
            $paragraph = trim($paragraph);
            if (empty($paragraph)) continue;

            if (preg_match('/^<(h[1-6]|hr|div)/', $paragraph)) {
                $htmlParagraphs[] = $paragraph;
            } else {
                // Convert formatting
                $paragraph = preg_replace('/\*\*(.+?)\*\*/', '<strong>$1</strong>', $paragraph);
                $paragraph = preg_replace('/\*(.+?)\*/', '<em>$1</em>', $paragraph);
                $htmlParagraphs[] = '<p>' . $paragraph . '</p>';
            }
        }

        return implode("\n", $htmlParagraphs);
    }

    /**
     * Process images with standard layout only
     */
    // Removed processStandardImages - publish functionality to be implemented
    private function processStandardImages_removed($html, $imageLayouts)
    {
        // Convert IMAGE XML tags to proper img elements
        $html = preg_replace_callback(
            '/<IMAGE\s+alt_text="([^"]*?)"\s+layout="([^"]*?)"\s*>\s*<description>([^<]*?)<\/description>\s*<\/IMAGE>/i',
            function($matches) use ($imageLayouts) {
                $altText = $matches[1];
                $layout = $matches[2];
                $description = $matches[3];

                // Only process standard layout
                if ($layout !== 'standard') {
                    return '';
                }

                // Find matching image layout
                $mediaId = $this->findMediaIdByAltText($altText, $imageLayouts);
                if (!$mediaId) {
                    return '';
                }

                // Get media info
                $mediaInfo = $this->getMediaInfo($mediaId);
                if (!$mediaInfo) {
                    return '';
                }

                // Create img tag
                return sprintf(
                    '<img src="%s" alt="%s" class="post-image standard-layout" loading="lazy">',
                    htmlspecialchars($mediaInfo['filepath'], ENT_QUOTES),
                    htmlspecialchars($altText, ENT_QUOTES)
                );
            },
            $html
        );

        return $html;
    }

    /**
     * Find media ID by alt text in image layouts
     */
    // Removed findMediaIdByAltText - publish functionality to be implemented
    private function findMediaIdByAltText_removed($altText, $imageLayouts)
    {
        foreach ($imageLayouts as $layout) {
            if (($layout['alt_text'] ?? '') === $altText) {
                return $layout['media_id'] ?? null;
            }
        }
        return null;
    }

    /**
     * Get media information by ID
     */
    // Removed getMediaInfo - publish functionality to be implemented
    private function getMediaInfo_removed($mediaId)
    {
        try {
            $db = \Brenzley\Core\Database::getInstance();
            $stmt = $db->prepare("SELECT filepath, alt_text, filename FROM media WHERE id = ?");
            $stmt->execute([$mediaId]);
            return $stmt->fetch(PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            error_log("Error fetching media info for ID $mediaId: " . $e->getMessage());
            return null;
        }
    }









    /**
     * Generate excerpt from HTML content
     */
    // Removed generateExcerpt - publish functionality to be implemented
    private function generateExcerpt_removed($html, $length = 160)
    {
        $text = strip_tags($html);
        $text = preg_replace('/\s+/', ' ', $text);
        $text = trim($text);

        if (strlen($text) <= $length) {
            return $text;
        }

        return substr($text, 0, $length) . '...';
    }
}
