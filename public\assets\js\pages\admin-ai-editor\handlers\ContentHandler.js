import { showNotification, stripHtml } from '../utils/EditorUtils.js';

/**
 * Handles the "Content" step functionalities including content generation with image placements.
 */
export class ContentHandler {
    constructor(uiManager, stateManager, chatFeature, contentEditorHandler, imageFeature) {
        this.uiManager = uiManager;
        this.stateManager = stateManager;
        this.chatFeature = chatFeature;
        this.contentEditorHandler = contentEditorHandler;
        this.imageFeature = imageFeature; // For rendering image placeholders
        this.elements = uiManager.elements;
        this.currentStreamBuffer = "";
        this.processedLength = 0; // Track how much content we've already processed
        this.imageTagBuffer = ""; // Buffer for partial image tags
        this.isInImageTag = false; // Track if we're currently inside an image tag
        this.imageTagStartIndex = -1; // Track where the current image tag started
        this.isGenerating = false; // Track if content generation is active

        // Streaming performance buffers
        this.pendingMarkdown = ""; // markdown delta waiting to render
        this.appendScheduled = false; // rAF scheduling flag
        
        const imageExtension = {
            type: 'output',
            regex: /<IMAGE[\s\S]*?<\/IMAGE>/g,
            replace: (match) => {
                return match;
            }
        };
        this.markdownConverter = new showdown.Converter({
            extensions: [imageExtension],
            noHeaderId: true,
            simplifiedAutoLink: true,
            strikethrough: true,
            tables: true,
            tasklists: true,
            ghCodeBlocks: true,
            emoji: true,
            underline: true,
            literalMidWordUnderscores: true,
            smoothLivePreview: true
        });
        this.domParser = new DOMParser();
    }

    init() {
        const generateInitialBtn = document.getElementById('ai-generate-initial-content');
        const regenerateBtn = this.elements.regenerateContentBtn; // Already cached
        const continueBtn = this.elements.continueToSeoBtn; // Already cached

        if (generateInitialBtn) {
            generateInitialBtn.addEventListener('click', () => {
                this.generateContent();
                generateInitialBtn.style.display = 'none';
                if (regenerateBtn) regenerateBtn.style.display = 'inline-flex';
                if (continueBtn) continueBtn.style.display = 'inline-flex';
            });
        }

        this.elements.contentArea.addEventListener('scroll', () => {
            const el = this.elements.contentArea;
            const atBottom = el.scrollHeight - el.scrollTop - el.clientHeight < 1;
            this.isUserScrolledUp = !atBottom;
        });

        // Add global protection for image placeholders in the content area
        this.setupImagePlaceholderProtection();

        if (regenerateBtn) {
            regenerateBtn.addEventListener('click', () => this.generateContent());
        }
        
        if (continueBtn) {
            continueBtn.addEventListener('click', async () => {
                try {
                    continueBtn.disabled = true;
                    await this.saveStateBeforeSeo();
                    this.stateManager.setCurrentStep('seo');
                    document.dispatchEvent(new CustomEvent('aieditor:stepChange', { detail: { step: 'seo' } }));
                } catch (e) {
                    showNotification(e.message || 'Failed to save before SEO', 'error');
                } finally {
                    continueBtn.disabled = false;
                }
            });
        }
    }

   // Serialize editor state and persist before entering SEO
   async saveStateBeforeSeo() {
       const state = this.stateManager.getState();
       const title = state.title || this.elements.titleInput?.value || 'Untitled';
       const html = this.elements.contentArea?.innerHTML || '';

       // Collect image layouts from DOM
       const image_layouts = [];
       const placeholders = this.elements.contentArea?.querySelectorAll('.ai-image-placeholder') || [];
       placeholders.forEach((el, idx) => {
           const id = el.id || `image-${idx+1}`;
           const layout = el.dataset.layout || 'standard';
           const media_id = el.dataset.mediaId ? Number(el.dataset.mediaId) : null;
           const alt_text = (el.querySelector('img.generated-image')?.alt || '') || (el.querySelector('.placeholder-alt')?.textContent || '') || null;
           const description = el.dataset.description || null;
           const additional_content_html = (layout === 'text_overlay') ? (el.querySelector('.image-text-overlay-content')?.innerHTML || null)
               : ((layout === 'left' || layout === 'right') ? (el.closest('.image-text-wrapper')?.querySelector('.adjacent-text-content')?.innerHTML || null) : null);
           const gallery_parent = el.closest('.ai-image-gallery');
           const gallery_parent_id = gallery_parent ? (gallery_parent.id || null) : null;
           const order_index = idx;
           image_layouts.push({ id, layout, media_id, alt_text, description, additional_content_html, gallery_parent_id, order_index });
       });

       const payload = {
           ai_blog_post_id: state.aiBlogPostId || null,
           title,
           markdown: state.parameters?.markdown || '',
           html,
           featured_image_id: this.elements.hiddenFeaturedImage?.value ? Number(this.elements.hiddenFeaturedImage.value) : null,
           image_layouts,
       };
       const res = await fetch('/api/admin/ai/blog-posts/save-state', {
           method: 'POST',
           headers: { 'Content-Type': 'application/json', 'X-Requested-With': 'XMLHttpRequest' },
           body: JSON.stringify(payload)
       });
       const data = await res.json().catch(() => ({}));
       if (!res.ok || !data.success) {
           throw new Error(data.message || 'Failed to save AI editor state');
       }
       this.stateManager.updateState('aiBlogPostId', data.ai_blog_post_id);
       this.uiManager.updateSaveStatus(`Last saved: ${new Date().toLocaleTimeString()}`);
       return true;
   }

    generateContent() {
        const outline = this.stateManager.getStateValue('outline');
        let title = this.stateManager.getStateValue('title');
        const ideaPreset = this.stateManager.getStateValue('ideaPreset');
        const selectedIdea = this.stateManager.getStateValue('selectedIdea');

        if (!outline || outline.length === 0) {
            showNotification('Please approve an outline first.', 'warning');
            return;
        }
        if (!title && selectedIdea?.title) {
            title = selectedIdea.title;
            this.contentEditorHandler.setTitle(title); // Update title input and state
        } else if (!title) {
            showNotification('Please provide an article title.', 'warning');
            this.elements.titleInput.focus();
            return;
        }

        // Set generation state
        this.setGenerationState(true);

        if (this.elements.regenerateContentBtn) this.elements.regenerateContentBtn.disabled = true;
        if (this.elements.continueToSeoBtn) this.elements.continueToSeoBtn.disabled = true;
        this.elements.contentArea.innerHTML = '<div class="ai-status-message">Generating full article content...</div>'; 
        const retryContainer = document.createElement('div');
        retryContainer.className = 'ai-retry-container';
        this.elements.contentArea.appendChild(retryContainer);
        
        // Reset all streaming state
        this.currentStreamBuffer = "";
        this.processedLength = 0;
        this.imageTagBuffer = "";
        this.isInImageTag = false;
        this.imageTagStartIndex = -1;

        this.chatFeature.addAssistantMessage(`Generating the full article for "${title}"... This might take a few moments.`);

        this.streamContentFromAPI();
    }

    /**
     * Sets the generation state and updates image controls accordingly
     * @param {boolean} isGenerating - Whether content generation is active
     */
    setGenerationState(isGenerating) {
        this.isGenerating = isGenerating;
        
        // Update image controls state
        if (this.imageFeature) {
            this.imageFeature.setControlsDisabled(isGenerating);
        }
        
        // Store state globally for other components to access
        window.aiEditorGenerating = isGenerating;
    }

    /**
     * Gets the current generation state
     * @returns {boolean} Whether content generation is active
     */
    isContentGenerating() {
        return this.isGenerating;
    }

    /**
     * Sets up global protection for image placeholders in the content area
     */
    setupImagePlaceholderProtection() {
        if (!this.elements.contentArea) return;

        // Add keydown event listener to prevent deletion of image placeholders
        this.elements.contentArea.addEventListener('keydown', (e) => {
            this.handleImagePlaceholderProtection(e);
        });

        // Add beforeinput event listener for additional protection
        this.elements.contentArea.addEventListener('beforeinput', (e) => {
            this.handleImagePlaceholderProtection(e);
        });

        // Prevent paste operations that might affect image placeholders
        this.elements.contentArea.addEventListener('paste', (e) => {
            // Allow paste but ensure image placeholders remain protected after paste
            setTimeout(() => {
                this.reprotectImagePlaceholders();
            }, 10);
        });
    }

    /**
     * Handles protection of image placeholders from keyboard events
     * @param {Event} e - The keyboard or input event
     */
    handleImagePlaceholderProtection(e) {
        const selection = window.getSelection();
        if (!selection.rangeCount) return;

        const range = selection.getRangeAt(0);
        const { startContainer, endContainer, startOffset, endOffset } = range;

        // Check if we're trying to delete an image placeholder or content adjacent to it
        if (e.key === 'Backspace' || e.key === 'Delete' || 
            (e.type === 'beforeinput' && this.isDestructiveInputType(e.inputType))) {
            
            // Check if selection contains or is adjacent to an image placeholder
            if (this.selectionAffectsImagePlaceholder(range, e.key === 'Backspace' || e.inputType?.includes('Backward'))) {
                e.preventDefault();
                e.stopPropagation();
                return false;
            }
        }
    }

    /**
     * Checks if an input type is destructive (could delete content)
     * @param {string} inputType - The input type from beforeinput event
     * @returns {boolean} Whether the input type is destructive
     */
    isDestructiveInputType(inputType) {
        const destructiveTypes = [
            'deleteContentBackward',
            'deleteContentForward', 
            'deleteByCut',
            'deleteByDrag',
            'deleteContent',
            'deleteEntireSoftLine',
            'deleteHardLineBackward',
            'deleteHardLineForward',
            'deleteSoftLineBackward',
            'deleteSoftLineForward',
            'deleteWordBackward',
            'deleteWordForward'
        ];
        return destructiveTypes.includes(inputType);
    }

    /**
     * Checks if a selection affects an image placeholder
     * @param {Range} range - The current selection range
     * @param {boolean} isBackward - Whether the deletion is backward (backspace)
     * @returns {boolean} Whether the selection affects an image placeholder
     */
    selectionAffectsImagePlaceholder(range, isBackward = false) {
        const { startContainer, endContainer, startOffset, endOffset } = range;

        // Helper function to find image placeholder ancestor
        const findImagePlaceholder = (node) => {
            while (node && node !== this.elements.contentArea) {
                if (node.classList && node.classList.contains('ai-image-placeholder')) {
                    return node;
                }
                node = node.parentNode;
            }
            return null;
        };

        // Check if selection starts or ends within an image placeholder
        const startPlaceholder = findImagePlaceholder(startContainer);
        const endPlaceholder = findImagePlaceholder(endContainer);

        if (startPlaceholder || endPlaceholder) {
            return true;
        }

        // Check if we're at the boundary of an image placeholder
        if (range.collapsed) {
            const container = startContainer;
            const offset = startOffset;

            // For backward deletion (backspace), check the previous sibling
            if (isBackward) {
                if (container.nodeType === Node.TEXT_NODE && offset === 0) {
                    const prevSibling = container.previousSibling;
                    if (prevSibling && prevSibling.classList && prevSibling.classList.contains('ai-image-placeholder')) {
                        return true;
                    }
                } else if (container.nodeType === Node.ELEMENT_NODE && offset > 0) {
                    const prevChild = container.childNodes[offset - 1];
                    if (prevChild && prevChild.classList && prevChild.classList.contains('ai-image-placeholder')) {
                        return true;
                    }
                }
            } 
            // For forward deletion (delete key), check the next sibling
            else {
                if (container.nodeType === Node.TEXT_NODE && offset === container.textContent.length) {
                    const nextSibling = container.nextSibling;
                    if (nextSibling && nextSibling.classList && nextSibling.classList.contains('ai-image-placeholder')) {
                        return true;
                    }
                } else if (container.nodeType === Node.ELEMENT_NODE && offset < container.childNodes.length) {
                    const nextChild = container.childNodes[offset];
                    if (nextChild && nextChild.classList && nextChild.classList.contains('ai-image-placeholder')) {
                        return true;
                    }
                }
            }
        }

        return false;
    }

    /**
     * Re-protects image placeholders after operations that might have affected them
     */
    reprotectImagePlaceholders() {
        if (!this.imageFeature) return;
        
        const placeholders = this.elements.contentArea.querySelectorAll('.ai-image-placeholder');
        placeholders.forEach(placeholder => {
            if (!placeholder.getAttribute('data-protected')) {
                // Call the method directly since it's part of the ImageFeature class
                this.imageFeature.makeImagePlaceholderProtected(placeholder);
            }
        });
    }

    async streamContentFromAPI() {
        // Keep existing placeholder/status until first chunk arrives
        let statusMessage = this.elements.contentArea.querySelector('.ai-loading, .ai-status-message');
        if (!statusMessage) {
            statusMessage = document.createElement('div');
            statusMessage.className = 'ai-status-message';
            statusMessage.textContent = 'Generating full article content...';
            this.elements.contentArea.appendChild(statusMessage);
        }
        // Prepare streaming containers appended after status
        // Clear previous stream containers if any
        const oldStatic = this.elements.contentArea.querySelector('#ai-stream-static');
        const oldLive = this.elements.contentArea.querySelector('#ai-stream-live');
        if (oldStatic) oldStatic.remove();
        if (oldLive) oldLive.remove();
        const staticDiv = document.createElement('div');
        staticDiv.id = 'ai-stream-static';
        const liveDiv = document.createElement('div');
        liveDiv.id = 'ai-stream-live';
        this.elements.contentArea.appendChild(staticDiv);
        this.elements.contentArea.appendChild(liveDiv);
        this.staticContainer = staticDiv;
        this.liveContainer = liveDiv;
        this.currentStreamBuffer = ""; // Reset buffer

        const outline = this.stateManager.getStateValue('outline');
        const title = this.stateManager.getStateValue('title');
        const selectedIdea = this.stateManager.getStateValue('selectedIdea');
        const blogIdeaId = selectedIdea?.db_id || null;
        const outlineId = this.stateManager.getStateValue('outlineId') || null;

        try {
            const response = await fetch('/api/admin/ai/blog-posts/stream-content', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: JSON.stringify({
                    title: title,
                    outline: outline,
                    ideaPreset: selectedIdea?.preset || 'professional',
                    blog_idea_id: blogIdeaId,
                    outline_id: outlineId
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const reader = response.body.getReader();
            const decoder = new TextDecoder();
            let done = false;
            let buffer = '';

            while (!done) {
                const { value, done: readerDone } = await reader.read();
                done = readerDone;
                buffer += decoder.decode(value, { stream: true });

                // Process buffer line by line for SSE messages
                let eolIndex;
                while ((eolIndex = buffer.indexOf('\n\n')) !== -1) {
                    const message = buffer.substring(0, eolIndex).trim();
                    buffer = buffer.substring(eolIndex + 2);

                    if (message.startsWith('data:')) {
                        const jsonString = message.substring(5).trim();
                        if (jsonString) {
                            try {
                                const data = JSON.parse(jsonString);
                                this.handleSseMessage(data);
                            } catch (error) {
                                console.error('Error parsing SSE JSON:', error, jsonString);
                            }
                        }
                    }
                }
            }
        } catch (error) {
            console.error('Fetch failed for content streaming:', error);
            showNotification('Connection error during content generation.', 'error');
            this.chatFeature.addAssistantMessage('Sorry, a connection error occurred while generating the article.');
            const retryContainer = this.elements.contentArea.querySelector('.ai-retry-container');
            if (retryContainer) {
                retryContainer.innerHTML = '';
                const retryBtn = document.createElement('button');
                retryBtn.className = 'ai-btn ai-btn-secondary';
                retryBtn.textContent = 'Try Again';
                retryBtn.addEventListener('click', () => this.generateContent());
                retryContainer.appendChild(retryBtn);
            }
            this.resetContentButtons();
        }
    }

    handleSseMessage(data) {
        switch (data.type) {
            case 'log':
                console.log('%cAI Provider Log:', 'color: #2196F3; font-weight: bold;', `\n${data.content}`);
                break;
            case 'chunk':
                // On first chunk, remove placeholder/status message
                const status = this.elements.contentArea.querySelector('.ai-loading, .ai-status-message');
                if (status) status.remove();
                this.handleStreamChunk(data.content);
                break;
            case 'end':
                console.log("Stream ended. Post ID:", data.content.post_id);
                this.finalizeContentStream();
                if (data.content.post_id) {
                    this.stateManager.updateState('aiBlogPostId', data.content.post_id);
                }
                showNotification('Article content generated successfully!', 'success');
                break;
            case 'error':
                const errMsg = data.message || 'Unknown error';
                showNotification(`Error generating content: ${errMsg}`, 'error');
                this.chatFeature.addAssistantMessage(`Error: ${errMsg}`);
                console.error("SSE Error:", errMsg);
                // Show inline error with retry
                this.elements.contentArea.innerHTML = `<div class="ai-error">Error: ${errMsg}</div>`;
                const retry = document.createElement('button');
                retry.className = 'ai-btn ai-btn-secondary';
                retry.textContent = 'Try Again';
                retry.addEventListener('click', () => this.generateContent());
                this.elements.contentArea.appendChild(retry);
                this.resetContentButtons();
                break;
            default:
                console.warn("Unknown SSE event type:", data.type, data);
        }
    }

    resetContentButtons() {
        this.elements.regenerateContentBtn.disabled = false;
        this.elements.continueToSeoBtn.disabled = false;
        
        // Clear generation state
        this.setGenerationState(false);
    }

    handleStreamChunk(chunk) {
        // Accumulate into global buffer for final completeness
        this.currentStreamBuffer += chunk;
        // Append into pending delta and schedule a render pass
        this.pendingMarkdown += chunk;
        if (!this.appendScheduled) {
            this.appendScheduled = true;
            requestAnimationFrame(() => this.flushPendingRender());
        }
    }

    flushPendingRender() {
        this.appendScheduled = false;
        if (!this.pendingMarkdown) return;

        // Only convert the newly arrived markdown delta to HTML
        let delta = this.pendingMarkdown;
        this.pendingMarkdown = '';

        // Split delta by lines and only render complete paragraphs/blocks
        const parts = delta.split(/\n\n+/); // paragraphs separated by blank line
        // Keep last partial paragraph in pending for next tick
        const lastPart = parts.pop();
        if (lastPart && /[^\n]$/.test(lastPart)) {
            // likely partial; keep it pending
            this.pendingMarkdown = lastPart;
        } else if (lastPart) {
            // it's a complete block
            parts.push(lastPart);
        }
        if (parts.length === 0) return;
        const completeMarkdown = parts.join('\n\n');

        // Convert complete markdown blocks to HTML
        const newHtml = this.markdownConverter.makeHtml(completeMarkdown);

        // Convert any IMAGE tags in this delta
        const converted = this.convertImageTagsToPlaceholders(newHtml);

        // Append nodes into live container
        const temp = document.createElement('div');
        temp.innerHTML = converted;
        const frag = document.createDocumentFragment();
        while (temp.firstChild) frag.appendChild(temp.firstChild);
        (this.liveContainer || this.elements.contentArea).appendChild(frag);

        // Reinitialize placeholders for any appended images
        this.imageFeature.reinitializePlaceholders();

        this.scrollToBottom();
    }

    processStreamBufferIncremental() {
        // Look for complete IMAGE tags in the buffer
        const imageTagRegex = /<IMAGE[\s\S]*?<\/IMAGE>/g;
        let lastProcessedIndex = this.processedLength;
        let match;
        
        // Reset regex to start from our last processed position
        imageTagRegex.lastIndex = Math.max(0, this.processedLength - 20); // Look back a bit to catch tags that span chunks
        
        // Find all complete image tags
        const newImageTags = [];
        while ((match = imageTagRegex.exec(this.currentStreamBuffer)) !== null) {
            if (match.index >= lastProcessedIndex) {
                newImageTags.push({
                    content: match[0],
                    startIndex: match.index,
                    endIndex: match.index + match[0].length
                });
            }
        }
        
        // Check if we're in the middle of an incomplete image tag
        const incompleteImageMatch = this.findIncompleteImageTag();
        
        // Determine how much content we can safely process
        let contentToProcess = this.currentStreamBuffer;
        if (incompleteImageMatch.found) {
            // Don't process the incomplete image tag part
            contentToProcess = this.currentStreamBuffer.substring(0, incompleteImageMatch.startIndex);
        }
        
        // Only update if we have new content to process
        if (contentToProcess.length > this.processedLength) {
            // Convert markdown to HTML for the processable content
            let html = this.markdownConverter.makeHtml(contentToProcess);
            
            // Convert custom IMAGE tags to interactive placeholders
            html = this.convertImageTagsToPlaceholders(html);
            
            // Update the content area with the processed HTML
            // Append rather than replace whole content to reduce layout thrash
        const tempContainer = document.createElement('div');
        tempContainer.innerHTML = html;
        const frag = document.createDocumentFragment();
        while (tempContainer.firstChild) frag.appendChild(tempContainer.firstChild);
        // If this is the first render, clear existing; else just append
        if (this.processedLength === 0 && this.elements.contentArea.childNodes.length <= 1) {
            this.elements.contentArea.innerHTML = '';
        }
        this.elements.contentArea.appendChild(frag);
            
            // Initialize any new image placeholders that were completed
            if (newImageTags.length > 0) {
                this.imageFeature.reinitializePlaceholders();
            }
            
            // Update processed length
            this.processedLength = contentToProcess.length;
        }
        
        this.scrollToBottom();
    }

    findIncompleteImageTag() {
        const buffer = this.currentStreamBuffer;
        
        // Look for the start of an IMAGE tag that might not be complete
        const imageStartRegex = /<IMAGE(?![^>]*<\/IMAGE>)/g;
        let match;
        let lastIncompleteStart = -1;
        
        // Find the last incomplete IMAGE tag start
        while ((match = imageStartRegex.exec(buffer)) !== null) {
            // Check if this IMAGE tag is complete by looking for its closing tag
            const remainingBuffer = buffer.substring(match.index);
            const closeTagMatch = remainingBuffer.match(/<\/IMAGE>/);
            
            if (!closeTagMatch) {
                // This is an incomplete image tag
                lastIncompleteStart = match.index;
            }
        }
        
        if (lastIncompleteStart !== -1) {
            return {
                found: true,
                startIndex: lastIncompleteStart
            };
        }
        
        return { found: false };
    }



    finalizeContentStream() {
        // If nothing came back, show a clear error and retry option
        const plain = stripHtml(this.currentStreamBuffer || '').trim();
        if (!plain) {
            showNotification('No content received from API.', 'error');
            this.elements.contentArea.innerHTML = '<div class="ai-error">No content received from API.</div>';
            const retry = document.createElement('button');
            retry.className = 'ai-btn ai-btn-secondary';
            retry.textContent = 'Try Again';
            retry.addEventListener('click', () => this.generateContent());
            this.elements.contentArea.appendChild(retry);
            this.resetContentButtons();
            this.setGenerationState(false);
            return;
        }

        // Process any remaining content in the buffer
        // Render the final content into the static container
        const html = this.convertImageTagsToPlaceholders(this.markdownConverter.makeHtml(this.currentStreamBuffer));
        if (this.staticContainer && this.liveContainer) {
            this.staticContainer.innerHTML = html;
            this.liveContainer.innerHTML = '';
        } else {
            this.elements.contentArea.innerHTML = html;
        }
        this.imageFeature.initializeImagePlaceholders();
        
        this.stateManager.updateState('content', this.elements.contentArea.innerHTML);
        this.resetContentButtons();
        this.chatFeature.addAssistantMessage("Full article content has been generated!");
        this.currentStreamBuffer = "";
        
        // Clear generation state
        this.setGenerationState(false);
    }


    scrollToBottom() {
        if (!this.isUserScrolledUp) {
            this.elements.contentArea.scrollTop = this.elements.contentArea.scrollHeight;
        }
    }

    /**
     * Converts custom <IMAGE> tags to interactive image placeholders
     * @param {string} html - HTML content containing IMAGE tags
     * @returns {string} HTML with IMAGE tags converted to placeholders
     */
    convertImageTagsToPlaceholders(html) {
        const imageTagRegex = /<IMAGE\s+([^>]*?)>([\s\S]*?)<\/IMAGE>/g;
        let imageCounter = 0;
        
        return html.replace(imageTagRegex, (match, attributes, innerContent) => {
            imageCounter++;
            
            // Parse attributes
            const attrs = this.parseImageAttributes(attributes);
            const altText = attrs.alt_text || `Image ${imageCounter}`;
            const layout = attrs.layout || 'standard';
            
            // Parse inner content to get all prompts and descriptions
            const parsedContent = this.parseImageInnerContent(innerContent);
            
            // Handle gallery layouts (2-item, 3-item)
            if (layout === 'gallery-2-item' || layout === 'gallery-3-item') {
                return this.createGalleryPlaceholders(layout, altText, parsedContent, imageCounter);
            }
            
            // Handle single image layouts
            const imageId = `ai-image-${Date.now()}-${imageCounter}`;
            
            // All images are now manual - use description if available
            const statusText = parsedContent.descriptions.length > 0 ? parsedContent.descriptions[0] : 'Manual image upload required';
            
            // Validate layout-specific requirements
            if ((layout === 'text_overlay' || layout === 'left' || layout === 'right') && !parsedContent.additionalContent) {
                console.warn(`Layout '${layout}' requires additional content but none was provided for image ${imageId}`);
            }
            
            // Create the placeholder using ImageFeature's method
            const placeholderHtml = this.imageFeature.createImagePlaceholder(
                imageId,
                layout,
                altText,
                'manual', // Always manual now
                statusText,
                true, // Always manual
                parsedContent.additionalContent
            );
            
            return placeholderHtml;
        });
    }

    /**
     * Creates gallery placeholders for multi-item layouts
     * @param {string} layout - Gallery layout (gallery-2-item or gallery-3-item)
     * @param {string} altText - Alt text for the gallery
     * @param {object} parsedContent - Parsed content with descriptions
     * @param {number} imageCounter - Current image counter
     * @returns {string} HTML for the gallery container with multiple placeholders
     */
    createGalleryPlaceholders(layout, altText, parsedContent, imageCounter) {
        const expectedCount = layout === 'gallery-2-item' ? 2 : 3;
        
        // Create individual placeholders for each gallery item
        const placeholders = [];
        for (let i = 0; i < expectedCount; i++) {
            const itemId = `ai-gallery-${Date.now()}-${imageCounter}-${i + 1}`;
            const itemAltText = `${altText} - Item ${i + 1}`;
            
            // All images are now manual
            let statusText = '';
            
            // Check if we have a description for this specific item
            if (i < parsedContent.descriptions.length && parsedContent.descriptions[i]) {
                statusText = parsedContent.descriptions[i];
            }
            // Fallback for missing items
            else {
                statusText = `Gallery item ${i + 1}`;
            }
            
            const placeholderHtml = this.imageFeature.createImagePlaceholder(
                itemId,
                'gallery-item', // Use gallery-item layout for proper styling
                itemAltText,
                'manual', // Always manual now
                statusText,
                true, // Always manual
                ''
            );
            
            placeholders.push(placeholderHtml);
        }
        
        // Generate unique gallery ID for mobile functionality
        const galleryId = `gallery-${Date.now()}-${imageCounter}`;
        
        // Wrap placeholders in gallery container
        const galleryHtml = `
            <div class="ai-image-gallery ai-image-layout-${layout}" data-layout="${layout}">
                <div class="ai-gallery-header">
                    <h3 class="ai-gallery-title">${altText}</h3>
                    <span class="ai-gallery-count">${expectedCount} images</span>
                </div>
                <div class="ai-gallery-grid" data-gallery="${galleryId}">
                    ${placeholders.join('')}
                </div>
                <div class="ai-gallery-dots" id="dots-${galleryId}"></div>
            </div>
        `;
        
        return galleryHtml;
    }

    /**
     * Parses IMAGE tag attributes
     * @param {string} attributeString - The attribute string from the IMAGE tag
     * @returns {object} Parsed attributes
     */
    parseImageAttributes(attributeString) {
        const attrs = {};
        
        // Parse alt_text attribute
        const altMatch = attributeString.match(/alt_text\s*=\s*["']([^"']*?)["']/);
        if (altMatch) {
            attrs.alt_text = altMatch[1];
        }
        
        // Parse layout attribute
        const layoutMatch = attributeString.match(/layout\s*=\s*["']([^"']*?)["']/);
        if (layoutMatch) {
            attrs.layout = layoutMatch[1];
        }
        
        return attrs;
    }

    /**
     * Parses the inner content of an IMAGE tag
     * @param {string} innerContent - The content inside the IMAGE tag
     * @returns {object} Parsed content with descriptions and additional content (no AI prompts)
     */
    parseImageInnerContent(innerContent) {
        const result = {
            descriptions: [],
            additionalContent: '',
            // Legacy support for single image layouts
            description: '',
            hasDescription: false
        };
        
        // Extract ALL description tags
        const descriptionRegex = /<description>([\s\S]*?)<\/description>/g;
        let descriptionMatch;
        while ((descriptionMatch = descriptionRegex.exec(innerContent)) !== null) {
            const descriptionText = descriptionMatch[1].trim();
            if (descriptionText) {
                result.descriptions.push(descriptionText);
            }
        }
        
        // Set legacy properties for backward compatibility
        if (result.descriptions.length > 0) {
            result.description = result.descriptions[0];
            result.hasDescription = true;
        }
        
        // Extract additional content for text overlay or floated layouts
        const additionalMatch = innerContent.match(/<additional_content>([\s\S]*?)<\/additional_content>/);
        if (additionalMatch) {
            result.additionalContent = additionalMatch[1].trim();
        }
        
        // Also check for any remaining text content that might not be in tags
        // This helps with backward compatibility and edge cases
        if (!result.additionalContent) {
            // Remove all known tags and see if there's remaining content
            let remainingContent = innerContent
                .replace(/<description>[\s\S]*?<\/description>/g, '')
                .replace(/<additional_content>[\s\S]*?<\/additional_content>/g, '')
                .trim();
            
            // If there's remaining content and it's not just whitespace/HTML tags
            if (remainingContent && remainingContent.replace(/<[^>]*>/g, '').trim()) {
                result.additionalContent = remainingContent;
            }
        }
        
        return result;
    }
}