import { showNotification, stripHtml } from '../utils/EditorUtils.js';

const SVG_ICON_UPLOAD = `<svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path><polyline points="17 8 12 3 7 8"></polyline><line x1="12" y1="3" x2="12" y2="15"></line></svg>`;
const SVG_ICON_GENERATING = `<svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="ai-icon-spin"><path d="M21 12a9 9 0 1 1-6.219-8.56"></path></svg>`;
const SVG_ICON_PENDING = `<svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><line x1="12" y1="8" x2="12" y2="12"></line><line x1="12" y1="16" x2="12.01" y2="16"></line></svg>`;
const SVG_ICON_IMAGE_DEFAULT = `<svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"><rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect><circle cx="8.5" cy="8.5" r="1.5"></circle><polyline points="21 15 16 10 5 21"></polyline></svg>`;

// Import MediaLibraryDialog - it should be available globally after loading the component
// The MediaLibraryDialog component is loaded via script tag in the admin editor view

/**
 * Manages image features including placeholders, generation, and manual uploads.
 */
export class ImageFeature {
    constructor(uiManager, stateManager, chatFeature) {
        this.uiManager = uiManager;
        this.stateManager = stateManager;
        this.chatFeature = chatFeature; // For adding chat messages related to image operations
        this.elements = uiManager.elements;
        // Initialize MediaLibraryDialog - will be available after component loads
        this.mediaLibraryDialog = null;
        this.initializeMediaLibraryDialog();
        
        // Prompt editor dialog removed - no longer needed for manual-only images
        this.promptEditorDialog = null;

        this.featuredImageSuggester = null;
    }

    /**
     * Initializes MediaLibraryDialog when available
     */
    initializeMediaLibraryDialog() {
        // Try to initialize immediately if available
        if (window.MediaLibraryDialog) {
            this.createMediaLibraryDialog();
        } else {
            // Wait for MediaLibraryDialog to be available
            const checkForDialog = () => {
                if (window.MediaLibraryDialog) {
                    this.createMediaLibraryDialog();
                } else {
                    setTimeout(checkForDialog, 100);
                }
            };
            checkForDialog();
        }
    }

    /**
     * Creates the MediaLibraryDialog instance
     */
    createMediaLibraryDialog() {
        console.log('Creating MediaLibraryDialog instance...');
        try {
            this.mediaLibraryDialog = new window.MediaLibraryDialog({
                onSelect: (media) => {
                    console.log('Media selected:', media);
                    if (this.mediaLibraryDialog.activePlaceholderId) {
                        console.log('Setting manual image for placeholder:', this.mediaLibraryDialog.activePlaceholderId);
                        this.setManualImage(this.mediaLibraryDialog.activePlaceholderId, media);
                    } else {
                        console.warn('No active placeholder ID set');
                    }
                },
                apiEndpoints: {
                    list: '/api/admin/media',
                    upload: '/api/admin/media/upload'
                }
            });
            console.log('MediaLibraryDialog created successfully:', !!this.mediaLibraryDialog);
        } catch (error) {
            console.error('Error creating MediaLibraryDialog:', error);
        }
    }

    /**
     * Initializes the featured image suggester and image placeholder interactions.
     */
    init() {
        console.log('ImageFeature.init() called');
        console.log('MediaLibraryDialog available at init:', !!window.MediaLibraryDialog);
        console.log('this.mediaLibraryDialog at init:', !!this.mediaLibraryDialog);
        
        this.initFeaturedImageSuggester();
        this.initializeImagePlaceholders(); // Ensure this is called to set up existing placeholders
        this.bindResizeListener(); // Add resize listener for responsive galleries
        this.setupContentObserver(); // Watch for new placeholders being added
        
        console.log('ImageFeature.init() completed');
    }

    /**
     * Sets up a MutationObserver to watch for new image placeholders being added to the DOM
     */
    setupContentObserver() {
        if (!this.elements.contentArea) return;

        const observer = new MutationObserver((mutations) => {
            let hasNewPlaceholders = false;
            
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList') {
                    mutation.addedNodes.forEach((node) => {
                        if (node.nodeType === Node.ELEMENT_NODE) {
                            // Check if the added node is a placeholder or contains placeholders
                            if (node.classList && node.classList.contains('ai-image-placeholder')) {
                                hasNewPlaceholders = true;
                            } else if (node.querySelectorAll) {
                                const placeholders = node.querySelectorAll('.ai-image-placeholder');
                                if (placeholders.length > 0) {
                                    hasNewPlaceholders = true;
                                }
                            }
                        }
                    });
                }
            });

            if (hasNewPlaceholders) {
                console.log('New image placeholders detected, reinitializing...');
                this.initializeImagePlaceholders();
            }
        });

        observer.observe(this.elements.contentArea, {
            childList: true,
            subtree: true
        });

        console.log('Content observer set up to watch for new placeholders');
    }

    /**
     * Binds resize listener for responsive gallery functionality
     */
    bindResizeListener() {
        let resizeTimeout;
        window.addEventListener('resize', () => {
            if (resizeTimeout) clearTimeout(resizeTimeout);
            
            resizeTimeout = setTimeout(() => {
                // Reinitialize mobile galleries after resize
                this.initializeMobileGalleries();
            }, 250);
        });
    }

    initFeaturedImageSuggester() {
        if (typeof FeaturedImageSuggester !== 'undefined' && this.elements.container.querySelector('#featured-image-suggester')) {
            this.featuredImageSuggester = new FeaturedImageSuggester({
                containerSelector: '#featured-image-suggester',
                contentSelector: '#ai-content-area', // Ensure this selector is correct
                titleSelector: '#post-title',       // Ensure this selector is correct
                onSelectImage: (image) => this.setFeaturedImage(image),
                apiEndpoints: {
                    suggest: '/api/admin/media/suggest-featured',
                    generate: '/api/admin/media/generate-ai-image'
                }
            });
        } else {
            console.warn('FeaturedImageSuggester component or container not found.');
        }
    }

    setFeaturedImage(image) {
        this.stateManager.updateState('featuredImage', image);
        this.uiManager.updateFeaturedImagePreview(image); // Update UI if this method exists

        if (this.elements.hiddenFeaturedImage) {
            this.elements.hiddenFeaturedImage.value = image.id;
        } else {
            const hiddenInput = document.createElement('input');
            hiddenInput.type = 'hidden';
            hiddenInput.name = 'featured_image_id';
            hiddenInput.id = 'hidden-featured-image'; // Ensure this ID matches cacheDomElements
            hiddenInput.value = image.id;
            if (this.elements.postForm) {
                 this.elements.postForm.appendChild(hiddenInput);
                 this.elements.hiddenFeaturedImage = hiddenInput; // Cache the new element
            } else {
                console.error("Post form not found for featured image hidden input.");
            }
        }
        showNotification('Featured image selected', 'success');
        this.chatFeature.addAssistantMessage(`I've set "${image.filename}" as your featured image.`);
    }


    /**
     * Creates an HTML string for an image placeholder.
     * @param {string} id - Unique ID for the image.
     * @param {string} layout - Layout style (e.g., 'standard', 'wide').
     * @param {string} alt - Alt text for the image.
     * @param {string} status - Initial status (always 'manual' now).
     * @param {string} statusText - Text for the status/description area.
     * @param {boolean} manualOnly - Always true now (kept for compatibility).
     * @param {string} [additionalContentHtml=''] - Optional additional content for text_overlay and floated layouts.
     * @returns {string} HTML string for the placeholder.
     */
    createImagePlaceholder(id, layout, alt, status, statusText, manualOnly = true, additionalContentHtml = '') {
        // All images are now manual only
        const currentStatus = 'manual';
        const statusLabel = 'Manual Image';

        // Generate inner content for manual upload
        let innerContent = this.generatePlaceholderInnerContent(currentStatus, statusText, alt);

        // Create the main placeholder div
        const placeholderDiv = document.createElement('div');
        placeholderDiv.id = id;
        placeholderDiv.className = this.getPlaceholderClassName(layout);
        
        // Set data attributes for state management
        this.setPlaceholderDataAttributes(placeholderDiv, layout, currentStatus, true, statusText, additionalContentHtml);

        // Build the basic placeholder structure
        placeholderDiv.innerHTML = this.buildBasicPlaceholderHTML(innerContent, currentStatus, statusLabel, true);

        // Handle layout-specific enhancements
        return this.enhancePlaceholderForLayout(placeholderDiv, layout, additionalContentHtml);
    }

    /**
     * Generates the inner content for the placeholder based on status
     * @param {string} currentStatus - Current status of the image (always 'manual' now)
     * @param {string} statusText - Status text to display
     * @param {string} alt - Alt text for the image
     * @returns {string} HTML string for inner content
     */
    generatePlaceholderInnerContent(currentStatus, statusText, alt) {
        // We'll determine space availability dynamically after rendering
        return `
            <button class="ai-upload-section ai-upload-button" type="button">
                <div class="ai-upload-content">
                    <div class="ai-placeholder-icon">${SVG_ICON_UPLOAD}</div>
                    <div class="ai-upload-text">Upload Image</div>
                </div>
            </button>
            <div class="ai-image-description-container" data-full-text="${this.escapeHtml(statusText)}">
                <div class="ai-image-description-text">${statusText}</div>
            </div>
            <div class="ai-show-description-cta">
                <button class="ai-show-description-btn" type="button">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                        <circle cx="12" cy="12" r="3"></circle>
                    </svg>
                    Show Description
                </button>
            </div>`;
    }

    /**
     * Escapes HTML characters for safe attribute storage
     * @param {string} text - Text to escape
     * @returns {string} Escaped text
     */
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    /**
     * Gets the appropriate CSS class name for the placeholder
     * @param {string} layout - Layout type
     * @returns {string} CSS class name
     */
    getPlaceholderClassName(layout) {
        if (layout === 'gallery-item') {
            return 'ai-image-placeholder ai-image-layout-gallery-item';
        }
        return `ai-image-placeholder ai-image-layout-${layout}`;
    }

    /**
     * Sets data attributes on the placeholder element
     * @param {HTMLElement} placeholderDiv - The placeholder div element
     * @param {string} layout - Layout type
     * @param {string} currentStatus - Current status (always 'manual' now)
     * @param {boolean} isManualImage - Whether this is a manual image (always true now)
     * @param {string} statusText - Status text
     * @param {string} additionalContentHtml - Additional content HTML
     */
    setPlaceholderDataAttributes(placeholderDiv, layout, currentStatus, isManualImage, statusText, additionalContentHtml) {
        placeholderDiv.dataset.layout = layout;
        placeholderDiv.dataset.status = currentStatus;
        placeholderDiv.dataset.manual = 'true'; // Always manual now
        placeholderDiv.dataset.prompt = ''; // No AI prompts anymore
        placeholderDiv.dataset.description = statusText;
        
        // Store text overlay content for text_overlay layouts
        if (layout === 'text_overlay' && additionalContentHtml) {
            placeholderDiv.dataset.textOverlay = stripHtml(additionalContentHtml);
        }
        
        // Store adjacent content for floated layouts
        if ((layout === 'left' || layout === 'right') && additionalContentHtml) {
            placeholderDiv.dataset.adjacentContent = stripHtml(additionalContentHtml);
        }
    }

    /**
     * Builds the basic HTML structure for the placeholder
     * @param {string} innerContent - Inner content HTML
     * @param {string} currentStatus - Current status (always 'manual' now)
     * @param {string} statusLabel - Status label text (no longer used)
     * @param {boolean} isManualImage - Whether this is a manual image (always true now)
     * @returns {string} HTML string for basic placeholder structure
     */
    buildBasicPlaceholderHTML(innerContent, currentStatus, statusLabel, isManualImage) {
        return `
            <div class="ai-image-placeholder-inner">
                ${innerContent}
            </div>`;
    }

    /**
     * Enhances the placeholder for specific layout types
     * @param {HTMLElement} placeholderDiv - The placeholder div element
     * @param {string} layout - Layout type
     * @param {string} additionalContentHtml - Additional content HTML
     * @returns {string} Enhanced HTML string for the placeholder
     */
    enhancePlaceholderForLayout(placeholderDiv, layout, additionalContentHtml) {
        if (!additionalContentHtml) {
            return placeholderDiv.outerHTML;
        }

        switch (layout) {
            case 'text_overlay':
                return this.createTextOverlayPlaceholder(placeholderDiv, additionalContentHtml);
            
            case 'left':
            case 'right':
                return this.createFloatedPlaceholder(placeholderDiv, layout, additionalContentHtml);
            
            default:
                return placeholderDiv.outerHTML;
        }
    }

    /**
     * Creates a text overlay placeholder with overlay content
     * @param {HTMLElement} placeholderDiv - The placeholder div element
     * @param {string} additionalContentHtml - Additional content for overlay
     * @returns {string} HTML string for text overlay placeholder
     */
    createTextOverlayPlaceholder(placeholderDiv, additionalContentHtml) {
        const overlayContentDiv = document.createElement('div');
        overlayContentDiv.className = 'image-text-overlay-content';
        
        // Sanitize and structure the overlay content
        const sanitizedContent = this.sanitizeOverlayContent(additionalContentHtml);
        overlayContentDiv.innerHTML = sanitizedContent;
        
        // Add overlay content to the placeholder inner div
        const innerDiv = placeholderDiv.querySelector('.ai-image-placeholder-inner');
        if (innerDiv) {
            innerDiv.appendChild(overlayContentDiv);
        }
        
        return placeholderDiv.outerHTML;
    }

    /**
     * Creates a floated placeholder with adjacent text content
     * @param {HTMLElement} placeholderDiv - The placeholder div element
     * @param {string} layout - Layout type ('left' or 'right')
     * @param {string} additionalContentHtml - Additional content for adjacent text
     * @returns {string} HTML string for floated placeholder with wrapper
     */
    createFloatedPlaceholder(placeholderDiv, layout, additionalContentHtml) {
        // Create wrapper for floated image and text
        const wrapper = document.createElement('div');
        wrapper.className = 'image-text-wrapper';
        wrapper.dataset.layout = layout;
        
        // Add the placeholder to the wrapper
        wrapper.appendChild(placeholderDiv);

        // Create and add the adjacent text content
        const textDiv = document.createElement('div');
        textDiv.className = 'adjacent-text-content';
        
        // Sanitize and structure the adjacent text content
        const sanitizedContent = this.sanitizeAdjacentContent(additionalContentHtml);
        textDiv.innerHTML = sanitizedContent;
        wrapper.appendChild(textDiv);

        return wrapper.outerHTML;
    }

    /**
     * All images are now manual - this method is kept for compatibility but always returns true.
     * @param {string} description - Image description or alt text.
     * @returns {boolean} Always true since all images are manual now.
     */
    shouldBeManualImage(description) {
        return true; // All images are manual now
    }

    /**
     * Initializes event listeners for all image placeholders in the content area.
     */
    initializeImagePlaceholders() {
        console.log('initializeImagePlaceholders() called');
        const contentArea = this.elements.contentArea;
        if (!contentArea) {
            console.warn('Content area not found');
            return;
        }

        // Use a Set to avoid attaching listeners multiple times to the same element
        if (!this.initializedPlaceholders) {
            this.initializedPlaceholders = new Set();
        }

        const placeholders = contentArea.querySelectorAll('.ai-image-placeholder');
        console.log('Found placeholders:', placeholders.length);

        placeholders.forEach(placeholder => {
            console.log('Processing placeholder:', placeholder.id);
            if (this.initializedPlaceholders.has(placeholder.id)) {
                console.log('Placeholder already initialized:', placeholder.id);
                return;
            }
            this.initializedPlaceholders.add(placeholder.id);

            // Initialize control functionality
            this.initializePlaceholderControls(placeholder);

            // Ensure proper control positioning for layout type
            this.adjustControlsForLayout(placeholder);
        });

        // Initialize mobile galleries after placeholders are set up
        this.initializeMobileGalleries();
        console.log('initializeImagePlaceholders() completed');
    }

    /**
     * Public method to reinitialize image placeholders (called after content generation)
     */
    reinitializePlaceholders() {
        console.log('reinitializePlaceholders() called manually');
        this.initializeImagePlaceholders();
        
        // Apply current disabled state to newly initialized placeholders
        if (this.isContentGenerating()) {
            this.setControlsDisabled(true);
        }
    }

    /**
     * Initializes control functionality for a specific placeholder
     * @param {HTMLElement} placeholder - The placeholder element
     */
    initializePlaceholderControls(placeholder) {
        const uploadSection = placeholder.querySelector('.ai-upload-button');
        const showDescriptionBtn = placeholder.querySelector('.ai-show-description-btn');
        const showDescriptionCta = placeholder.querySelector('.ai-show-description-cta');
        const descriptionContainer = placeholder.querySelector('.ai-image-description-container');
        const descriptionText = placeholder.querySelector('.ai-image-description-text');

        // Make the entire placeholder non-editable and non-deletable
        this.makeImagePlaceholderProtected(placeholder);

        // Upload section functionality (now clickable)
        if (uploadSection) {
            console.log('Adding click listener to upload section for placeholder:', placeholder.id);
            uploadSection.addEventListener('click', (e) => {
                console.log('Upload section clicked for placeholder:', placeholder.id);
                e.stopPropagation();
                e.preventDefault();
                
                // Check if content generation is active
                if (this.isContentGenerating()) {
                    console.log('Content generation is active, ignoring upload click');
                    return;
                }
                
                this.openMediaLibraryForPlaceholder(placeholder.id);
            });
        } else {
            console.warn('Upload section not found for placeholder:', placeholder.id);
        }

        // Show Description button functionality
        if (showDescriptionBtn && descriptionContainer) {
            showDescriptionBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                e.preventDefault();
                
                const fullDescription = descriptionContainer.dataset.fullText || placeholder.dataset.description || '';
                this.showDescriptionDialog(fullDescription, placeholder.id);
            });
        }

        // Initialize adaptive description behavior
        this.initializeAdaptiveDescription(placeholder);

        // Enhanced hover behavior for complex layouts
        this.setupEnhancedHoverBehavior(placeholder);
        
        // Add change image button if placeholder already has an image
        this.addChangeImageButton(placeholder);
    }

    /**
     * Makes an image placeholder protected from editing and deletion
     * @param {HTMLElement} placeholder - The placeholder element to protect
     */
    makeImagePlaceholderProtected(placeholder) {
        // Set contenteditable to false for the placeholder and all its children
        placeholder.setAttribute('contenteditable', 'false');
        
        // Make all child elements non-editable except for interactive buttons
        const allChildren = placeholder.querySelectorAll('*');
        allChildren.forEach(child => {
            // Skip buttons and interactive elements
            if (!child.matches('button, .ai-upload-button, .ai-description-read-more')) {
                child.setAttribute('contenteditable', 'false');
                child.style.userSelect = 'none';
                child.style.webkitUserSelect = 'none';
                child.style.mozUserSelect = 'none';
                child.style.msUserSelect = 'none';
            }
        });

        // Add data attribute to mark as protected
        placeholder.setAttribute('data-protected', 'true');
        
        // Prevent drag and drop
        placeholder.setAttribute('draggable', 'false');
        
        // Add event listeners to prevent deletion
        placeholder.addEventListener('keydown', this.preventImageDeletion.bind(this));
        placeholder.addEventListener('beforeinput', this.preventImageDeletion.bind(this));
        
        // Prevent context menu on the placeholder (optional)
        placeholder.addEventListener('contextmenu', (e) => {
            e.preventDefault();
        });
    }

    /**
     * Prevents deletion of image placeholders via keyboard
     * @param {Event} e - The keyboard or input event
     */
    preventImageDeletion(e) {
        // Prevent backspace, delete, and other destructive keys
        if (e.key === 'Backspace' || e.key === 'Delete' || 
            e.key === 'Cut' || (e.ctrlKey && e.key === 'x')) {
            e.preventDefault();
            e.stopPropagation();
            return false;
        }
        
        // Prevent input events that could modify content
        if (e.type === 'beforeinput') {
            const destructiveInputTypes = [
                'deleteContentBackward',
                'deleteContentForward', 
                'deleteByCut',
                'deleteByDrag',
                'deleteContent',
                'deleteEntireSoftLine',
                'deleteHardLineBackward',
                'deleteHardLineForward',
                'deleteSoftLineBackward',
                'deleteSoftLineForward',
                'deleteWordBackward',
                'deleteWordForward'
            ];
            
            if (destructiveInputTypes.includes(e.inputType)) {
                e.preventDefault();
                e.stopPropagation();
                return false;
            }
        }
    }

    /**
     * Initializes adaptive description behavior for a placeholder
     * @param {HTMLElement} placeholder - The placeholder element
     */
    initializeAdaptiveDescription(placeholder) {
        const descriptionContainer = placeholder.querySelector('.ai-image-description-container');
        const showDescriptionCta = placeholder.querySelector('.ai-show-description-cta');
        
        if (!descriptionContainer || !showDescriptionCta) {
            return;
        }

        // Check space availability and toggle between modes
        const checkSpaceAndToggle = () => {
            requestAnimationFrame(() => {
                const shouldUseCompactMode = this.shouldUseCompactMode(placeholder);
                
                if (shouldUseCompactMode) {
                    // Hide description container and show CTA button
                    descriptionContainer.classList.add('compact-mode');
                    showDescriptionCta.classList.add('active');
                } else {
                    // Show description container and hide CTA button
                    descriptionContainer.classList.remove('compact-mode');
                    showDescriptionCta.classList.remove('active');
                }
            });
        };

        // Initial check
        checkSpaceAndToggle();

        // Re-check on window resize
        let resizeTimeout;
        const handleResize = () => {
            if (resizeTimeout) clearTimeout(resizeTimeout);
            resizeTimeout = setTimeout(checkSpaceAndToggle, 100);
        };

        window.addEventListener('resize', handleResize);

        // Store cleanup function for later use if needed
        placeholder._cleanupAdaptiveDescription = () => {
            window.removeEventListener('resize', handleResize);
        };
    }

    /**
     * Determines if compact mode should be used based on available space
     * @param {HTMLElement} placeholder - The placeholder element
     * @returns {boolean} Whether to use compact mode
     */
    shouldUseCompactMode(placeholder) {
        const layout = placeholder.dataset.layout;
        
        // Gallery items always use compact mode for space efficiency
        if (layout === 'gallery-item') {
            return true;
        }

        // Very small screens always use compact mode
        if (window.innerWidth <= 320) {
            return true;
        }

        // For other layouts, check available space
        const uploadButton = placeholder.querySelector('.ai-upload-button');
        const descriptionContainer = placeholder.querySelector('.ai-image-description-container');
        
        if (!uploadButton || !descriptionContainer) {
            return false;
        }

        // Get the placeholder's available height
        const placeholderHeight = placeholder.clientHeight;
        const uploadButtonHeight = uploadButton.offsetHeight;
        const containerMargins = this.getElementMargins(descriptionContainer);
        const containerPadding = this.getElementPadding(descriptionContainer);
        
        // Calculate minimum required height for description container
        const minDescriptionHeight = this.getMinDescriptionHeight();
        const requiredSpace = uploadButtonHeight + containerMargins.vertical + containerPadding.vertical + minDescriptionHeight;
        
        // Use compact mode if there's not enough space
        return placeholderHeight < requiredSpace;
    }

    /**
     * Gets the minimum required height for description container based on breakpoint
     * @returns {number} Minimum height in pixels
     */
    getMinDescriptionHeight() {
        const width = window.innerWidth;
        
        if (width <= 320) {
            return 60; // Extra small screens
        } else if (width <= 360) {
            return 70; // Very small mobile
        } else if (width <= 480) {
            return 80; // Mobile
        } else if (width <= 768) {
            return 100; // Tablet
        } else if (width <= 1024) {
            return 110; // Small desktop
        } else {
            return 120; // Desktop
        }
    }

    /**
     * Gets element margins
     * @param {HTMLElement} element - The element to measure
     * @returns {Object} Object with vertical and horizontal margin totals
     */
    getElementMargins(element) {
        const styles = window.getComputedStyle(element);
        return {
            vertical: parseFloat(styles.marginTop) + parseFloat(styles.marginBottom),
            horizontal: parseFloat(styles.marginLeft) + parseFloat(styles.marginRight)
        };
    }

    /**
     * Gets element padding
     * @param {HTMLElement} element - The element to measure
     * @returns {Object} Object with vertical and horizontal padding totals
     */
    getElementPadding(element) {
        const styles = window.getComputedStyle(element);
        return {
            vertical: parseFloat(styles.paddingTop) + parseFloat(styles.paddingBottom),
            horizontal: parseFloat(styles.paddingLeft) + parseFloat(styles.paddingRight)
        };
    }

    /**
     * Checks if content generation is currently active
     * @returns {boolean} Whether content generation is active
     */
    isContentGenerating() {
        return window.aiEditorGenerating === true;
    }

    /**
     * Sets the disabled state for all image controls
     * @param {boolean} disabled - Whether controls should be disabled
     */
    setControlsDisabled(disabled) {
        const contentArea = this.elements.contentArea;
        if (!contentArea) return;

        const allControls = contentArea.querySelectorAll('.ai-image-controls');
        allControls.forEach(controls => {
            if (disabled) {
                controls.classList.add('disabled');
            } else {
                controls.classList.remove('disabled');
            }
        });
    }

    /**
     * Adjusts control positioning based on layout type
     * @param {HTMLElement} placeholder - The placeholder element
     */
    adjustControlsForLayout(placeholder) {
        const layout = placeholder.dataset.layout;
        const controls = placeholder.querySelector('.ai-image-controls');
        
        if (!controls) return;

        // Add layout-specific classes for enhanced styling
        controls.classList.add(`ai-controls-${layout}`);

        // Handle special positioning for text overlay layouts
        if (layout === 'text_overlay') {
            this.adjustTextOverlayControls(placeholder, controls);
        }

        // Handle mobile-specific adjustments
        if (window.innerWidth < 768) {
            this.adjustMobileControls(placeholder, controls);
        }
    }

    /**
     * Sets up enhanced hover behavior for complex layouts
     * @param {HTMLElement} placeholder - The placeholder element
     */
    setupEnhancedHoverBehavior(placeholder) {
        const layout = placeholder.dataset.layout;
        const controls = placeholder.querySelector('.ai-image-controls');
        
        if (!controls) return;

        // For text overlay layouts, ensure controls are always visible on mobile
        if (layout === 'text_overlay' && window.innerWidth < 768) {
            controls.style.opacity = '1';
            return;
        }

        // For floated layouts, add enhanced hover detection
        if (layout === 'left' || layout === 'right') {
            const wrapper = placeholder.closest('.image-text-wrapper');
            if (wrapper) {
                wrapper.addEventListener('mouseenter', () => {
                    controls.style.opacity = '1';
                });
                wrapper.addEventListener('mouseleave', () => {
                    controls.style.opacity = '0';
                });
            }
        }

        // Add touch support for mobile devices
        placeholder.addEventListener('touchstart', () => {
            controls.style.opacity = '1';
            setTimeout(() => {
                if (controls.style.opacity === '1') {
                    controls.style.opacity = '0';
                }
            }, 3000);
        }, { passive: true });
    }

    /**
     * Adjusts controls for text overlay layouts
     * @param {HTMLElement} placeholder - The placeholder element
     * @param {HTMLElement} controls - The controls element
     */
    adjustTextOverlayControls(placeholder, controls) {
        const overlayContent = placeholder.querySelector('.image-text-overlay-content');
        if (overlayContent) {
            // Ensure controls don't interfere with overlay content
            controls.style.zIndex = '20';
            
            // Add backdrop for better visibility
            if (!controls.querySelector('.controls-backdrop')) {
                const backdrop = document.createElement('div');
                backdrop.className = 'controls-backdrop';
                backdrop.style.cssText = `
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    background: linear-gradient(to bottom, rgba(0,0,0,0.6), transparent);
                    border-radius: 0.375rem 0.375rem 0 0;
                    z-index: -1;
                `;
                controls.appendChild(backdrop);
            }
        }
    }

    /**
     * Adjusts controls for mobile devices
     * @param {HTMLElement} placeholder - The placeholder element
     * @param {HTMLElement} controls - The controls element
     */
    adjustMobileControls(placeholder, controls) {
        const layout = placeholder.dataset.layout;
        
        // For floated layouts on mobile, move controls below the image
        if (layout === 'left' || layout === 'right') {
            controls.style.position = 'relative';
            controls.style.top = 'auto';
            controls.style.left = 'auto';
            controls.style.right = 'auto';
            controls.style.opacity = '1';
            controls.style.marginTop = '0.5rem';
            controls.style.background = 'var(--admin-surface)';
            controls.style.border = '1px solid var(--admin-border)';
            controls.style.borderRadius = '0.25rem';
            controls.style.padding = '0.25rem';
        }
    }

    // retryImageGeneration method removed - no longer needed for manual-only images

    /**
     * Sanitizes overlay content for text overlay layouts
     * @param {string} content - Raw HTML content
     * @returns {string} Sanitized HTML content
     */
    sanitizeOverlayContent(content) {
        if (!content) return '';
        
        try {
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = content;
            
            // Remove dangerous elements
            const dangerousElements = tempDiv.querySelectorAll('script, iframe, object, embed, form');
            dangerousElements.forEach(el => el.remove());
            
            let sanitizedContent = tempDiv.innerHTML.trim();
            
            // Ensure content is wrapped in block elements
            if (!sanitizedContent.match(/<(p|div|h[1-6]|ul|ol|blockquote)/i)) {
                sanitizedContent = `<p>${sanitizedContent}</p>`;
            }
            
            return sanitizedContent;
        } catch (error) {
            console.error('Error sanitizing overlay content:', error);
            return `<p>${content.replace(/<[^>]*>/g, '')}</p>`;
        }
    }

    /**
     * Sanitizes adjacent content for floated layouts
     * @param {string} content - Raw HTML content
     * @returns {string} Sanitized HTML content
     */
    sanitizeAdjacentContent(content) {
        if (!content) return '';
        
        try {
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = content;
            
            // Remove dangerous elements
            const dangerousElements = tempDiv.querySelectorAll('script, iframe, object, embed, form');
            dangerousElements.forEach(el => el.remove());
            
            // Add clearfix to last element
            const lastElement = tempDiv.lastElementChild;
            if (lastElement) {
                lastElement.classList.add('clearfix-after');
            }
            
            return tempDiv.innerHTML;
        } catch (error) {
            console.error('Error sanitizing adjacent content:', error);
            return `<p>${content.replace(/<[^>]*>/g, '')}</p>`;
        }
    }

    /**
     * Initializes mobile gallery functionality for responsive layouts
     */
    initializeMobileGalleries() {
        // Only initialize on mobile screens
        if (window.innerWidth >= 768) return;

        const galleries = this.elements.contentArea.querySelectorAll('[data-gallery]');
        
        galleries.forEach(gallery => {
            const galleryId = gallery.dataset.gallery;
            const dotsContainer = document.getElementById(`dots-${galleryId}`);
            const items = gallery.querySelectorAll('.ai-image-placeholder');
            
            if (!dotsContainer || items.length === 0) return;

            // Clear existing dots
            dotsContainer.innerHTML = '';
            
            // Create dots
            items.forEach((item, index) => {
                const dot = document.createElement('div');
                dot.className = 'ai-gallery-dot';
                if (index === 0) dot.classList.add('active');
                
                dot.addEventListener('click', () => {
                    this.scrollToGalleryItem(gallery, index);
                    this.updateActiveDot(dotsContainer, index);
                });
                
                dotsContainer.appendChild(dot);
            });

            // Add scroll listener for auto-updating dots
            let scrollTimeout;
            gallery.addEventListener('scroll', () => {
                if (scrollTimeout) clearTimeout(scrollTimeout);
                
                scrollTimeout = setTimeout(() => {
                    this.updateGalleryActiveDot(gallery, dotsContainer);
                }, 50);
            });

            // Add touch/swipe support
            this.addGallerySwipeSupport(gallery);
        });
    }

    /**
     * Scrolls to a specific gallery item
     */
    scrollToGalleryItem(gallery, index) {
        const items = gallery.querySelectorAll('.ai-image-placeholder');
        if (items[index]) {
            const itemWidth = items[0].offsetWidth + 16; // item width + gap
            gallery.scrollTo({
                left: index * itemWidth,
                behavior: 'smooth'
            });
        }
    }

    /**
     * Updates the active dot based on scroll position
     */
    updateGalleryActiveDot(gallery, dotsContainer) {
        const items = gallery.querySelectorAll('.ai-image-placeholder');
        if (!items.length) return;

        const scrollLeft = gallery.scrollLeft;
        const itemWidth = items[0].offsetWidth + 16; // item width + gap
        const activeIndex = Math.round(scrollLeft / itemWidth);
        
        this.updateActiveDot(dotsContainer, Math.max(0, Math.min(activeIndex, items.length - 1)));
    }

    /**
     * Updates active dot styling
     */
    updateActiveDot(dotsContainer, activeIndex) {
        const dots = dotsContainer.querySelectorAll('.ai-gallery-dot');
        dots.forEach((dot, index) => {
            dot.classList.toggle('active', index === activeIndex);
        });
    }

    /**
     * Adds swipe support to gallery
     */
    addGallerySwipeSupport(gallery) {
        let startX = 0;
        let startY = 0;
        let isScrolling = false;

        gallery.addEventListener('touchstart', (e) => {
            startX = e.touches[0].clientX;
            startY = e.touches[0].clientY;
            isScrolling = false;
        }, { passive: true });

        gallery.addEventListener('touchmove', (e) => {
            if (isScrolling) return;

            const currentX = e.touches[0].clientX;
            const currentY = e.touches[0].clientY;
            const diffX = Math.abs(currentX - startX);
            const diffY = Math.abs(currentY - startY);

            // Determine scroll direction
            if (diffX > diffY) {
                isScrolling = true;
                // Horizontal scroll - let it happen naturally
            }
        }, { passive: true });
    }

    // generateImagePrompts method removed - no longer needed for manual-only images

    // generateAISuggestedPrompt method removed - no longer needed for manual-only images

    // updateImagePlaceholderUI method removed - no longer needed for manual-only images

    // simulateImageGeneration method removed - no longer needed for manual-only images

    // saveGeneratedImageToServer method removed - no longer needed for manual-only images

    /**
     * Shows a dialog with the full image description
     * @param {string} description - Full description text
     * @param {string} placeholderId - ID of the placeholder
     */
    showDescriptionDialog(description, placeholderId) {
        // Create dialog overlay
        const overlay = document.createElement('div');
        overlay.className = 'ai-description-dialog-overlay';
        
        // Create dialog content
        const dialog = document.createElement('div');
        dialog.className = 'ai-description-dialog';
        
        dialog.innerHTML = `
            <div class="ai-description-dialog-header">
                <h3>Image Description</h3>
                <button class="ai-description-dialog-close" type="button" aria-label="Close">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <line x1="18" y1="6" x2="6" y2="18"></line>
                        <line x1="6" y1="6" x2="18" y2="18"></line>
                    </svg>
                </button>
            </div>
            <div class="ai-description-dialog-content">
                <div class="ai-description-text-full">${description}</div>
            </div>
            <div class="ai-description-dialog-footer">
                <button class="ai-btn ai-btn-secondary ai-description-cancel-btn" type="button">
                    Cancel
                </button>
                <button class="ai-btn ai-btn-primary ai-dialog-upload-btn" type="button">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                        <polyline points="17 8 12 3 7 8"></polyline>
                        <line x1="12" y1="3" x2="12" y2="15"></line>
                    </svg>
                    Upload Image
                </button>
            </div>
        `;
        
        overlay.appendChild(dialog);
        document.body.appendChild(overlay);
        
        // Add initial animation state
        overlay.style.opacity = '0';
        dialog.style.transform = 'scale(0.9) translateY(-20px)';
        dialog.style.opacity = '0';
        
        // Trigger animation
        requestAnimationFrame(() => {
            overlay.style.transition = 'opacity 0.3s ease';
            dialog.style.transition = 'all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1)';
            
            overlay.style.opacity = '1';
            dialog.style.transform = 'scale(1) translateY(0)';
            dialog.style.opacity = '1';
        });
        
        // Add event listeners
        const closeBtn = dialog.querySelector('.ai-description-dialog-close');
        const cancelBtn = dialog.querySelector('.ai-description-cancel-btn');
        const uploadBtn = dialog.querySelector('.ai-dialog-upload-btn');
        
        const closeDialog = () => {
            // Animate out
            overlay.style.opacity = '0';
            dialog.style.transform = 'scale(0.9) translateY(-20px)';
            dialog.style.opacity = '0';
            
            setTimeout(() => {
                if (document.body.contains(overlay)) {
                    document.body.removeChild(overlay);
                }
            }, 300);
        };
        
        closeBtn.addEventListener('click', closeDialog);
        cancelBtn.addEventListener('click', closeDialog);
        overlay.addEventListener('click', (e) => {
            if (e.target === overlay) closeDialog();
        });
        
        uploadBtn.addEventListener('click', (e) => {
            e.preventDefault();
            closeDialog();
            this.openMediaLibraryForPlaceholder(placeholderId);
        });
        
        // Close on Escape key
        const handleKeyDown = (e) => {
            if (e.key === 'Escape') {
                closeDialog();
                document.removeEventListener('keydown', handleKeyDown);
            }
        };
        document.addEventListener('keydown', handleKeyDown);
    }

    /**
     * Updates an image prompt and regenerates the image.
     * @param {string} imageId - The ID of the image placeholder.
     * @param {string} newPrompt - The new prompt.
     */
    // updateImagePrompt method removed - no longer needed for manual-only images

    /**
     * Replaces an image by generating a new one with a new prompt.
     * @param {string} imageId - The ID of the image placeholder.
     * @param {string} newPrompt - The new prompt.
     */
    // replaceImage method removed - no longer needed for manual-only images

    /**
     * Opens the media library dialog for a specific placeholder
     * @param {string} placeholderId - The ID of the placeholder
     */
    openMediaLibraryForPlaceholder(placeholderId) {
        console.log('openMediaLibraryForPlaceholder called with:', placeholderId);
        console.log('MediaLibraryDialog available:', !!window.MediaLibraryDialog);
        console.log('this.mediaLibraryDialog:', !!this.mediaLibraryDialog);
        
        if (!this.mediaLibraryDialog) {
            // If dialog isn't ready yet, try to initialize it
            if (window.MediaLibraryDialog) {
                console.log('Creating MediaLibraryDialog...');
                this.createMediaLibraryDialog();
            } else {
                console.error('MediaLibraryDialog not available on window object');
                showNotification('Media library is loading, please try again in a moment.', 'warning');
                return;
            }
        }

        if (!this.mediaLibraryDialog) {
            console.error('Failed to create MediaLibraryDialog');
            showNotification('Failed to initialize media library dialog.', 'error');
            return;
        }

        // Set the active placeholder and open the dialog
        console.log('Setting active placeholder and opening dialog...');
        this.mediaLibraryDialog.activePlaceholderId = placeholderId;
        this.mediaLibraryDialog.open();
    }

    /**
     * Sets a manually selected image for a placeholder.
     * @param {string} placeholderId - The ID of the placeholder.
     * @param {Object} media - The media object (from media library).
     */
    setManualImage(placeholderId, media) {
        // Maintain a simple media cache for SEO step to resolve image URLs when <img> not present
        window.__mediaCache = window.__mediaCache || {};
        if (media && media.id) {
            window.__mediaCache[media.id] = media;
        }
        const placeholder = this.elements.contentArea.querySelector(`#${placeholderId}`);
        if (!placeholder || !media || !media.url) return;
        
        const layout = placeholder.dataset.layout;
        const innerDiv = placeholder.querySelector('.ai-image-placeholder-inner');
        if (!innerDiv) return;
        
        // Handle different layouts differently to preserve structure
        if (layout === 'text_overlay') {
            // For text overlay, preserve the overlay content structure
            let img = innerDiv.querySelector('img.generated-image');
            if (!img) {
                // Create img element and insert it at the beginning
                img = document.createElement('img');
                img.className = 'generated-image';
                innerDiv.insertBefore(img, innerDiv.firstChild);
            }
            
            // Set image properties
            img.src = media.url;
            img.alt = media.alt_text || 'Manually selected image';
            img.style.opacity = '0';
            img.onload = () => {
                img.style.opacity = '1';
            };
            
            // Ensure overlay content exists and is properly positioned
            let overlayContent = innerDiv.querySelector('.image-text-overlay-content');
            if (!overlayContent && placeholder.dataset.textOverlay) {
                // Recreate overlay content if it doesn't exist
                overlayContent = document.createElement('div');
                overlayContent.className = 'image-text-overlay-content';
                
                // Use the sanitized overlay content from dataset or create basic content
                const overlayText = placeholder.dataset.textOverlay;
                if (overlayText) {
                    // Create a simple paragraph if no HTML structure exists
                    const sanitizedContent = overlayText.includes('<') ?
                        this.sanitizeOverlayContent(overlayText) :
                        `<p>${overlayText}</p>`;
                    overlayContent.innerHTML = sanitizedContent;
                } else {
                    overlayContent.innerHTML = '<p>Image overlay text</p>';
                }
                
                innerDiv.appendChild(overlayContent);
            }
        } else {
            // For other layouts, use the standard approach
            let img = placeholder.querySelector('img.generated-image');
            if (!img) {
                // Create img element if it doesn't exist
                img = document.createElement('img');
                img.className = 'generated-image';
                // Clear existing content and add the image
                innerDiv.innerHTML = '';
                innerDiv.appendChild(img);
            }
            
            if (img) {
                img.src = media.url;
                img.alt = media.alt_text || 'Manually selected image';
                img.style.opacity = '0';
                img.onload = () => {
                    img.style.opacity = '1';
                };
            }
        }
        
        // Add change image button if it doesn't exist
        this.addChangeImageButton(placeholder);
        
        // Update placeholder state
        placeholder.dataset.status = 'manual';
        placeholder.dataset.manual = "true";
        placeholder.dataset.mediaId = media.id;
        
        // Update status display
        const statusElement = placeholder.querySelector('.ai-image-status');
        if (statusElement) {
            statusElement.textContent = 'Manual Image';
            statusElement.className = 'ai-image-status ai-image-status-manual';
        }
        
        showNotification('Image updated successfully!', 'success');
        
        if (this.chatFeature) {
            this.chatFeature.addAssistantMessage(`Manually set image "${media.filename}" for placeholder ${placeholderId}.`);
        }
    }

    /**
     * Adds a change image button to a placeholder that has an image
     * @param {HTMLElement} placeholder - The placeholder element
     */
    addChangeImageButton(placeholder) {
        // Check if button already exists
        if (placeholder.querySelector('.ai-change-image-btn')) {
            return;
        }

        // Only add to placeholders that have images
        const img = placeholder.querySelector('img.generated-image');
        if (!img) {
            return;
        }

        // Create the change image button
        const changeBtn = document.createElement('button');
        changeBtn.className = 'ai-change-image-btn';
        changeBtn.type = 'button';
        changeBtn.title = 'Change Image';
        changeBtn.setAttribute('aria-label', 'Change Image');
        
        // Add the upload icon
        changeBtn.innerHTML = `
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                <polyline points="17 8 12 3 7 8"></polyline>
                <line x1="12" y1="3" x2="12" y2="15"></line>
            </svg>
        `;

        // Add click event listener
        changeBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            e.preventDefault();
            
            // Check if content generation is active
            if (this.isContentGenerating()) {
                console.log('Content generation is active, ignoring change image click');
                return;
            }
            
            this.openMediaLibraryForPlaceholder(placeholder.id);
        });

        // Add the button to the placeholder inner div
        const innerDiv = placeholder.querySelector('.ai-image-placeholder-inner');
        if (innerDiv) {
            innerDiv.appendChild(changeBtn);
        }
    }

    /**
     * Removes the change image button from a placeholder
     * @param {HTMLElement} placeholder - The placeholder element
     */
    removeChangeImageButton(placeholder) {
        const changeBtn = placeholder.querySelector('.ai-change-image-btn');
        if (changeBtn) {
            changeBtn.remove();
        }
    }


}