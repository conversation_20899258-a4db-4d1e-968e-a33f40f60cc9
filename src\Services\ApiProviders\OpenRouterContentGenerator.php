<?php

namespace Brenzley\Services\ApiProviders;

/**
 * OpenRouter content generator
 * Uses OpenAI Compatible Content Generator implementation since OpenRouter follows the same API format
 */
class OpenRouterContentGenerator extends OpenAiCompatibleContentGenerator
{
    /**
     * Constructor
     *
     * @param OpenRouterProvider $apiProvider The OpenRouter provider
     */
    public function __construct(OpenRouterProvider $apiProvider)
    {
        parent::__construct($apiProvider);
    }
}
