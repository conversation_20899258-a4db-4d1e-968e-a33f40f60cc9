/**
 * Mobile Utilities for AI Editor
 * Handles mobile-specific interactions and responsive behavior
 */

export class MobileUtils {
    constructor() {
        this.isMobile = window.innerWidth <= 768;
        this.isTablet = window.innerWidth > 768 && window.innerWidth <= 1024;
        this.mobileMenuOpen = false;
        this.chatPanelOpen = false;
        
        this.init();
    }

    init() {
        this.setupMobileMenu();
        this.setupMobileChatPanel();
        this.setupResizeHandler();
        this.setupTouchOptimizations();
    }

    setupMobileMenu() {
        const mobileMenuBtn = document.getElementById('ai-header-mobile-menu');
        const mobileDropdown = document.getElementById('ai-header-mobile-dropdown');
        
        if (!mobileMenuBtn || !mobileDropdown) return;

        mobileMenuBtn.addEventListener('click', (e) => {
            e.preventDefault();
            this.toggleMobileMenu();
        });

        // Close menu when clicking outside
        document.addEventListener('click', (e) => {
            if (this.mobileMenuOpen && 
                !mobileMenuBtn.contains(e.target) && 
                !mobileDropdown.contains(e.target)) {
                this.closeMobileMenu();
            }
        });

        // Sync mobile buttons with desktop buttons
        this.syncMobileButtons();
    }

    setupMobileChatPanel() {
        const chatFab = document.getElementById('ai-chat-fab');
        const chatPanel = document.getElementById('ai-assistant-chat-panel');
        const chatBackdrop = document.getElementById('ai-chat-backdrop');
        const chatCloseBtn = document.getElementById('ai-chat-close-btn');

        if (!chatFab || !chatPanel || !chatBackdrop) return;

        // Override default chat behavior on mobile
        if (this.isMobile) {
            chatFab.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                this.toggleMobileChatPanel();
            });

            if (chatCloseBtn) {
                chatCloseBtn.addEventListener('click', (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    this.closeMobileChatPanel();
                });
            }

            chatBackdrop.addEventListener('click', () => {
                this.closeMobileChatPanel();
            });

            // Prevent body scroll when chat is open
            chatPanel.addEventListener('touchmove', (e) => {
                e.stopPropagation();
            });
        }
    }

    setupResizeHandler() {
        let resizeTimeout;
        window.addEventListener('resize', () => {
            clearTimeout(resizeTimeout);
            resizeTimeout = setTimeout(() => {
                const wasMobile = this.isMobile;
                this.isMobile = window.innerWidth <= 768;
                this.isTablet = window.innerWidth > 768 && window.innerWidth <= 1024;

                // Close mobile menu if switching to desktop
                if (wasMobile && !this.isMobile && this.mobileMenuOpen) {
                    this.closeMobileMenu();
                }

                // Reset chat panel behavior
                if (wasMobile !== this.isMobile) {
                    this.resetChatPanel();
                }
            }, 150);
        });
    }

    setupTouchOptimizations() {
        // Add touch-friendly hover states
        const touchElements = document.querySelectorAll('.ai-btn, .ai-idea-card, .ai-image-placeholder');
        
        touchElements.forEach(element => {
            element.addEventListener('touchstart', () => {
                element.classList.add('touch-active');
            });

            element.addEventListener('touchend', () => {
                setTimeout(() => {
                    element.classList.remove('touch-active');
                }, 150);
            });
        });

        // Prevent double-tap zoom on buttons
        const buttons = document.querySelectorAll('.ai-btn');
        buttons.forEach(button => {
            button.addEventListener('touchend', (e) => {
                e.preventDefault();
                button.click();
            });
        });
    }

    toggleMobileMenu() {
        if (this.mobileMenuOpen) {
            this.closeMobileMenu();
        } else {
            this.openMobileMenu();
        }
    }

    openMobileMenu() {
        const mobileDropdown = document.getElementById('ai-header-mobile-dropdown');
        const mobileMenuBtn = document.getElementById('ai-header-mobile-menu');
        
        if (mobileDropdown && mobileMenuBtn) {
            mobileDropdown.classList.add('show');
            mobileMenuBtn.setAttribute('aria-expanded', 'true');
            this.mobileMenuOpen = true;

            // Update hamburger icon to X
            const icon = mobileMenuBtn.querySelector('svg');
            if (icon) {
                icon.innerHTML = `
                    <line x1="18" y1="6" x2="6" y2="18"></line>
                    <line x1="6" y1="6" x2="18" y2="18"></line>
                `;
            }
        }
    }

    closeMobileMenu() {
        const mobileDropdown = document.getElementById('ai-header-mobile-dropdown');
        const mobileMenuBtn = document.getElementById('ai-header-mobile-menu');
        
        if (mobileDropdown && mobileMenuBtn) {
            mobileDropdown.classList.remove('show');
            mobileMenuBtn.setAttribute('aria-expanded', 'false');
            this.mobileMenuOpen = false;

            // Reset hamburger icon
            const icon = mobileMenuBtn.querySelector('svg');
            if (icon) {
                icon.innerHTML = `
                    <line x1="3" y1="6" x2="21" y2="6"></line>
                    <line x1="3" y1="12" x2="21" y2="12"></line>
                    <line x1="3" y1="18" x2="21" y2="18"></line>
                `;
            }
        }
    }

    toggleMobileChatPanel() {
        if (this.chatPanelOpen) {
            this.closeMobileChatPanel();
        } else {
            this.openMobileChatPanel();
        }
    }

    openMobileChatPanel() {
        const chatPanel = document.getElementById('ai-assistant-chat-panel');
        const chatBackdrop = document.getElementById('ai-chat-backdrop');
        
        if (chatPanel && chatBackdrop) {
            chatBackdrop.classList.add('show');
            chatPanel.classList.add('show');
            this.chatPanelOpen = true;

            // Prevent body scroll
            document.body.style.overflow = 'hidden';
            
            // Focus on chat input
            setTimeout(() => {
                const chatInput = document.getElementById('ai-chat-input');
                if (chatInput) {
                    chatInput.focus();
                }
            }, 300);
        }
    }

    closeMobileChatPanel() {
        const chatPanel = document.getElementById('ai-assistant-chat-panel');
        const chatBackdrop = document.getElementById('ai-chat-backdrop');
        
        if (chatPanel && chatBackdrop) {
            chatPanel.classList.remove('show');
            chatBackdrop.classList.remove('show');
            this.chatPanelOpen = false;

            // Restore body scroll
            document.body.style.overflow = '';
        }
    }

    resetChatPanel() {
        const chatPanel = document.getElementById('ai-assistant-chat-panel');
        const chatBackdrop = document.getElementById('ai-chat-backdrop');
        
        if (chatPanel && chatBackdrop) {
            chatPanel.classList.remove('show');
            chatBackdrop.classList.remove('show');
            this.chatPanelOpen = false;
            document.body.style.overflow = '';
        }
    }

    syncMobileButtons() {
        // Sync save draft buttons
        const saveDraft = document.getElementById('ai-save-draft');
        const saveDraftMobile = document.getElementById('ai-save-draft-mobile');
        
        if (saveDraft && saveDraftMobile) {
            saveDraftMobile.addEventListener('click', () => {
                saveDraft.click();
                this.closeMobileMenu();
            });
        }

        // Publish functionality has been completely removed - show placeholder message
        const publishMobile = document.getElementById('ai-publish-mobile');
        if (publishMobile) {
            publishMobile.addEventListener('click', () => {
                alert('Publish functionality to be implemented.');
            });
            publishMobile.style.opacity = '0.7';
            publishMobile.title = 'Publish functionality to be implemented';
        }

        // Sync preview buttons
        const preview = document.getElementById('ai-preview');
        const previewMobile = document.getElementById('ai-preview-mobile');
        
        if (preview && previewMobile) {
            previewMobile.addEventListener('click', () => {
                preview.click();
                this.closeMobileMenu();
            });
        }

        // Sync save status
        const saveStatus = document.getElementById('ai-save-status');
        const saveStatusMobile = document.getElementById('ai-save-status-mobile');
        
        if (saveStatus && saveStatusMobile) {
            const observer = new MutationObserver(() => {
                saveStatusMobile.textContent = saveStatus.textContent;
            });
            
            observer.observe(saveStatus, { 
                childList: true, 
                subtree: true, 
                characterData: true 
            });
        }
    }

    // Utility methods
    isMobileDevice() {
        return this.isMobile;
    }

    isTabletDevice() {
        return this.isTablet;
    }

    isDesktopDevice() {
        return !this.isMobile && !this.isTablet;
    }

    // Add haptic feedback for supported devices
    addHapticFeedback(element, type = 'light') {
        if (navigator.vibrate && this.isMobile) {
            element.addEventListener('click', () => {
                const patterns = {
                    light: [10],
                    medium: [20],
                    heavy: [30]
                };
                navigator.vibrate(patterns[type] || patterns.light);
            });
        }
    }

    // Optimize scroll performance on mobile
    optimizeScrolling() {
        const scrollElements = document.querySelectorAll('.ai-process-content, .ai-chat-messages');
        
        scrollElements.forEach(element => {
            element.style.webkitOverflowScrolling = 'touch';
            element.style.overscrollBehavior = 'contain';
        });
    }
}

// Auto-initialize on mobile devices
if (window.innerWidth <= 768) {
    document.addEventListener('DOMContentLoaded', () => {
        window.mobileUtils = new MobileUtils();
    });
}