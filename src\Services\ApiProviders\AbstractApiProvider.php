<?php

namespace Brenzley\Services\ApiProviders;

use Brenzley\Services\Logging\Logger;

/**
 * Abstract base class for API providers
 */
abstract class AbstractApiProvider implements StreamingApiProviderInterface
{
    /**
     * API key for the provider
     */
    protected string $apiKey;

    /**
     * Selected model for the provider
     */
    protected ?string $selectedModel;

    /**
     * Context window size for the model
     */
    protected int $contextWindow;

    /**
     * Additional provider-specific settings
     */
    protected array $settings;

    /**
     * Retry handler for API requests
     */
    protected RetryHandler $retryHandler;

    /**
     * Rate limiter for API requests
     */
    protected RateLimiter $rateLimiter;

    /**
     * Usage tracker for API requests
     */
    protected UsageTracker $usageTracker;

    /**
     * Whether to enable request retries
     */
    protected bool $enableRetries;

    /**
     * Whether to enable rate limiting
     */
    protected bool $enableRateLimiting;

    /**
     * Whether to enable usage tracking
     */
    protected bool $enableUsageTracking;

    /**
     * Maximum number of retry attempts
     */
    protected int $maxRetries;

    /**
     * Maximum number of requests per minute
     */
    protected int $requestsPerMinute;

    /**
     * Maximum number of tokens per minute
     */
    protected int $tokensPerMinute;

    /**
     * Constructor
     *
     * @param string $apiKey API key for the provider
     * @param string|null $selectedModel Selected model for the provider
     * @param int $contextWindow Context window size for the model
     * @param array $settings Additional provider-specific settings
     */
    public function __construct(string $apiKey, ?string $selectedModel = null, int $contextWindow = 8096, array $settings = [])
    {
        $this->apiKey = $apiKey;
        $this->selectedModel = $selectedModel;
        $this->contextWindow = $contextWindow;
        $this->settings = $settings;

        // Initialize retry settings from provider settings or use defaults
        $this->enableRetries = $settings['enable_retries'] ?? true;
        $this->maxRetries = $settings['max_retries'] ?? 3;
        $baseDelay = $settings['retry_base_delay'] ?? 1.0;
        $maxDelay = $settings['retry_max_delay'] ?? 30.0;
        $jitter = $settings['retry_jitter'] ?? 0.1;

        // Initialize rate limiting settings
        $this->enableRateLimiting = $settings['enable_rate_limiting'] ?? true;
        $this->requestsPerMinute = $settings['requests_per_minute'] ?? 60;
        $this->tokensPerMinute = $settings['tokens_per_minute'] ?? 100000;

        // Initialize usage tracking settings
        $this->enableUsageTracking = $settings['enable_usage_tracking'] ?? true;

        // Initialize handlers
        $this->retryHandler = new RetryHandler($this->maxRetries, $baseDelay, $maxDelay, $jitter);
        $this->rateLimiter = new RateLimiter(
            $this->getProviderName(),
            $this->requestsPerMinute,
            $this->tokensPerMinute,
            $this->enableRateLimiting
        );
        $this->usageTracker = new UsageTracker($this->getProviderName(), $this->enableUsageTracking);
    }

    /**
     * Make a HTTP request to the API
     *
     * @param string $url The URL to request
     * @param string $method The HTTP method (GET, POST, etc.)
     * @param array $headers The HTTP headers
     * @param array|null $data The request data (for POST, PUT, etc.)
     * @param string $endpoint The API endpoint name (for usage tracking)
     * @param int $inputTokens Number of input tokens (for usage tracking)
     * @return array Response data, HTTP code, and any error message
     */
    protected function makeRequest(
        string $url,
        string $method = 'GET',
        array $headers = [],
        ?array $data = null,
        string $endpoint = 'unknown',
        int $inputTokens = 0
    ): array {
        // Check rate limits before making the request
        if (!$this->rateLimiter->canMakeRequest()) {
            $waitTime = $this->rateLimiter->getWaitTime();

            Logger::warning("Rate limit reached for {$this->getProviderName()}, waiting {$waitTime} seconds", [
                'provider' => $this->getProviderName(),
                'wait_time' => $waitTime
            ]);

            // Wait if the wait time is reasonable, otherwise return an error
            if ($waitTime <= 10) {
                sleep($waitTime);
            } else {
                return [
                    'response' => json_encode([
                        'error' => [
                            'message' => "Rate limit exceeded. Please try again in {$waitTime} seconds."
                        ]
                    ]),
                    'http_code' => 429,
                    'error' => "Rate limit exceeded. Please try again in {$waitTime} seconds."
                ];
            }
        }

        $startTime = microtime(true);

        // If retries are disabled, make a single request
        if (!$this->enableRetries) {
            $result = $this->executeSingleRequest($url, $method, $headers, $data);
        } else {
            // Use retry handler to execute the request with retry logic
            $result = $this->retryHandler->execute(
                // Function to execute
                fn() => $this->executeSingleRequest($url, $method, $headers, $data),
                // Function to determine if an error is retryable
                function (array $result) {
                    // Retry on curl errors (network issues)
                    if (!empty($result['error'])) {
                        return RetryHandler::isRetryableCurlError($result['error']);
                    }

                    // Retry on certain HTTP status codes
                    if (isset($result['http_code'])) {
                        return RetryHandler::isRetryableHttpStatus($result['http_code']);
                    }

                    return false;
                },
                // Function to call before each retry (for logging)
                function (int $attempt, float $delay, array $lastResult) use ($url, $method) {
                    $this->logRetryAttempt($url, $method, $attempt, $delay, $lastResult);
                }
            );
        }

        $endTime = microtime(true);
        $latency = $endTime - $startTime;

        // Track the request for rate limiting
        $this->rateLimiter->trackRequest();

        // Track the request for usage tracking
        $outputTokens = $this->estimateOutputTokens($result);
        $success = !empty($result['response']) && empty($result['error']) && $result['http_code'] >= 200 && $result['http_code'] < 300;

        $this->usageTracker->trackRequest(
            $endpoint,
            $this->selectedModel ?? 'unknown',
            $inputTokens,
            $outputTokens,
            $success,
            $latency
        );

        return $result;
    }

    /**
     * Estimate the number of output tokens in a response
     *
     * @param array $result The API response
     * @return int Estimated number of output tokens
     */
    protected function estimateOutputTokens(array $result): int
    {
        // If the response is empty or there was an error, return 0
        if (empty($result['response']) || !empty($result['error']) || $result['http_code'] < 200 || $result['http_code'] >= 300) {
            return 0;
        }

        // Try to parse the response as JSON
        $data = json_decode($result['response'], true);

        // If the response is not valid JSON, estimate based on string length
        if (json_last_error() !== JSON_ERROR_NONE) {
            // Rough estimate: 1 token per 4 characters
            return (int)(strlen($result['response']) / 4);
        }

        // Check if the response contains token usage information
        if (isset($data['usage']['completion_tokens'])) {
            return (int)$data['usage']['completion_tokens'];
        }

        if (isset($data['usage']['total_tokens']) && isset($data['usage']['prompt_tokens'])) {
            return (int)$data['usage']['total_tokens'] - (int)$data['usage']['prompt_tokens'];
        }

        // If no token usage information is available, estimate based on content
        if (isset($data['choices'][0]['message']['content'])) {
            // Rough estimate: 1 token per 4 characters
            return (int)(strlen($data['choices'][0]['message']['content']) / 4);
        }

        if (isset($data['candidates'][0]['content']['parts'][0]['text'])) {
            // Rough estimate: 1 token per 4 characters
            return (int)(strlen($data['candidates'][0]['content']['parts'][0]['text']) / 4);
        }

        // Default fallback: estimate based on total response length
        return (int)(strlen($result['response']) / 4);
    }

    /**
     * Execute a single HTTP request without retry logic
     *
     * @param string $url The URL to request
     * @param string $method The HTTP method (GET, POST, etc.)
     * @param array $headers The HTTP headers
     * @param array|null $data The request data (for POST, PUT, etc.)
     * @return array Response data, HTTP code, and any error message
     */
    protected function executeSingleRequest(string $url, string $method = 'GET', array $headers = [], ?array $data = null): array
    {
        $ch = curl_init($url);

        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $method);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 2);

        if ($data !== null && in_array($method, ['POST', 'PUT', 'PATCH'])) {
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        }

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);

        curl_close($ch);

        // Log the request (in a real application, you would use a proper logging system)
        $this->logApiRequest($url, $method, $httpCode, $error);

        return [
            'response' => $response,
            'http_code' => $httpCode,
            'error' => $error
        ];
    }

    /**
     * Get the provider name
     *
     * @return string The provider name
     */
    abstract protected function getProviderName(): string;

    /**
     * Log an API request
     *
     * @param string $url The URL requested
     * @param string $method The HTTP method used
     * @param int $httpCode The HTTP status code received
     * @param string $error Any error message
     */
    protected function logApiRequest(string $url, string $method, int $httpCode, string $error): void
    {
        Logger::apiRequest(
            $this->getProviderName(),
            $url,
            $method,
            $httpCode,
            $error
        );
    }

    /**
     * Log a retry attempt
     *
     * @param string $url The URL being requested
     * @param string $method The HTTP method being used
     * @param int $attempt The current attempt number
     * @param float $delay The delay before the next attempt
     * @param array $lastResult The result of the last attempt
     */
    protected function logRetryAttempt(string $url, string $method, int $attempt, float $delay, array $lastResult): void
    {
        $httpCode = $lastResult['http_code'] ?? 0;
        $error = $lastResult['error'] ?? 'Unknown error';

        Logger::apiRetry(
            $this->getProviderName(),
            $url,
            $method,
            $attempt,
            $this->maxRetries,
            $delay,
            $httpCode,
            $error
        );
    }

    /**
     * Parse API error response
     *
     * @param string $response The API response
     * @return string The error message
     */
    protected function parseErrorResponse(string $response): string
    {
        $data = json_decode($response, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            return "Invalid response from API: $response";
        }

        if (isset($data['error']['message'])) {
            return $data['error']['message'];
        }

        if (isset($data['error'])) {
            if (is_string($data['error'])) {
                return $data['error'];
            }

            if (is_array($data['error']) && isset($data['error']['message'])) {
                return $data['error']['message'];
            }
        }

        if (isset($data['message'])) {
            return $data['message'];
        }

        return 'Unknown error occurred';
    }

    /**
     * Stream a completion using the API provider
     *
     * @param string $prompt The prompt to generate a completion for
     * @param callable $callback Callback function to handle each chunk of the stream
     * @param array $options Additional options for the completion
     * @return void
     */
    public function streamCompletion(string $prompt, callable $callback, array $options = []): void
    {
        throw new \RuntimeException('Streaming not implemented for this provider');
    }

    /**
     * Execute a streaming HTTP request
     *
     * @param string $url The URL to request
     * @param array $data The request data
     * @param array $headers The HTTP headers
     * @param callable $callback Callback function to handle each chunk of the stream
     * @param array $options Additional options for the request
     * @return void
     */
    protected function executeStreamingRequest(string $url, array $data, array $headers, callable $callback, array $options = []): void
    {
        $debug = $options['debug'] ?? false;

        // Log request details if debug is enabled
        if ($debug) {
            $debugInfo = [
                'message' => 'Starting streaming request',
                'provider' => $this->getProviderName(),
                'url' => $url,
                'headers' => $headers,
                'data' => $data
            ];
            Logger::info('Streaming request debug', $debugInfo);
        }

        // Initialize curl
        $ch = curl_init($url);

        // Optimized curl options for real-time streaming
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 0); // Don't return as string, use write function
        curl_setopt($ch, CURLOPT_TIMEOUT, 120); // 2 minute timeout
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10); // 10 second connection timeout
        
        // Optimize for streaming performance
        curl_setopt($ch, CURLOPT_BUFFERSIZE, 1024); // Small buffer for immediate processing
        curl_setopt($ch, CURLOPT_TCP_NODELAY, 1); // Disable Nagle's algorithm for faster transmission
        curl_setopt($ch, CURLOPT_FRESH_CONNECT, 1); // Force new connection
        curl_setopt($ch, CURLOPT_FORBID_REUSE, 1); // Don't reuse connection
        
        // SSL optimization
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 2);
        curl_setopt($ch, CURLOPT_SSL_SESSIONID_CACHE, 0); // Disable SSL session cache for streaming
        
        // Enable verbose output if debugging
        if ($debug) {
            curl_setopt($ch, CURLOPT_VERBOSE, 1);
        }

        // Create a context for the callback with performance tracking
        $context = [
            'callback' => $callback,
            'debug' => $debug,
            'provider' => $this->getProviderName(),
            'chunks_received' => 0,
            'chunks_processed' => 0,
            'bytes_received' => 0,
            'start_time' => microtime(true),
            'last_chunk_time' => microtime(true)
        ];

        // Set write function to process chunks with immediate handling
        curl_setopt($ch, CURLOPT_WRITEFUNCTION, function($ch, $data) use (&$context) {
            $currentTime = microtime(true);
            $chunkSize = strlen($data);
            
            // Update statistics
            $context['chunks_received']++;
            $context['bytes_received'] += $chunkSize;
            
            // Calculate chunk processing time for performance monitoring
            $timeSinceLastChunk = $currentTime - $context['last_chunk_time'];
            $context['last_chunk_time'] = $currentTime;

            if ($context['debug'] && $context['chunks_received'] <= 5) {
                Logger::debug('Chunk received', [
                    'provider' => $context['provider'],
                    'chunk_number' => $context['chunks_received'],
                    'chunk_size' => $chunkSize,
                    'time_since_last' => $timeSinceLastChunk,
                    'chunk_preview' => substr($data, 0, 100)
                ]);
            }

            // Process chunk immediately without delay
            try {
                $this->processStreamChunk($data, $context['callback']);
                $context['chunks_processed']++;
            } catch (\Throwable $e) {
                Logger::error('Chunk processing error', [
                    'provider' => $context['provider'],
                    'error' => $e->getMessage(),
                    'chunk_size' => $chunkSize,
                    'chunk_preview' => substr($data, 0, 200)
                ]);
                
                // For streaming errors, we should propagate them instead of trying to recover
                if (strpos($e->getMessage(), 'API Error:') === 0 || 
                    strpos($e->getMessage(), 'Stream Error:') === 0 || 
                    strpos($e->getMessage(), 'Parse Error:') === 0) {
                    // This is a legitimate API error that should be propagated
                    throw $e;
                }
                
                // Try to recover by sending raw chunk if parsing fails
                try {
                    if (!empty($data) && strlen($data) < 10000) { // Safety check for size
                        $context['callback']($data);
                    }
                } catch (\Throwable $recoveryError) {
                    Logger::error('Chunk recovery also failed', [
                        'provider' => $context['provider'],
                        'recovery_error' => $recoveryError->getMessage()
                    ]);
                }
                // Continue processing despite errors
            }

            return $chunkSize;
        });

        // Execute the request
        $result = curl_exec($ch);

        // Get response info for debugging
        $info = curl_getinfo($ch);
        $totalTime = microtime(true) - $context['start_time'];
        $httpCode = $info['http_code'] ?? 0;

        // Check for errors
        $error = curl_error($ch);
        if ($error || $result === false) {
            $errorDetails = [
                'provider' => $this->getProviderName(),
                'error' => $error ?: 'Unknown cURL error',
                'http_code' => $httpCode,
                'total_time' => $totalTime,
                'chunks_received' => $context['chunks_received'],
                'bytes_received' => $context['bytes_received']
            ];

            Logger::error("Streaming error: " . ($error ?: 'Unknown cURL error'), $errorDetails);
            
            curl_close($ch);
            throw new \RuntimeException("Streaming error: " . ($error ?: 'Unknown cURL error'));
        }

        // Check for HTTP error status codes
        if ($httpCode >= 400) {
            $errorMessage = "HTTP Error $httpCode";
            
            // Provide more specific error messages for common HTTP errors
            switch ($httpCode) {
                case 400:
                    $errorMessage = "Bad Request (400): Invalid request parameters";
                    break;
                case 401:
                    $errorMessage = "Unauthorized (401): Invalid API key or authentication failed";
                    break;
                case 403:
                    $errorMessage = "Forbidden (403): Access denied or insufficient permissions";
                    break;
                case 404:
                    $errorMessage = "Not Found (404): API endpoint not found";
                    break;
                case 429:
                    $errorMessage = "Rate Limited (429): Too many requests, please slow down";
                    break;
                case 500:
                    $errorMessage = "Internal Server Error (500): API server error";
                    break;
                case 502:
                    $errorMessage = "Bad Gateway (502): API server is down or unreachable";
                    break;
                case 503:
                    $errorMessage = "Service Unavailable (503): API server is temporarily unavailable";
                    break;
                default:
                    $errorMessage = "HTTP Error $httpCode: Request failed";
            }

            $errorDetails = [
                'provider' => $this->getProviderName(),
                'http_code' => $httpCode,
                'total_time' => $totalTime,
                'chunks_received' => $context['chunks_received'],
                'bytes_received' => $context['bytes_received']
            ];

            Logger::error("Streaming HTTP error: $errorMessage", $errorDetails);
            
            curl_close($ch);
            throw new \RuntimeException($errorMessage);
        }

        // Log completion statistics
        if ($debug || $context['chunks_received'] === 0) {
            $completionInfo = [
                'message' => 'Streaming request completed',
                'provider' => $this->getProviderName(),
                'http_code' => $info['http_code'],
                'total_time' => $totalTime,
                'chunks_received' => $context['chunks_received'],
                'chunks_processed' => $context['chunks_processed'],
                'bytes_received' => $context['bytes_received'],
                'avg_chunk_size' => $context['chunks_received'] > 0 ? $context['bytes_received'] / $context['chunks_received'] : 0
            ];
            Logger::info('Streaming request completed', $completionInfo);
        }

        // Close curl
        curl_close($ch);

        // Clean up context to prevent memory leaks
        unset($context);
    }

    /**
     * Process a chunk of streaming data
     *
     * @param string $chunk The data chunk
     * @param callable $callback Callback function to handle the processed chunk
     * @return void
     */
    protected function processStreamChunk(string $chunk, callable $callback): void
    {
        // Default implementation - override in provider-specific classes
        if (!empty($chunk)) {
            // Try to parse as JSON for debugging
            $jsonData = json_decode($chunk, true);
            if (json_last_error() === JSON_ERROR_NONE) {
                // This is valid JSON, log it for debugging
                Logger::debug('Received JSON chunk', [
                    'provider' => $this->getProviderName(),
                    'chunk_length' => strlen($chunk),
                    'json_data' => $jsonData
                ]);
            } else {
                // Not JSON, check if it's SSE format
                if (strpos($chunk, 'data: ') === 0) {
                    Logger::debug('Received SSE chunk', [
                        'provider' => $this->getProviderName(),
                        'chunk_length' => strlen($chunk),
                        'chunk_start' => substr($chunk, 0, 50)
                    ]);
                } else {
                    Logger::debug('Received raw chunk', [
                        'provider' => $this->getProviderName(),
                        'chunk_length' => strlen($chunk),
                        'chunk_start' => substr($chunk, 0, 50)
                    ]);
                }
            }

            // Pass the chunk to the callback
            $callback($chunk);
        }
    }
}
