/**
 * AI Editor - Main CSS File
 * A streamlined, step-by-step process for creating AI-generated articles
 *
 * This file imports all the modular CSS components for better maintainability.
 * Each import represents a specific functionality area of the AI Editor.
 */

/* Import all modular CSS files */
@import url('./admin-ai-editor-css/variables.css');
@import url('./admin-ai-editor-css/layout.css');
@import url('./admin-ai-editor-css/forms.css');
@import url('./admin-ai-editor-css/content-process.css');
@import url('./admin-ai-editor-css/image-layouts.css');
@import url('./admin-ai-editor-css/responsive.css');
@import url('./admin-ai-editor-css/animations.css');
@import url('./admin-ai-editor-css/chat-notifications.css');
@import url('./admin-ai-editor-css/media-library.css');
@import url('./admin-ai-editor-css/seo.css');

/* Tooltip styles for general use */
.ai-tooltip {
  display: none;
  pointer-events: none;
  background: var(--admin-surface);
  color: var(--admin-text-primary);
  border: 1px solid var(--admin-border);
  border-radius: var(--border-radius-md);
  padding: .4rem .6rem;
  font-size: .85rem;
  box-shadow: var(--ai-shadow-sm);
  max-width: 280px;
}

.ai-tooltip.ai-tooltip-visible {
  display: block;
}


/**
 * File Structure Overview:
 * 
 * 1. variables.css - CSS custom properties and theme configuration
 * 2. layout.css - Main container, header, and layout structure styles
 * 3. forms.css - Form inputs, buttons, and general UI component styles
 * 4. content-process.css - Ideas, outlines, and content generation workflow
 * 5. image-layouts.css - Image placeholders, layouts, and controls
 * 6. responsive.css - Mobile, tablet, and desktop responsive breakpoints
 * 7. animations.css - Keyframe animations, utilities, and helper styles
 * 8. chat-notifications.css - Floating chat panel and notification system
 * 9. media-library.css - Media library dialog and components
 */