/* SEO Step Styles - Compact Modern Design */

/* SEO Card Structure */
.ai-seo-card {
    background: var(--admin-surface);
    border: 1px solid var(--admin-border);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
}

.ai-seo-card .ai-card-header {
    padding: 1rem 1.5rem;
    background: linear-gradient(135deg, var(--admin-bg), var(--admin-surface));
    border-bottom: 1px solid var(--admin-border);
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 1rem;
}

.ai-seo-card .ai-section-heading {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 700;
    color: var(--admin-text-primary);
}

/* SEO Tab Navigation - Compact & Polished */
.ai-tabs {
    display: flex;
    background: var(--admin-surface);
    border-radius: var(--border-radius-lg);
    padding: 0.25rem;
    border: 1px solid var(--admin-border);
    gap: 0.25rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.ai-tab {
    flex: 1;
    padding: 0.75rem 1rem;
    background: transparent;
    border: none;
    cursor: pointer;
    border-radius: var(--border-radius-md);
    transition: all 0.2s ease;
    color: var(--admin-text-secondary);
    font-weight: 600;
    font-size: 0.9rem;
    text-align: center;
    white-space: nowrap;
    position: relative;
}

.ai-tab::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--admin-primary);
    border-radius: 1px;
    transform: scaleX(0);
    transition: transform 0.2s ease;
}

.ai-tab.active {
    color: var(--admin-primary);
    background: var(--admin-bg);
    font-weight: 700;
}

.ai-tab.active::after {
    transform: scaleX(1);
}

.ai-tab:hover:not(.active) {
    color: var(--admin-text-primary);
    background: var(--admin-bg);
}

.ai-tab-content {
    display: none;
}

.ai-tab-content.active {
    display: block;
}

/* SEO Toolbar */
.ai-seo-toolbar {
    padding: 1rem 1.5rem;
    background: var(--admin-bg);
    border-bottom: 1px solid var(--admin-border);
    display: flex;
    justify-content: flex-end;
}

/* SEO Progress */
.ai-seo-progress {
    display: none;
    padding: 1.5rem;
    background: var(--admin-surface);
}

.ai-seo-progress.visible {
    display: block;
}

.ai-seo-progress-rows {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.ai-seo-progress-row {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: var(--admin-bg);
    border: 1px solid var(--admin-border);
    border-radius: var(--border-radius-md);
    transition: all 0.3s ease;
}

.ai-seo-progress-badge {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 0.9rem;
    color: white;
    background: linear-gradient(135deg, var(--admin-primary), var(--admin-accent));
    box-shadow: 0 2px 8px rgba(var(--admin-primary-rgb), 0.3);
    flex-shrink: 0;
}

/* SEO Results */
.ai-seo-results {
    display: none;
    padding: 1.5rem;
}

.ai-seo-results.visible {
    display: block;
}

/* Form Groups */
.ai-form-group {
    margin-bottom: 1.5rem;
}

.ai-form-label {
    display: block;
    font-weight: 600;
    color: var(--admin-text-primary);
    margin-bottom: 0.5rem;
    font-size: 0.95rem;
}

.ai-form-input,
.ai-form-textarea {
    width: 100%;
    padding: 0.875rem 1rem;
    border: 1px solid var(--admin-border);
    border-radius: var(--border-radius-md);
    font-family: inherit;
    font-size: 0.95rem;
    transition: all 0.2s ease;
    background: var(--admin-bg);
    color: var(--admin-text-primary);
}

.ai-form-input:focus,
.ai-form-textarea:focus {
    outline: none;
    border-color: var(--admin-primary);
    box-shadow: 0 0 0 3px rgba(var(--admin-primary-rgb), 0.1);
}

.ai-form-textarea {
    resize: vertical;
    line-height: 1.5;
}

/* Chips */
.ai-chip-container {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    min-height: 2rem;
}

.ai-chip {
    display: inline-flex;
    align-items: center;
    padding: 0.375rem 0.75rem;
    background: linear-gradient(135deg, rgba(var(--admin-primary-rgb), 0.1), rgba(var(--admin-primary-rgb), 0.05));
    border: 1px solid rgba(var(--admin-primary-rgb), 0.2);
    border-radius: var(--border-radius-md);
    color: var(--admin-primary);
    font-size: 0.85rem;
    font-weight: 500;
}

/* Form Actions */
.ai-form-actions {
    display: flex;
    gap: 0.75rem;
    margin-top: 1rem;
    flex-wrap: wrap;
}

/* Image Alt Texts Section - Compact Design */
.ai-card-section {
    padding: 1.5rem;
}

.ai-section-subtitle {
    margin: 0 0 1rem 0;
    font-size: 1rem;
    font-weight: 600;
    color: var(--admin-text-primary);
}

.ai-image-alt-list {
    list-style: none;
    padding: 0;
    margin: 0;
    display: grid;
    gap: 1rem;
}

.ai-image-alt-item {
    display: grid;
    grid-template-columns: auto 1fr auto;
    gap: 1rem;
    padding: 1rem;
    background: var(--admin-bg);
    border: 1px solid var(--admin-border);
    border-radius: var(--border-radius-lg);
    transition: all 0.2s ease;
    align-items: start;
}

.ai-image-alt-item:hover {
    border-color: var(--admin-primary);
    box-shadow: 0 4px 12px rgba(var(--admin-primary-rgb), 0.1);
}

.ai-image-alt-left {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
}

.ai-image-alt-thumb-wrapper {
    width: 64px;
    height: 64px;
    border: 1px solid var(--admin-border);
    border-radius: var(--border-radius-md);
    display: grid;
    place-items: center;
    background: var(--admin-surface);
    overflow: hidden;
    transition: all 0.2s ease;
}

.ai-image-alt-thumb-wrapper:hover {
    border-color: var(--admin-primary);
}

.ai-image-alt-thumb {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: var(--border-radius-sm);
    cursor: zoom-in;
}

.ai-thumb-empty {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    color: var(--admin-text-secondary);
    text-align: center;
    padding: 0.5rem;
    line-height: 1.2;
    font-weight: 500;
    background: var(--admin-bg);
    border: 1px dashed var(--admin-border);
    border-radius: var(--border-radius-sm);
    width: 100%;
    height: 100%;
}

.ai-image-alt-center {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    min-width: 0;
}

.ai-image-alt-input-wrapper {
    position: relative;
}

.ai-image-alt-input {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--admin-border);
    border-radius: var(--border-radius-md);
    font-family: inherit;
    font-size: 0.9rem;
    transition: all 0.2s ease;
    background: var(--admin-surface);
    color: var(--admin-text-primary);
    line-height: 1.4;
    resize: none;
    min-height: 60px;
}

.ai-image-alt-input:focus {
    outline: none;
    border-color: var(--admin-primary);
    box-shadow: 0 0 0 3px rgba(var(--admin-primary-rgb), 0.1);
    background: var(--admin-bg);
}

.ai-image-alt-right {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    align-items: flex-end;
    min-width: 80px;
}

.ai-image-alt-actions {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    width: 100%;
}

.ai-image-alt-actions .ai-btn {
    font-size: 0.8rem;
    padding: 0.5rem 0.75rem;
    white-space: nowrap;
}

/* Empty State */
.ai-empty-state {
    text-align: center;
    padding: 2rem 1rem;
    color: var(--admin-text-secondary);
    background: var(--admin-surface);
    border-radius: var(--border-radius-md);
    border: 1px dashed var(--admin-border);
}

.ai-empty-icon {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    opacity: 0.6;
}

.ai-empty-title {
    font-weight: 600;
    margin-bottom: 0.25rem;
    color: var(--admin-text-primary);
    font-size: 0.95rem;
}

.ai-empty-desc {
    font-size: 0.85rem;
    line-height: 1.4;
    color: var(--admin-text-secondary);
}

/* Mobile Responsive Design */
@media (max-width: 768px) {
    .ai-seo-card .ai-card-header {
        flex-direction: column;
        align-items: stretch;
        gap: 1rem;
    }
    
    .ai-tabs {
        order: -1;
    }
    
    .ai-tab {
        padding: 0.875rem 1rem;
        font-size: 0.9rem;
    }
    
    .ai-seo-toolbar,
    .ai-seo-progress,
    .ai-seo-results,
    .ai-card-section {
        padding: 1rem;
    }
    
    .ai-image-alt-item {
        grid-template-columns: 1fr;
        gap: 1rem;
        padding: 1rem;
    }
    
    .ai-image-alt-left {
        flex-direction: row;
        justify-content: center;
        align-items: center;
    }
    
    .ai-image-alt-thumb-wrapper {
        width: 56px;
        height: 56px;
    }
    
    .ai-image-alt-right {
        align-items: center;
        min-width: auto;
    }
    
    .ai-image-alt-actions {
        flex-direction: row;
        gap: 0.5rem;
        justify-content: center;
    }
    
    .ai-image-alt-actions .ai-btn {
        font-size: 0.75rem;
        padding: 0.5rem 0.75rem;
    }
}

@media (max-width: 480px) {
    .ai-seo-card .ai-card-header {
        padding: 1rem;
    }
    
    .ai-seo-toolbar,
    .ai-seo-progress,
    .ai-seo-results,
    .ai-card-section {
        padding: 0.75rem;
    }
    
    .ai-image-alt-item {
        padding: 0.75rem;
        gap: 0.75rem;
    }
    
    .ai-image-alt-actions {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .ai-form-input,
    .ai-form-textarea {
        padding: 0.75rem;
        font-size: 0.9rem;
    }
    
    .ai-image-alt-input {
        padding: 0.75rem;
        font-size: 0.85rem;
        min-height: 50px;
    }
    
    .ai-image-alt-thumb-wrapper {
        width: 48px;
        height: 48px;
    }
}

/* Animation for status updates */
@keyframes statusPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.ai-image-alt-status.updating {
    animation: statusPulse 0.6s ease-in-out;
}

/* Loading states */
.ai-loading-shimmer {
    background: linear-gradient(90deg, var(--admin-surface) 25%, var(--admin-bg) 50%, var(--admin-surface) 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}

/* Enhanced button styles for actions */
.ai-image-alt-actions .ai-btn {
    border-radius: var(--border-radius-lg);
    font-weight: 600;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.ai-image-alt-actions .ai-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(var(--admin-primary-rgb), 0.2);
}

/* Accessibility improvements */
.ai-seo-tab:focus-visible,
.ai-image-alt-input:focus-visible,
.ai-seo-field input:focus-visible,
.ai-seo-field textarea:focus-visible {
    outline: 2px solid var(--admin-primary);
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .ai-image-alt-item {
        border-width: 3px;
    }
    
    .ai-seo-tab.active {
        border: 3px solid var(--admin-primary);
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .ai-seo-tab,
    .ai-image-alt-item,
    .ai-image-alt-thumb,
    .ai-seo-field input,
    .ai-seo-field textarea,
    .ai-image-alt-input {
        transition: none;
    }
    
    .ai-seo-content {
        animation: none;
    }
}