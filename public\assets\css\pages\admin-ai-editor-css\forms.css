/**
 * AI Editor - Form Elements & UI Components
 * Contains form inputs, buttons, and general UI component styles
 */

/* ========================================
   FORM ELEMENTS & UI COMPONENTS
   ======================================== */

/* Form Elements */
.ai-panel-form {
    max-width: 600px;
    margin: 0 auto 1.5rem;
    padding: 0 1rem;
}

.ai-form-group {
    margin-bottom: 1.25rem;
}

.ai-form-label {
    display: block;
    font-size: 0.9375rem;
    font-weight: 500;
    color: var(--admin-text-primary);
    margin-bottom: 0.75rem;
    line-height: 1.4;
}

.ai-form-input,
.ai-form-select,
.ai-form-textarea {
    width: 100%;
    padding: 1rem 0.75rem;
    border: 1px solid var(--admin-border);
    border-radius: 0.5rem;
    background-color: var(--admin-bg);
    color: var(--admin-text-primary);
    font-size: 1rem;
    line-height: 1.5;
    min-height: 44px;
    transition: border-color 0.2s, box-shadow 0.2s, background-color 0.2s;
    -webkit-appearance: none;
    appearance: none;
}

.ai-form-input:focus,
.ai-form-select:focus,
.ai-form-textarea:focus {
    outline: none;
    border-color: var(--ai-primary);
    box-shadow: 0 0 0 2px rgba(var(--ai-primary-rgb), 0.3);
}

.ai-form-textarea {
    min-height: 100px;
    resize: vertical;
}

.ai-form-error {
    color: var(--ai-danger);
    font-size: 0.75rem;
    margin-top: 0.25rem;
}

/* Buttons */
.ai-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.75rem 1.25rem;
    border-radius: 0.5rem;
    font-size: 0.9375rem;
    font-weight: 500;
    cursor: pointer;
    min-height: 44px;
    min-width: 44px;
    gap: 0.5rem;
    transition: all 0.2s ease;
    border: none;
    text-decoration: none;
    user-select: none;
    -webkit-tap-highlight-color: transparent;
}

.ai-btn-primary {
    background-color: var(--ai-primary);
    color: white;
    border: none;
}

.ai-btn-primary:hover {
    background-color: var(--ai-primary-dark);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(var(--ai-primary-rgb), 0.3);
}

.ai-btn-primary:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(var(--ai-primary-rgb), 0.2);
}

.ai-btn-secondary {
    background-color: var(--admin-bg);
    color: var(--admin-text-primary);
    border: 1px solid var(--admin-border);
}

.ai-btn-secondary:hover {
    background-color: var(--admin-surface);
    border-color: var(--admin-text-secondary);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.ai-btn-secondary:active {
    transform: translateY(0);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.ai-btn-full {
    width: 100%;
}

.ai-btn:disabled {
  opacity: .55;
  cursor: not-allowed;
  filter: grayscale(0.3);
}


.ai-btn-sm {
    padding: 0.5rem 0.875rem;
    font-size: 0.8125rem;
    align-self: flex-end;
    margin-left: auto;
    min-height: 36px;
}

/* Mobile responsive button text */
@media (max-width: 480px) {
    .ai-btn .ai-btn-text {
        display: none;
    }
    
    .ai-btn {
        padding: 0.75rem;
        min-width: 44px;
    }
    
    .ai-header-mobile-dropdown .ai-btn .ai-btn-text,
    .ai-header-mobile-dropdown .ai-btn {
        display: inline-flex;
        padding: 0.75rem 1rem;
    }
    
    .ai-header-mobile-dropdown .ai-btn .ai-btn-text {
        display: inline;
    }
}

@media (min-width: 481px) and (max-width: 767px) {
    .ai-btn .ai-btn-text {
        display: none;
    }
    
    .ai-header-mobile-dropdown .ai-btn .ai-btn-text {
        display: inline;
    }
}

@media (min-width: 768px) {
    .ai-btn .ai-btn-text {
        display: inline;
    }
}

/* Range Controls */
.ai-range-control {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.ai-range-input {
    flex: 1;
    height: 6px;
    -webkit-appearance: none;
    appearance: none;
    background: var(--admin-border);
    border-radius: 3px;
    outline: none;
}

.ai-range-input::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: var(--ai-primary);
    cursor: pointer;
}

.ai-range-input::-moz-range-thumb {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: var(--ai-primary);
    cursor: pointer;
    border: none;
}

.ai-range-value {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--admin-text-primary);
    min-width: 24px;
    text-align: center;
}

/* Checkbox Groups */
.ai-checkbox-group {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.ai-checkbox-label {
    display: flex;
    align-items: center;
    font-size: 0.875rem;
    color: var(--admin-text-primary);
    cursor: pointer;
    margin-right: 1rem;
}

.ai-checkbox-label input {
    margin-right: 0.5rem;
}

/* Toggle Buttons */
.ai-toggle-button {
    background: none;
    border: none;
    color: var(--admin-text-secondary);
    cursor: pointer;
    padding: 0.25rem;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: transform 0.2s;
}

.ai-toggle-button.collapsed svg {
    transform: rotate(180deg);
}

/* Option Groups */
.ai-option-group {
    margin-bottom: 0.5rem;
}

.ai-options-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

/* Status Message */
.ai-status-message {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 1rem;
    margin: 1.5rem 0;
    text-align: center;
    font-size: 0.875rem;
    color: var(--admin-text-secondary);
    background-color: rgba(var(--ai-primary-rgb), 0.05);
    border-radius: 0.5rem;
    border-left: 3px solid var(--ai-primary);
    position: relative;
    animation: pulse 2s infinite ease-in-out;
}

.ai-status-message::before {
    content: '';
    display: inline-block;
    width: 12px;
    height: 12px;
    border: 2px solid rgba(var(--ai-primary-rgb), 0.3);
    border-radius: 50%;
    border-top-color: var(--ai-primary);
    animation: spin 1s linear infinite;
    margin-right: 0.75rem;
}

/* Loading Indicator */
.ai-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    color: var(--admin-text-secondary);
    font-style: italic;
}

.ai-loading::before {
    content: '';
    width: 1rem;
    height: 1rem;
    border-radius: 50%;
    border: 2px solid var(--admin-border);
    border-top-color: var(--ai-primary);
    margin-right: 0.5rem;
    animation: spin 1s linear infinite;
}

/* Section Headings */
.ai-section-heading {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--admin-text-primary);
    margin: 0;
}

/* Code Block Components */
.ai-code-block-container {
    position: relative;
}

.ai-code-block {
    background-color: var(--admin-surface);
    border: 1px solid var(--admin-border);
    border-radius: 0.375rem;
    padding: 1rem;
    margin: 0.5rem 0;
    font-family: monospace;
    font-size: 0.875rem;
    white-space: pre-wrap;
    word-break: break-all;
    color: var(--admin-text-primary);
    max-height: 200px;
    overflow-y: auto;
}

.ai-copy-code-btn {
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
    background-color: var(--admin-surface);
    border: 1px solid var(--admin-border);
    border-radius: 0.25rem;
    padding: 0.25rem;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: background-color 0.2s;
}

.ai-copy-code-btn:hover {
    background-color: var(--admin-bg);
}

/* ========================================
   TEXT SELECTION FORMATTING SYSTEM
   ======================================== */

/* Floating Selection Toolbar */
.ai-selection-toolbar {
    position: absolute;
    display: none;
    background: var(--admin-surface);
    border: 1px solid var(--admin-border);
    border-radius: 8px;
    padding: 4px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    animation: ai-toolbar-fade-in 0.2s ease-out;
}

.ai-selection-toolbar.visible {
    display: flex;
    align-items: center;
    gap: 2px;
}

.ai-format-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border: none;
    background: transparent;
    border-radius: 4px;
    cursor: pointer;
    color: var(--admin-text-primary);
    transition: all 0.2s ease;
    position: relative;
}

.ai-format-btn:hover {
    background: var(--ai-primary);
    color: white;
    transform: translateY(-1px);
}

.ai-format-btn:active {
    transform: translateY(0);
}

.ai-format-btn.active {
    background: var(--ai-primary);
    color: white;
}

.ai-toolbar-separator {
    width: 1px;
    height: 20px;
    background: var(--admin-border);
    margin: 0 4px;
}

/* Heading Dropdown */
.ai-heading-dropdown-container {
    position: relative;
    display: inline-block;
}

.ai-heading-btn {
    display: flex;
    align-items: center;
    gap: 2px;
    padding: 0 6px;
    width: auto;
    min-width: 40px;
}

.ai-dropdown-arrow {
    width: 8px;
    height: 8px;
    transition: transform 0.2s ease;
}

.ai-heading-dropdown-container:hover .ai-dropdown-arrow {
    transform: rotate(180deg);
}

.ai-heading-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    background: var(--admin-surface);
    border: 1px solid var(--admin-border);
    border-radius: 6px;
    padding: 4px 0;
    min-width: 120px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 1002;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-8px);
    transition: all 0.2s ease;
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
}

.ai-heading-dropdown-container:hover .ai-heading-dropdown {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.ai-heading-option {
    padding: 8px 12px;
    cursor: pointer;
    color: var(--admin-text-primary);
    font-size: 0.875rem;
    transition: background-color 0.2s ease;
    border-radius: 4px;
    margin: 0 4px;
}

.ai-heading-option:hover {
    background: var(--ai-primary);
    color: white;
}

.ai-heading-option[data-heading="h2"] {
    font-weight: 600;
    font-size: 1rem;
}

.ai-heading-option[data-heading="h3"] {
    font-weight: 600;
    font-size: 0.9375rem;
}

.ai-heading-option[data-heading="h4"] {
    font-weight: 600;
    font-size: 0.875rem;
}

.ai-heading-option[data-heading="h1"] {
    font-weight: 700;
    font-size: 1.125rem;
}

.ai-heading-option[data-heading="h5"] {
    font-weight: 600;
    font-size: 0.8125rem;
}

.ai-heading-option[data-heading="h6"] {
    font-weight: 600;
    font-size: 0.75rem;
}

/* Context Menu Submenu */
.ai-heading-submenu-trigger {
    position: relative;
}

.ai-submenu-arrow {
    margin-left: auto;
    transition: transform 0.2s ease;
}

.ai-context-submenu {
    position: absolute;
    left: 100%;
    top: 0;
    background: var(--admin-surface);
    border: 1px solid var(--admin-border);
    border-radius: 6px;
    padding: 4px 0;
    min-width: 140px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 1003;
    opacity: 0;
    visibility: hidden;
    transform: translateX(-8px);
    transition: all 0.2s ease;
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
}

.ai-heading-submenu-trigger:hover .ai-context-submenu {
    opacity: 1;
    visibility: visible;
    transform: translateX(0);
}

.ai-heading-submenu-trigger:hover .ai-submenu-arrow {
    transform: rotate(90deg);
}

.ai-context-submenu .ai-context-menu-item {
    padding: 6px 12px;
    font-size: 0.8125rem;
}

/* ========================================
   CUSTOM LINK DIALOG
   ======================================== */

.ai-link-dialog {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 2000;
    display: none;
    align-items: center;
    justify-content: center;
}

.ai-link-dialog.visible {
    display: flex;
}

.ai-link-dialog-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
    -webkit-backdrop-filter: blur(4px);
}

.ai-link-dialog-content {
    position: relative;
    background: var(--admin-surface);
    border: 1px solid var(--admin-border);
    border-radius: 12px;
    width: 90%;
    max-width: 480px;
    max-height: 90vh;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    animation: ai-dialog-fade-in 0.3s ease-out;
}

.ai-link-dialog-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1.5rem;
    border-bottom: 1px solid var(--admin-border);
}

.ai-link-dialog-header h3 {
    margin: 0;
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--admin-text-primary);
}

.ai-link-dialog-close {
    background: none;
    border: none;
    padding: 0.5rem;
    cursor: pointer;
    color: var(--admin-text-secondary);
    border-radius: 6px;
    transition: all 0.2s ease;
}

.ai-link-dialog-close:hover {
    background: var(--admin-border);
    color: var(--admin-text-primary);
}

.ai-link-dialog-body {
    padding: 1.5rem;
}

.ai-link-form-group {
    margin-bottom: 1.25rem;
}

.ai-link-label {
    display: block;
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--admin-text-primary);
    margin-bottom: 0.5rem;
}

.ai-link-input {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--admin-border);
    border-radius: 6px;
    background: var(--admin-bg);
    color: var(--admin-text-primary);
    font-size: 0.875rem;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.ai-link-input:focus {
    outline: none;
    border-color: var(--ai-primary);
    box-shadow: 0 0 0 2px rgba(var(--ai-primary-rgb), 0.2);
}

.ai-link-options {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    margin-top: 1rem;
}

.ai-link-checkbox-label {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-size: 0.875rem;
    color: var(--admin-text-primary);
    gap: 0.75rem;
}

.ai-link-checkbox {
    display: none;
}

.ai-link-checkmark {
    width: 18px;
    height: 18px;
    border: 2px solid var(--admin-border);
    border-radius: 4px;
    background: var(--admin-bg);
    position: relative;
    transition: all 0.2s ease;
}

.ai-link-checkbox:checked + .ai-link-checkmark {
    background: var(--ai-primary);
    border-color: var(--ai-primary);
}

.ai-link-checkbox:checked + .ai-link-checkmark::after {
    content: '';
    position: absolute;
    left: 5px;
    top: 2px;
    width: 4px;
    height: 8px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

.ai-link-dialog-footer {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 0.75rem;
    padding: 1.5rem;
    border-top: 1px solid var(--admin-border);
    background: var(--admin-bg);
}

.ai-link-btn {
    padding: 0.75rem 1.5rem;
    border-radius: 6px;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    border: none;
    min-width: 80px;
}

.ai-link-btn-secondary {
    background: var(--admin-surface);
    color: var(--admin-text-primary);
    border: 1px solid var(--admin-border);
}

.ai-link-btn-secondary:hover {
    background: var(--admin-border);
}

.ai-link-btn-primary {
    background: var(--ai-primary);
    color: white;
}

.ai-link-btn-primary:hover {
    background: var(--ai-primary-dark);
}

.ai-link-btn-primary:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* Dialog Animation */
@keyframes ai-dialog-fade-in {
    from {
        opacity: 0;
        transform: scale(0.95) translateY(-20px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .ai-link-dialog-content {
        width: 95%;
        margin: 1rem;
    }
    
    .ai-link-dialog-header,
    .ai-link-dialog-body,
    .ai-link-dialog-footer {
        padding: 1rem;
    }
    
    .ai-link-options {
        gap: 1rem;
    }
    
    .ai-link-btn {
        padding: 0.875rem 1.25rem;
        font-size: 0.9375rem;
    }
}

/* Right-click Context Menu */
.ai-context-menu {
    position: absolute;
    display: none;
    background: var(--admin-surface);
    border: 1px solid var(--admin-border);
    border-radius: 8px;
    padding: 4px 0;
    min-width: 180px;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    z-index: 1001;
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    animation: ai-context-fade-in 0.15s ease-out;
}

.ai-context-menu.visible {
    display: block;
}

.ai-context-menu-item {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    cursor: pointer;
    color: var(--admin-text-primary);
    transition: background-color 0.2s ease;
    gap: 8px;
    font-size: 0.875rem;
}

.ai-context-menu-item:hover {
    background: var(--ai-primary);
    color: white;
}

.ai-context-menu-item svg {
    flex-shrink: 0;
}

.ai-context-menu-item span:first-of-type {
    flex: 1;
}

.ai-context-shortcut {
    font-size: 0.75rem;
    opacity: 0.7;
    font-family: monospace;
}

.ai-context-menu-separator {
    height: 1px;
    background: var(--admin-border);
    margin: 4px 0;
}

/* Content Area Enhancements */
.ai-content-area {
    position: relative;
    min-height: 300px;
    padding: 1.5rem;
    border: 1px solid var(--admin-border);
    border-radius: 8px;
    background: var(--admin-bg);
    color: var(--admin-text-primary);
    font-size: 1rem;
    line-height: 1.6;
    outline: none;
    transition: border-color 0.2s ease;
}

.ai-content-area:focus {
    border-color: var(--ai-primary);
    box-shadow: 0 0 0 2px rgba(var(--ai-primary-rgb), 0.2);
}

.ai-content-area[data-placeholder]:empty::before {
    content: attr(data-placeholder);
    color: var(--admin-text-secondary);
    font-style: italic;
    pointer-events: none;
}

/* Selection Highlighting */
.ai-content-area ::selection {
    background: rgba(var(--ai-primary-rgb), 0.2);
    color: var(--admin-text-primary);
}

.ai-content-area ::-moz-selection {
    background: rgba(var(--ai-primary-rgb), 0.2);
    color: var(--admin-text-primary);
}

/* Animations */
@keyframes ai-toolbar-fade-in {
    from {
        opacity: 0;
        transform: translateY(-8px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

@keyframes ai-context-fade-in {
    from {
        opacity: 0;
        transform: scale(0.95);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* Mobile Responsive Design */
@media (max-width: 768px) {
    .ai-selection-toolbar {
        padding: 6px;
        border-radius: 12px;
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
    }
    
    .ai-format-btn {
        width: 40px;
        height: 40px;
        border-radius: 6px;
    }
    
    .ai-toolbar-separator {
        height: 24px;
        margin: 0 6px;
    }
    
    .ai-context-menu {
        min-width: 200px;
        border-radius: 12px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    }
    
    .ai-context-menu-item {
        padding: 12px 16px;
        font-size: 0.9375rem;
    }
    
    .ai-content-area {
        padding: 1rem;
        font-size: 1.0625rem;
        line-height: 1.7;
    }
}

/* Touch-friendly interactions for mobile */
@media (max-width: 768px) and (pointer: coarse) {
    .ai-format-btn {
        width: 44px;
        height: 44px;
    }
    
    .ai-context-menu-item {
        padding: 14px 16px;
        min-height: 44px;
    }
}

/* Compact layout for very small screens */
@media (max-width: 480px) {
    .ai-selection-toolbar {
        max-width: calc(100vw - 32px);
        flex-wrap: wrap;
        gap: 4px;
    }
    
    .ai-format-btn {
        width: 36px;
        height: 36px;
    }
    
    .ai-toolbar-separator {
        display: none;
    }
    
    .ai-context-menu {
        max-width: calc(100vw - 32px);
        min-width: 160px;
    }
    
    .ai-context-shortcut {
        display: none;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .ai-selection-toolbar,
    .ai-context-menu {
        border-width: 2px;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    }
    
    .ai-format-btn:hover,
    .ai-context-menu-item:hover {
        outline: 2px solid var(--ai-primary);
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .ai-selection-toolbar,
    .ai-context-menu {
        animation: none;
    }
    
    .ai-format-btn,
    .ai-context-menu-item {
        transition: none;
    }
    
    .ai-format-btn:hover {
        transform: none;
    }
}

/* ========================================
   DESCRIPTION DIALOG STYLING
   ======================================== */

.ai-description-dialog-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(4px);
    -webkit-backdrop-filter: blur(4px);
    z-index: 2000;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 1rem;
}

.ai-description-dialog {
    background: var(--admin-surface);
    border: 1px solid var(--admin-border);
    border-radius: 12px;
    width: 100%;
    max-width: 500px;
    max-height: 80vh;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.ai-description-dialog-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1.5rem;
    border-bottom: 1px solid var(--admin-border);
    background: var(--admin-bg);
}

.ai-description-dialog-header h3 {
    margin: 0;
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--admin-text-primary);
}

.ai-description-dialog-close {
    background: none;
    border: none;
    padding: 0.5rem;
    cursor: pointer;
    color: var(--admin-text-secondary);
    border-radius: 6px;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.ai-description-dialog-close:hover {
    background: var(--admin-border);
    color: var(--admin-text-primary);
}

.ai-description-dialog-content {
    padding: 1.5rem;
    max-height: 60vh;
    overflow-y: auto;
}

.ai-description-text-full {
    color: var(--admin-text-primary);
    line-height: 1.6;
    font-size: 0.9375rem;
}

.ai-description-dialog-footer {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 0.75rem;
    padding: 1.5rem;
    border-top: 1px solid var(--admin-border);
    background: var(--admin-bg);
}

.ai-description-cancel-btn {
    min-width: 80px;
}

.ai-dialog-upload-btn {
    min-width: 120px;
}

/* Mobile responsive */
@media (max-width: 768px) {
    .ai-description-dialog {
        margin: 1rem;
        max-width: none;
    }
    
    .ai-description-dialog-header,
    .ai-description-dialog-content,
    .ai-description-dialog-footer {
        padding: 1rem;
    }
    
    .ai-description-dialog-footer {
        flex-direction: column-reverse;
        gap: 0.5rem;
    }
    
    .ai-description-cancel-btn,
    .ai-dialog-upload-btn {
        width: 100%;
        min-width: auto;
    }
}

/* ========================================
   TABLE FORMATTING AND EDITING
   ======================================== */

/* Table Styling in Content Area */
.ai-content-area table {
    width: 100%;
    border-collapse: collapse;
    margin: 1.5rem 0;
    background: var(--admin-surface);
    border: 1px solid var(--admin-border);
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.ai-content-area table thead {
    background: var(--admin-bg);
    border-bottom: 2px solid var(--admin-border);
}

.ai-content-area table th {
    padding: 0.875rem 1rem;
    text-align: left;
    font-weight: 600;
    color: var(--admin-text-primary);
    font-size: 0.875rem;
    border-right: 1px solid var(--admin-border);
}

.ai-content-area table th:last-child {
    border-right: none;
}

.ai-content-area table td {
    padding: 0.75rem 1rem;
    border-right: 1px solid var(--admin-border);
    border-bottom: 1px solid var(--admin-border);
    color: var(--admin-text-primary);
    font-size: 0.875rem;
    line-height: 1.5;
    vertical-align: top;
}

.ai-content-area table td:last-child {
    border-right: none;
}

.ai-content-area table tbody tr:last-child td {
    border-bottom: none;
}

.ai-content-area table tbody tr:hover {
    background: rgba(var(--ai-primary-rgb), 0.05);
}

/* Editable Table Cells */
.ai-content-area table td[contenteditable="true"] {
    position: relative;
    cursor: text;
    transition: all 0.2s ease;
}

.ai-content-area table td[contenteditable="true"]:hover {
    background: rgba(var(--ai-primary-rgb), 0.1);
    box-shadow: inset 0 0 0 1px rgba(var(--ai-primary-rgb), 0.3);
}

.ai-content-area table td[contenteditable="true"]:focus {
    outline: none;
    background: var(--admin-bg);
    box-shadow: inset 0 0 0 2px var(--ai-primary);
    z-index: 10;
}

/* Table Toolbar - Enhanced positioning and styling */
.ai-table-toolbar {
    position: fixed;
    display: none;
    background: var(--admin-bg);
    border: 1px solid var(--admin-border);
    border-radius: var(--border-radius-lg);
    padding: 0.75rem;
    box-shadow: var(--ai-shadow-xl);
    z-index: 1000;
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    background: rgba(var(--admin-bg-rgb), 0.95);
    min-width: fit-content;
    max-width: 90vw;
    overflow-x: auto;
    animation: tableToolbarSlideIn 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.ai-table-toolbar.visible {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    flex-wrap: nowrap;
}

@keyframes tableToolbarSlideIn {
    from {
        opacity: 0;
        transform: translateY(-10px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.ai-table-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border: none;
    background: var(--admin-surface);
    border: 1px solid var(--admin-border);
    border-radius: var(--border-radius-md);
    cursor: pointer;
    color: var(--admin-text-primary);
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
    font-size: 0.75rem;
    font-weight: 500;
    position: relative;
    flex-shrink: 0;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.ai-table-btn:hover {
    background: var(--ai-primary-light);
    border-color: var(--ai-primary);
    color: var(--ai-primary);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(var(--ai-primary-rgb), 0.2);
}

.ai-table-btn:active {
    transform: translateY(-1px);
    box-shadow: 0 2px 6px rgba(var(--ai-primary-rgb), 0.3);
}

.ai-table-btn svg {
    width: 16px;
    height: 16px;
    stroke-width: 2;
}

/* Custom tooltip for table buttons */
.ai-table-btn::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: -45px;
    left: 50%;
    transform: translateX(-50%);
    background: var(--admin-text-primary);
    color: var(--admin-bg);
    padding: 0.5rem 0.75rem;
    border-radius: var(--border-radius-sm);
    font-size: 0.75rem;
    font-weight: 500;
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
    z-index: 1001;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.ai-table-btn::before {
    content: '';
    position: absolute;
    bottom: -35px;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 6px solid transparent;
    border-right: 6px solid transparent;
    border-bottom: 6px solid var(--admin-text-primary);
    opacity: 0;
    pointer-events: none;
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
    z-index: 1001;
}

.ai-table-btn:hover::after,
.ai-table-btn:hover::before {
    opacity: 1;
}

.ai-table-separator {
    width: 1px;
    height: 24px;
    background: var(--admin-border);
    margin: 0 0.25rem;
    flex-shrink: 0;
}

/* Table Context Menu */
.ai-table-context-menu {
    position: absolute;
    display: none;
    background: var(--admin-surface);
    border: 1px solid var(--admin-border);
    border-radius: 6px;
    padding: 4px 0;
    min-width: 160px;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    z-index: 1001;
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
}

.ai-table-context-menu.visible {
    display: block;
}

.ai-table-context-item {
    display: flex;
    align-items: center;
    padding: 6px 12px;
    cursor: pointer;
    color: var(--admin-text-primary);
    transition: background-color 0.2s ease;
    font-size: 0.8125rem;
    gap: 8px;
}

.ai-table-context-item:hover {
    background: var(--ai-primary);
    color: white;
}

.ai-table-context-item.danger:hover {
    background: var(--ai-danger);
    color: white;
}

/* Table Selection Highlighting */
.ai-table-cell-selected {
    background: rgba(var(--ai-primary-rgb), 0.2) !important;
    box-shadow: inset 0 0 0 2px var(--ai-primary) !important;
}

.ai-table-row-selected {
    background: rgba(var(--ai-primary-rgb), 0.1) !important;
}

.ai-table-column-selected {
    background: rgba(var(--ai-primary-rgb), 0.1) !important;
}

/* Responsive Table */
@media (max-width: 768px) {
    .ai-content-area table {
        font-size: 0.8125rem;
        margin: 1rem 0;
    }
    
    .ai-content-area table th,
    .ai-content-area table td {
        padding: 0.5rem 0.75rem;
    }
    
    .ai-table-toolbar {
        padding: 6px;
        border-radius: 8px;
    }
    
    .ai-table-btn {
        width: 32px;
        height: 32px;
        font-size: 0.8125rem;
    }
    
    .ai-table-context-menu {
        min-width: 180px;
    }
    
    .ai-table-context-item {
        padding: 8px 12px;
        font-size: 0.875rem;
    }
}

/* Very small screens - horizontal scroll for tables */
@media (max-width: 480px) {
    .ai-content-area table {
        display: block;
        overflow-x: auto;
        white-space: nowrap;
        border-radius: 6px;
    }
    
    .ai-content-area table thead,
    .ai-content-area table tbody,
    .ai-content-area table tr {
        display: table;
        width: 100%;
        table-layout: fixed;
    }
    
    .ai-content-area table th,
    .ai-content-area table td {
        min-width: 120px;
        white-space: normal;
    }
}

/* Table Animation */
@keyframes ai-table-highlight {
    0% {
        background: rgba(var(--ai-primary-rgb), 0.3);
    }
    100% {
        background: transparent;
    }
}

.ai-table-cell-highlight {
    animation: ai-table-highlight 0.6s ease-out;
}

/* Table Creation Dialog */
.ai-table-create-dialog {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
    -webkit-backdrop-filter: blur(4px);
    z-index: 2000;
    display: none;
    align-items: center;
    justify-content: center;
    padding: 1rem;
}

.ai-table-create-dialog.visible {
    display: flex;
}

.ai-table-create-content {
    background: var(--admin-surface);
    border: 1px solid var(--admin-border);
    border-radius: 12px;
    width: 100%;
    max-width: 400px;
    padding: 1.5rem;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.ai-table-create-header {
    margin-bottom: 1.5rem;
}

.ai-table-create-header h3 {
    margin: 0 0 0.5rem 0;
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--admin-text-primary);
}

.ai-table-create-header p {
    margin: 0;
    font-size: 0.875rem;
    color: var(--admin-text-secondary);
}

.ai-table-create-form {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.ai-table-size-inputs {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.ai-table-create-actions {
    display: flex;
    justify-content: flex-end;
    gap: 0.75rem;
    margin-top: 1rem;
}