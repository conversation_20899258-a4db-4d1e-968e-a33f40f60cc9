<?php 
// Partial view included by create.php and edit.php
// Expects: $errors (optional), $old_input (optional), $post (optional for edit), $categories
$errors = $errors ?? [];
$post = $post ?? null; // If editing, $post object exists
$old_input = $old_input ?? ($post ? $post : []); // Use old input or existing post data
$categories = $categories ?? []; // Ensure categories variable exists
?>

<?php if (isset($errors['general'])): ?>
    <div class="error-general"><?= htmlspecialchars($errors['general']) ?></div>
<?php endif; ?>

<div class="form-group">
    <label for="title">Title</label>
    <input type="text" id="title" name="title" value="<?= htmlspecialchars($old_input['title'] ?? '') ?>" required>
    <?php if (isset($errors['title'])): ?>
        <div class="error"><?= htmlspecialchars($errors['title']) ?></div>
    <?php endif; ?>
</div>

<div class="form-group">
    <label for="slug">Slug</label>
    <input type="text" id="slug" name="slug" value="<?= htmlspecialchars($old_input['slug'] ?? '') ?>" required>
    <small>URL-friendly version of the title (e.g., my-first-post). TODO: Auto-generate.</small>
    <?php if (isset($errors['slug'])): ?>
        <div class="error"><?= htmlspecialchars($errors['slug']) ?></div>
    <?php endif; ?>
</div>

<div class="form-group">
    <label for="content">Content</label>
    <textarea id="content" name="content" rows="10" required><?= htmlspecialchars($old_input['content'] ?? '') ?></textarea>
    <?php if (isset($errors['content'])): ?>
        <div class="error"><?= htmlspecialchars($errors['content']) ?></div>
    <?php endif; ?>
</div>

<div class="form-group">
    <label for="category_id">Category</label>
    <select id="category_id" name="category_id">
        <option value="">[Uncategorized]</option>
        <?php // Loop through categories fetched from DB
        foreach ($categories as $category): ?>
            <option value="<?= $category['id'] ?>" <?= (($old_input['category_id'] ?? null) == $category['id']) ? 'selected' : '' ?>>
                <?= htmlspecialchars($category['name']) ?>
            </option>
        <?php endforeach; ?>
        <?php /* Remove temporary options
        <option value="1" <?= (($old_input['category_id'] ?? null) == 1) ? 'selected' : '' ?>>(TEMP: Cat 1)</option>
        <option value="2" <?= (($old_input['category_id'] ?? null) == 2) ? 'selected' : '' ?>>(TEMP: Cat 2)</option>
        */ ?>
    </select>
    <?php if (isset($errors['category_id'])): ?>
        <div class="error"><?= htmlspecialchars($errors['category_id']) ?></div>
    <?php endif; ?>
</div>

<div class="form-group">
    <label for="status">Status</label>
    <select id="status" name="status">
        <option value="draft" <?= (($old_input['status'] ?? 'draft') === 'draft') ? 'selected' : '' ?>>Draft</option>
        <option value="published" disabled>Published (functionality to be implemented)</option>
        <option value="archived" <?= (($old_input['status'] ?? '') === 'archived') ? 'selected' : '' ?>>Archived</option>
    </select>
     <?php if (isset($errors['status'])): ?>
        <div class="error"><?= htmlspecialchars($errors['status']) ?></div>
    <?php endif; ?>
</div>