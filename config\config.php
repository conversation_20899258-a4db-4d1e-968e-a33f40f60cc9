<?php

declare(strict_types=1);

// Basic Configuration for Brenzley Blog

// --- Database Configuration ---
// Prioritize environment variables if set (common in Docker environments),
// otherwise fall back to default values.
define('DB_HOST', getenv('DB_HOST') ?: 'localhost');
define('DB_NAME', getenv('DB_NAME') ?: 'brenzley_db');
define('DB_USER', getenv('DB_USER') ?: 'root');
define('DB_PASS', getenv('DB_PASS') ?: ''); // Default empty password
define('DB_CHARSET', getenv('DB_CHARSET') ?: 'utf8mb4');

// --- Site Configuration ---
// For Docker, BASE_URL might be http://localhost:8080 (or whatever port you map)
// Or, if you set up a reverse proxy, it could be your custom domain.
// This define might need adjustment based on how you access the Dockerized app.
define('APP_NAME', 'Brenzley');
define('BASE_URL', 'http://localhost:8080'); // Updated for Docker

// --- Environment ---
// Set to 'development' or 'production'
define('ENVIRONMENT', 'development');

// --- Error Reporting (Development vs Production) ---
if (ENVIRONMENT === 'development') {
    error_reporting(E_ALL);
    ini_set('display_errors', '1');
} else {
    error_reporting(0);
    ini_set('display_errors', '0');
    // TODO: Implement robust logging for production errors
}

// --- Other Settings ---
// Add other application-wide settings here 