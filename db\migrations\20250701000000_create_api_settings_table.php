<?php

declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

final class CreateApiSettingsTable extends AbstractMigration
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-change-method
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function up(): void
    {
        // Create the api_settings table
        $table = $this->table('api_settings');
        $table->addColumn('provider', 'string', ['limit' => 50, 'null' => false])
              ->addColumn('is_active', 'boolean', ['default' => false])
              ->addColumn('is_verified', 'boolean', ['default' => false])
              ->addColumn('api_key', 'text', ['null' => true]) // Will store encrypted API key
              ->addColumn('api_key_iv', 'string', ['limit' => 64, 'null' => true]) // Initialization vector for encryption
              ->addColumn('base_url', 'string', ['limit' => 255, 'null' => true]) // For OpenAI compatible APIs
              ->addColumn('selected_model', 'string', ['limit' => 100, 'null' => true])
              ->addColumn('context_window', 'integer', ['default' => 8096, 'null' => false]) // Default context window size
              ->addColumn('settings', 'text', ['null' => true]) // JSON encoded additional settings
              ->addColumn('created_at', 'timestamp', ['default' => 'CURRENT_TIMESTAMP'])
              ->addColumn('updated_at', 'timestamp', ['default' => 'CURRENT_TIMESTAMP', 'update' => 'CURRENT_TIMESTAMP'])
              ->addIndex(['provider'], ['unique' => true])
              ->create();
              
        // Insert default providers
        // Check if the table is empty before inserting to prevent issues on re-runs if data persists
        if ($this->adapter->getAdapterType() !== 'sqlite') { // SQLite doesn't support truncate directly like this
            $rowCount = $this->fetchRow('SELECT COUNT(*) as count FROM api_settings');
            if ($rowCount && $rowCount['count'] == 0) {
                $this->seedDefaultProviders();
            } elseif (!$rowCount) { // If fetchRow returns false or table doesn't exist yet (shouldn't happen here)
                 $this->seedDefaultProviders();
            }
        } else {
             $this->seedDefaultProviders(); // For SQLite, or if you prefer to always try inserting and let unique key handle it (if applicable)
        }
    }

    private function seedDefaultProviders(): void
    {
        $this->table('api_settings')->insert([
            [
                'provider' => 'none',
                'is_active' => true,
                'is_verified' => true,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'provider' => 'google_ai',
                'is_active' => false,
                'is_verified' => false,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'provider' => 'openrouter',
                'is_active' => false,
                'is_verified' => false,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'provider' => 'openai_compatible',
                'is_active' => false,
                'is_verified' => false,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ]
        ])->saveData();
    }

    public function down(): void
    {
        $this->table('api_settings')->drop()->save();
    }
}
