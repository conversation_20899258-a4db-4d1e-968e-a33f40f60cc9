<?php

namespace Brenzley\Controllers\Admin;

use <PERSON><PERSON><PERSON><PERSON>\Core\View;
use <PERSON><PERSON><PERSON><PERSON>\Core\Session;
use <PERSON><PERSON>zley\Models\ApiSettingsModel;
use B<PERSON>zley\Services\ApiProviders\StreamingApiProviderInterface;

/**
 * Controller for AI provider testing page
 */
class AiTestController
{
    private ApiSettingsModel $apiSettingsModel;

    /**
     * Constructor
     */
    public function __construct()
    {
        $this->apiSettingsModel = new ApiSettingsModel();
    }

    /**
     * Display the AI test page
     */
    public function index(): void
    {
        // Check if user is logged in
        if (!Session::get('user_id')) {
            header('Location: /login');
            exit;
        }

        // Check if user has admin role
        if (Session::get('user_role') !== 'admin') {
            header('Location: /dashboard');
            exit;
        }

        // Get active provider
        $activeProvider = $this->apiSettingsModel->getActiveProvider();

        // Get all providers
        $providers = $this->apiSettingsModel->getAllProviders();
        
        // Filter out Google AI and Groq providers
        $providers = array_filter($providers, function($provider) {
            return !in_array($provider['provider'], ['google_ai', 'groq']);
        });
        
        // Re-index the array to avoid gaps in indices
        $providers = array_values($providers);
        
        // Also filter active provider if it's Google AI or Groq
        if ($activeProvider && in_array($activeProvider['provider'], ['google_ai', 'groq'])) {
            $activeProvider = null;
        }

        // Capture content in buffer to use with layout
        ob_start();
        View::render('admin/ai-test', [
            'title' => 'AI Provider Test',
            'activeProvider' => $activeProvider,
            'providers' => $providers
        ], null);
        $content = ob_get_clean();

        // Render with admin layout
        View::renderLayout('admin', [
            'title' => 'AI Provider Test',
            'content' => $content,
            'userRole' => Session::get('user_role', '')
        ], ['admin-ai-test']);
    }

    /**
     * Test the AI provider with streaming
     */
    public function testStream(): void
    {
        // Check authentication
        if (!Session::isLoggedIn()) {
            http_response_code(401);
            echo "Error: Authentication required\n";
            exit;
        }

        // Check if user has admin role
        if (Session::get('user_role') !== 'admin') {
            http_response_code(403);
            echo "Error: Unauthorized\n";
            exit;
        }

        // Get request data
        $prompt = $_GET['prompt'] ?? '';
        $providerId = $_GET['provider_id'] ?? '';

        // Validate request
        if (empty($prompt)) {
            http_response_code(400);
            echo "Error: Prompt is required\n";
            exit;
        }

        // Get active provider if provider_id is not specified
        if (empty($providerId)) {
            $activeProvider = $this->apiSettingsModel->getActiveProvider();
            if (!$activeProvider) {
                http_response_code(500);
                echo "Error: No active AI provider configured\n";
                exit;
            }
            $providerId = $activeProvider['id'];
        }

        // Get provider details
        $provider = $this->apiSettingsModel->getProvider((int)$providerId);
        if (!$provider) {
            http_response_code(404);
            echo "Error: Provider not found\n";
            exit;
        }

        // Set headers for streaming response
        header('Content-Type: text/event-stream');
        header('Cache-Control: no-cache');
        header('Connection: keep-alive');
        header('X-Accel-Buffering: no'); // Disable Nginx buffering
        header('Access-Control-Allow-Origin: *');
        header('Access-Control-Allow-Headers: Cache-Control');

        // Disable output buffering completely
        while (ob_get_level()) {
            ob_end_clean();
        }

        // Disable implicit flush and set up for immediate output
        ini_set('implicit_flush', '1');
        ini_set('output_buffering', '0');
        
        // If using FastCGI, finish the request to avoid buffering
        if (function_exists('fastcgi_finish_request')) {
            // We'll call this after headers but before streaming
            register_shutdown_function('fastcgi_finish_request');
        }

        // Create API provider instance
        $apiProvider = $this->createApiProvider($provider);

        if (!$apiProvider) {
            echo "data: " . json_encode(['error' => 'Failed to create API provider']) . "\n\n";
            flush();
            exit;
        }

        try {
            // Send start message
            echo "data: " . json_encode(['type' => 'start', 'message' => 'Starting stream...']) . "\n\n";
            flush();

            // Start streaming with optimized callback for immediate forwarding
            // Use default streaming behavior
            $rawOptions = [];
            $apiProvider->streamCompletion($prompt, function($chunk) {
                // Send chunk in the format expected by frontend with immediate flush
                if ($chunk !== null && $chunk !== '') {
                    // Use minimal processing - just wrap in expected JSON format
                    echo "data: " . json_encode(['type' => 'chunk', 'content' => $chunk]) . "\n\n";
                    
                    // Force immediate output
                    if (ob_get_level()) {
                        ob_flush();
                    }
                    flush();
                }
            }, $rawOptions);

            // Send completion signal
            echo "data: " . json_encode(['type' => 'end', 'message' => 'Stream completed']) . "\n\n";
            flush();
        } catch (\Throwable $e) {
            // Determine error type and provide specific handling
            $errorType = 'unknown';
            $errorMessage = $e->getMessage();
            
            if (strpos($errorMessage, 'Base URL is required') !== false) {
                $errorType = 'configuration';
                $errorMessage = 'OpenAI compatible provider requires a base URL to be configured.';
            } elseif (strpos($errorMessage, 'No model selected') !== false) {
                $errorType = 'configuration';
                $errorMessage = 'No model has been selected for this provider.';
            } elseif (strpos($errorMessage, 'Connection') !== false || strpos($errorMessage, 'cURL') !== false) {
                $errorType = 'network';
                $errorMessage = 'Network connection error: ' . $errorMessage;
            } elseif (strpos($errorMessage, 'timeout') !== false) {
                $errorType = 'timeout';
                $errorMessage = 'Request timed out. The AI provider may be experiencing high load.';
            } elseif (strpos($errorMessage, 'Unauthorized') !== false || strpos($errorMessage, '401') !== false) {
                $errorType = 'authentication';
                $errorMessage = 'Authentication failed. Please check your API key.';
            } elseif (strpos($errorMessage, 'Rate limit') !== false || strpos($errorMessage, '429') !== false) {
                $errorType = 'rate_limit';
                $errorMessage = 'Rate limit exceeded. Please wait before making another request.';
            }

            // Send detailed error in SSE format
            echo "data: " . json_encode([
                'type' => 'error', 
                'message' => $errorMessage,
                'error_type' => $errorType,
                'provider' => $provider['provider'] ?? 'unknown'
            ]) . "\n\n";
            flush();
        }

        exit;
    }

    /**
     * Create an API provider instance from provider settings
     *
     * @param array $provider Provider settings
     * @return StreamingApiProviderInterface|null
     */
    private function createApiProvider(array $provider): ?StreamingApiProviderInterface
    {
        // Get API key
        $apiKey = $this->apiSettingsModel->getDecryptedApiKey($provider['provider']) ?? '';
        if (empty($apiKey)) {
            return null;
        }

        // Get provider settings
        $settings = [
            'api_key' => $apiKey,
            'selected_model' => $provider['selected_model'] ?? null,
            'context_window' => (int)($provider['context_window'] ?? 8096),
            'base_url' => $provider['base_url'] ?? '', // Include base_url from database settings
            'settings' => json_decode($provider['settings'] ?? '{}', true) ?? []
        ];

        // Create provider instance
        // Pass the full settings array including base_url
        return \Brenzley\Services\ApiProviders\ApiProviderFactory::create($provider['provider'], $settings);
    }

    /**
     * Get provider class based on provider name
     *
     * @param string $providerName Provider name
     * @return string|null Provider class or null
     */
    private function getProviderClass(string $providerName): ?string
    {
        $providers = [
            'openai' => 'Brenzley\\Services\\ApiProviders\\OpenAiProvider',
            'anthropic' => 'Brenzley\\Services\\ApiProviders\\AnthropicProvider',
            'openrouter' => 'Brenzley\\Services\\ApiProviders\\OpenRouterProvider',
            'openai_compatible' => 'Brenzley\\Services\\ApiProviders\\OpenAiCompatibleProvider',
        ];

        return $providers[$providerName] ?? null;
    }
}
