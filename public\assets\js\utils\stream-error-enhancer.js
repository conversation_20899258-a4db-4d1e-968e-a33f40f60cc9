/**
 * Stream Error Enhancer
 * Enhances existing EventSource handlers to show better error messages
 */
(function() {
    'use strict';
    
    // Enhanced error display function
    window.showStreamError = function(container, message, type = 'error') {
        if (!container) return;
        
        const errorPrefix = type === 'connection' ? '🔌 Connection Error' : 
                           type === 'parse' ? '📝 Parse Error' : 
                           '❌ Error';
        
        // Add error to container
        const errorText = `\n\n${errorPrefix}: ${message}`;
        
        if (container.tagName === 'TEXTAREA' || container.tagName === 'INPUT') {
            container.value += errorText;
        } else if (container.contentEditable === 'true') {
            container.textContent += errorText;
        } else {
            container.textContent += errorText;
        }
        
        // Scroll to bottom if possible
        if (container.scrollTop !== undefined) {
            container.scrollTop = container.scrollHeight;
        }
        
        // Show toast if available
        if (window.showToast) {
            window.showToast(`${errorPrefix}: ${message}`, 'error');
        }
    };
    
    // Enhanced message parser that handles all message types
    window.parseStreamMessage = function(event, container, chunkCallback, endCallback, errorCallback) {
        try {
            const data = JSON.parse(event.data);
            
            switch (data.type) {
                case 'chunk':
                    if (data.content && typeof chunkCallback === 'function') {
                        chunkCallback(data.content);
                    }
                    break;
                    
                case 'end':
                    if (typeof endCallback === 'function') {
                        endCallback(data);
                    }
                    break;
                    
                case 'error':
                    const errorMessage = data.message || 'Unknown streaming error occurred';
                    window.showStreamError(container, errorMessage, 'error');
                    if (typeof errorCallback === 'function') {
                        errorCallback(errorMessage);
                    }
                    break;
                    
                case 'start':
                    // Optional: Handle start messages
                    console.log('Stream started:', data.message || 'Starting...');
                    break;
                    
                case 'phase':
                    // Optional: Handle phase messages for multi-step processes
                    console.log('Stream phase:', data);
                    break;
                    
                default:
                    console.warn('Unknown stream message type:', data.type, data);
            }
        } catch (error) {
            console.error('Error parsing stream data:', error);
            window.showStreamError(container, 'Invalid response format', 'parse');
            if (typeof errorCallback === 'function') {
                errorCallback('Parse error: Invalid response format');
            }
        }
    };
    
    // Enhanced connection error handler
    window.handleStreamConnectionError = function(event, container, errorCallback) {
        console.error('EventSource failed:', event);
        window.showStreamError(container, 'Stream connection failed. Please check your API settings and try again.', 'connection');
        if (typeof errorCallback === 'function') {
            errorCallback('Connection failed');
        }
    };
    
    console.log('Stream Error Enhancer loaded');
})();