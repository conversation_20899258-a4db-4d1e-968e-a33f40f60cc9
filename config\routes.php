<?php

declare(strict_types=1);

use <PERSON><PERSON><PERSON><PERSON>\Controllers\AuthController;
use <PERSON><PERSON><PERSON><PERSON>\Controllers\DashboardController;
use <PERSON><PERSON><PERSON><PERSON>\Controllers\Admin\PostController as AdminPostController;
use <PERSON><PERSON><PERSON><PERSON>\Controllers\Admin\CategoryController as AdminCategoryController;
use <PERSON><PERSON>zley\Controllers\Admin\TagController as AdminTagController;
use B<PERSON>zley\Controllers\Admin\CommentController as AdminCommentController;
use B<PERSON>zley\Controllers\Admin\ApiSettingsController;
use B<PERSON>zley\Controllers\Admin\AiTestController;
use B<PERSON><PERSON><PERSON>\Controllers\CommentController;
use <PERSON><PERSON><PERSON><PERSON>\Controllers\HomeController;
use B<PERSON>zley\Controllers\PostController as PublicPostController;
use B<PERSON><PERSON>y\Controllers\CategoryController as PublicCategoryController;
use B<PERSON>zley\Controllers\TagController as PublicTagController;
use <PERSON><PERSON><PERSON><PERSON>\Controllers\InteractionController;

// Route definitions for FastRoute
// Format: $routeCollector->addRoute(METHOD, '/path', [Controller::class, 'methodName'], ['auth' => bool (optional)]);
// The 'auth' key is a custom flag to indicate if admin/author access is required.

return function(FastRoute\RouteCollector $r) {
    // Public Routes
    $r->addRoute('GET', '/', [HomeController::class, 'index']);
    $r->addRoute('GET', '/register', [AuthController::class, 'showRegistrationForm']);
    $r->addRoute('POST', '/register', [AuthController::class, 'register']);
    $r->addRoute('GET', '/login', [AuthController::class, 'showLoginForm']);
    $r->addRoute('POST', '/login', [AuthController::class, 'login']);
    $r->addRoute('GET', '/logout', [AuthController::class, 'logout']); // Should ideally be POST, but keeping GET for now
    $r->addRoute('GET', '/posts/{slug:[a-zA-Z0-9-]+}', [PublicPostController::class, 'show']);
    $r->addRoute('POST', '/posts/{postId:\d+}/comments', [CommentController::class, 'store']);

    // --- User Profile ---
    $r->addRoute('GET', '/profile', [DashboardController::class, 'profile']);

    // --- User Interactions (Require Login - check in controller) ---
    $r->addRoute('POST', '/posts/{id:\d+}/read', [InteractionController::class, 'markAsRead']);
    $r->addRoute('POST', '/posts/{id:\d+}/save', [InteractionController::class, 'saveArticle']);
    $r->addRoute('POST', '/posts/{id:\d+}/unsave', [InteractionController::class, 'unsaveArticle']);

    // --- Categories Listing Page ---
    $r->addRoute('GET', '/categories', [PublicCategoryController::class, 'index']);

    // --- Single Post ---
    $r->addRoute('GET', '/posts/{slug:.+}', [PublicPostController::class, 'show']);

    // --- Category Archives ---
    $r->addRoute('GET', '/categories/{slug:.+}', [PublicCategoryController::class, 'show']);

    // --- Tag Archives ---
    $r->addRoute('GET', '/tags/{slug:.+}', [PublicTagController::class, 'show']);

    // --- Tags Listing Page ---
    $r->addRoute('GET', '/tags', [PublicTagController::class, 'index']);

    // Admin Routes (require authentication)
    $r->addGroup('/admin', function (FastRoute\RouteCollector $r) {
        $r->addRoute('GET', '', [DashboardController::class, 'index'], ['auth' => true]);

        // Posts
        $r->addRoute('GET', '/posts', [AdminPostController::class, 'index'], ['auth' => true]);
        $r->addRoute('GET', '/posts/create', [AdminPostController::class, 'create'], ['auth' => true]);
        $r->addRoute('POST', '/posts/store', [AdminPostController::class, 'store'], ['auth' => true]);
        $r->addRoute('GET', '/posts/edit/{id:\d+}', [AdminPostController::class, 'edit'], ['auth' => true]);
        $r->addRoute('GET', '/posts/collaborative-edit/{id:\d+}', [AdminPostController::class, 'collaborativeEdit'], ['auth' => true]);
        $r->addRoute('GET', '/posts/version-history/{id:\d+}', [AdminPostController::class, 'versionHistory'], ['auth' => true]);
        $r->addRoute('POST', '/posts/update/{id:\d+}', [AdminPostController::class, 'update'], ['auth' => true]);
        $r->addRoute('POST', '/posts/delete/{id:\d+}', [AdminPostController::class, 'delete'], ['auth' => true]);

        // Categories
        $r->addRoute('GET', '/categories', [AdminCategoryController::class, 'index'], ['auth' => true]);
        $r->addRoute('GET', '/categories/create', [AdminCategoryController::class, 'create'], ['auth' => true]);
        $r->addRoute('POST', '/categories/store', [AdminCategoryController::class, 'store'], ['auth' => true]);
        $r->addRoute('GET', '/categories/edit/{id:\d+}', [AdminCategoryController::class, 'edit'], ['auth' => true]);
        $r->addRoute('POST', '/categories/update/{id:\d+}', [AdminCategoryController::class, 'update'], ['auth' => true]);
        $r->addRoute('POST', '/categories/delete/{id:\d+}', [AdminCategoryController::class, 'delete'], ['auth' => true]);

        // Tags
        $r->addRoute('GET', '/tags', [AdminTagController::class, 'index'], ['auth' => true]);
        $r->addRoute('GET', '/tags/create', [AdminTagController::class, 'create'], ['auth' => true]);
        $r->addRoute('POST', '/tags/store', [AdminTagController::class, 'store'], ['auth' => true]);
        $r->addRoute('GET', '/tags/edit/{id:\d+}', [AdminTagController::class, 'edit'], ['auth' => true]);
        $r->addRoute('POST', '/tags/update/{id:\d+}', [AdminTagController::class, 'update'], ['auth' => true]);
        $r->addRoute('POST', '/tags/delete/{id:\d+}', [AdminTagController::class, 'delete'], ['auth' => true]);

        // Comments
        $r->addRoute('GET', '/comments', [AdminCommentController::class, 'index'], ['auth' => true]);
        $r->addRoute('POST', '/comments/update/{id:\d+}', [AdminCommentController::class, 'updateStatus'], ['auth' => true]);
        $r->addRoute('POST', '/comments/delete/{id:\d+}', [AdminCommentController::class, 'delete'], ['auth' => true]);

        // API Settings
        $r->addRoute('GET', '/api-settings', [ApiSettingsController::class, 'index'], ['auth' => true, 'role' => 'admin']);
        $r->addRoute('POST', '/api-settings/update', [ApiSettingsController::class, 'update'], ['auth' => true, 'role' => 'admin']);
        $r->addRoute('POST', '/api-settings/set-active', [ApiSettingsController::class, 'setActive'], ['auth' => true, 'role' => 'admin']);

        // Media Library
        $r->addRoute('GET', '/media', [\Brenzley\Controllers\Admin\MediaLibraryController::class, 'index'], ['auth' => true]);

        // AI Test Page
        $r->addRoute('GET', '/ai-test', [AiTestController::class, 'index'], ['auth' => true, 'role' => 'admin']);
    });

    // --- API Routes ---
    // Grouping API routes under /api/admin
    $r->addGroup('/api/admin', function (FastRoute\RouteCollector $r) {
        // Posts API
        $r->addRoute('POST', '/ai/generate', [AdminPostController::class, 'generateContentApi'], ['auth' => true]); // Generate content with AI
        $r->addRoute('POST', '/ai/enhance', [AdminPostController::class, 'enhanceContentApi'], ['auth' => true]); // Enhance content with AI
        $r->addRoute('POST', '/ai/analyze-seo', [AdminPostController::class, 'analyzeSeoApi'], ['auth' => true]); // Analyze SEO

        // Media API
        $r->addRoute('GET', '/media', [\Brenzley\Controllers\Admin\MediaController::class, 'indexApi'], ['auth' => true, 'role' => 'admin']); // List media
        $r->addRoute('POST', '/media/upload', [\Brenzley\Controllers\Admin\MediaController::class, 'uploadApi'], ['auth' => true, 'role' => 'admin']); // Upload media
        $r->addRoute('POST', '/media/ai-generated', [\Brenzley\Controllers\Admin\MediaController::class, 'saveAiGeneratedImageApi'], ['auth' => true, 'role' => 'admin']); // Save AI-generated image
        $r->addRoute('DELETE', '/media/{id:[0-9]+}', [\Brenzley\Controllers\Admin\MediaController::class, 'deleteApi'], ['auth' => true, 'role' => 'admin']); // Delete media
        $r->addRoute('PUT', '/media/{id:[0-9]+}/metadata', [\Brenzley\Controllers\Admin\MediaController::class, 'updateMetadataApi'], ['auth' => true, 'role' => 'admin']); // Update media metadata

        // Media Suggestion API
        $r->addRoute('POST', '/media/suggest-featured', [\Brenzley\Controllers\Api\MediaSuggestionController::class, 'suggestFeaturedImages']); // Suggest featured images
        $r->addRoute('POST', '/media/generate-ai-image', [\Brenzley\Controllers\Api\MediaSuggestionController::class, 'generateAIImage']); // Generate AI image
        $r->addRoute('POST', '/media/generate-caption', [\Brenzley\Controllers\Api\CaptionGeneratorController::class, 'generateCaptions']); // Generate image captions

        // Collaboration API
        $r->addRoute('GET', '/collaboration/connect', [\Brenzley\Controllers\Api\CollaborationController::class, 'connect']); // WebSocket connection
        $r->addRoute('POST', '/collaboration/sync', [\Brenzley\Controllers\Api\CollaborationController::class, 'sync']); // Sync document changes
        $r->addRoute('POST', '/collaboration/presence', [\Brenzley\Controllers\Api\CollaborationController::class, 'updatePresence']); // Update user presence
        $r->addRoute('GET', '/collaboration/collaborators', [\Brenzley\Controllers\Api\CollaborationController::class, 'getCollaborators']); // Get active collaborators

        // Version History API
        $r->addRoute('GET', '/versions/list', [\Brenzley\Controllers\Api\VersionController::class, 'listVersions']); // List versions
        $r->addRoute('GET', '/versions/get', [\Brenzley\Controllers\Api\VersionController::class, 'getVersion']); // Get version
        $r->addRoute('GET', '/versions/compare', [\Brenzley\Controllers\Api\VersionController::class, 'compareVersions']); // Compare versions
        $r->addRoute('POST', '/versions/restore', [\Brenzley\Controllers\Api\VersionController::class, 'restoreVersion']); // Restore version

        // API Settings API
        $r->addRoute('GET', '/ai-settings/models', [ApiSettingsController::class, 'getModelsApi']); // Get models for a provider
        $r->addRoute('GET', '/ai-settings/test-connection', [ApiSettingsController::class, 'testConnectionApi']); // Test connection to a provider

        // AI Content Generation API
        // Content Sessions
        $r->addRoute('POST', '/ai/sessions/create', [\Brenzley\Controllers\Api\AiContentController::class, 'createSession']);
        $r->addRoute('GET', '/ai/sessions/get', [\Brenzley\Controllers\Api\AiContentController::class, 'getSession']);
        $r->addRoute('POST', '/ai/sessions/update', [\Brenzley\Controllers\Api\AiContentController::class, 'updateSession']);
        $r->addRoute('GET', '/ai/sessions/active', [\Brenzley\Controllers\Api\AiContentController::class, 'getActiveSessions']);

        // Blog Ideas
        $r->addRoute('POST', '/ai/blog-ideas/generate', [\Brenzley\Controllers\Api\AiBlogIdeaController::class, 'generate']);
        $r->addRoute('GET', '/ai/blog-ideas/stream', [\Brenzley\Controllers\Api\AiBlogIdeaController::class, 'stream']);
        $r->addRoute('GET', '/ai/blog-ideas/get', [\Brenzley\Controllers\Api\AiBlogIdeaController::class, 'get']);
        $r->addRoute('GET', '/ai/blog-ideas/list', [\Brenzley\Controllers\Api\AiBlogIdeaController::class, 'list']);
        $r->addRoute('POST', '/ai/blog-ideas/update', [\Brenzley\Controllers\Api\AiBlogIdeaController::class, 'update']);
        $r->addRoute('POST', '/ai/blog-ideas/delete', [\Brenzley\Controllers\Api\AiBlogIdeaController::class, 'delete']);

        // Blog Outlines
        $r->addRoute('POST', '/ai/blog-outlines/generate', [\Brenzley\Controllers\Api\AiBlogOutlineController::class, 'generate']);
        $r->addRoute('GET', '/ai/blog-outlines/stream', [\Brenzley\Controllers\Api\AiBlogOutlineController::class, 'stream']);
        $r->addRoute('GET', '/ai/blog-outlines/get', [\Brenzley\Controllers\Api\AiBlogOutlineController::class, 'get']);
        $r->addRoute('GET', '/ai/blog-outlines/list', [\Brenzley\Controllers\Api\AiBlogOutlineController::class, 'list']);
        $r->addRoute('POST', '/ai/blog-outlines/update', [\Brenzley\Controllers\Api\AiBlogOutlineController::class, 'update']);
        $r->addRoute('POST', '/ai/blog-outlines/delete', [\Brenzley\Controllers\Api\AiBlogOutlineController::class, 'delete']);

        // Blog Sections
        $r->addRoute('POST', '/ai/blog-sections/generate', [\Brenzley\Controllers\Api\AiBlogSectionController::class, 'generate']);
        $r->addRoute('GET', '/ai/blog-sections/get', [\Brenzley\Controllers\Api\AiBlogSectionController::class, 'get']);
        $r->addRoute('GET', '/ai/blog-sections/list', [\Brenzley\Controllers\Api\AiBlogSectionController::class, 'list']);
        $r->addRoute('POST', '/ai/blog-sections/update', [\Brenzley\Controllers\Api\AiBlogSectionController::class, 'update']);
        $r->addRoute('POST', '/ai/blog-sections/reorder', [\Brenzley\Controllers\Api\AiBlogSectionController::class, 'reorder']);
        $r->addRoute('POST', '/ai/blog-sections/delete', [\Brenzley\Controllers\Api\AiBlogSectionController::class, 'delete']);

        // Blog Posts
        $r->addRoute('POST', '/ai/blog-posts/generate', [\Brenzley\Controllers\Api\AiBlogPostController::class, 'generate']);
        $r->addRoute('POST', '/ai/blog-posts/stream-content', [\Brenzley\Controllers\Api\AiBlogPostController::class, 'streamFullContent']);
        $r->addRoute('POST', '/ai/blog-posts/generate-from-sections', [\Brenzley\Controllers\Api\AiBlogPostController::class, 'generateFromSections']);
        $r->addRoute('GET', '/ai/blog-posts/get', [\Brenzley\Controllers\Api\AiBlogPostController::class, 'get']);
        $r->addRoute('GET', '/ai/blog-posts/list', [\Brenzley\Controllers\Api\AiBlogPostController::class, 'list']);
        $r->addRoute('POST', '/ai/blog-posts/update', [\Brenzley\Controllers\Api\AiBlogPostController::class, 'update']);
        $r->addRoute('POST', '/ai/blog-posts/save-state', [\Brenzley\Controllers\Api\AiBlogPostController::class, 'saveState']);
        $r->addRoute('POST', '/ai/blog-posts/convert', [\Brenzley\Controllers\Api\AiBlogPostController::class, 'convertToRegularPost']);
        // Update AI post parameters (alt texts for placeholders without media)
        $r->addRoute('POST', '/ai/blog-posts/update-parameters', [\Brenzley\Controllers\Api\AiBlogPostController::class, 'updateParameters']);

        // AI Test
        $r->addRoute('GET', '/ai/test-stream', [AiTestController::class, 'testStream']);

        // Publish API - REMOVED (functionality to be reimplemented)
        // $r->addRoute('POST', '/posts/publish', [\Brenzley\Controllers\Admin\PublishController::class, 'publish'], ['auth' => true]);
        $r->addRoute('POST', '/ai/blog-posts/delete', [\Brenzley\Controllers\Api\AiBlogPostController::class, 'delete']);

        // SEO Streaming
        $r->addRoute('POST', '/ai/seo/stream', [\Brenzley\Controllers\Api\AiSeoController::class, 'stream']);
        // SEO Save
        $r->addRoute('POST', '/ai/seo/save', [\Brenzley\Controllers\Api\AiSeoController::class, 'save']);

        // Image Metadata
        $r->addRoute('POST', '/ai/image-metadata/generate-caption', [\Brenzley\Controllers\Api\AiImageMetadataController::class, 'generateCaption']);
        $r->addRoute('POST', '/ai/image-metadata/generate-alt-text', [\Brenzley\Controllers\Api\AiImageMetadataController::class, 'generateAltText']);
        $r->addRoute('POST', '/ai/image-metadata/stream-alts', [\Brenzley\Controllers\Api\AiImageMetadataController::class, 'streamAltTexts']);
        $r->addRoute('GET', '/ai/image-metadata/get', [\Brenzley\Controllers\Api\AiImageMetadataController::class, 'get']);
        $r->addRoute('GET', '/ai/image-metadata/get-for-media', [\Brenzley\Controllers\Api\AiImageMetadataController::class, 'getForMedia']);
        $r->addRoute('POST', '/ai/image-metadata/apply', [\Brenzley\Controllers\Api\AiImageMetadataController::class, 'applyToMedia']);

        // Legacy Content Generation API (for backward compatibility)
        $r->addRoute('POST', '/content/generate-blog-idea', [\Brenzley\Controllers\Api\ContentGenerationController::class, 'generateBlogIdea']);
        $r->addRoute('POST', '/content/generate-blog-outline', [\Brenzley\Controllers\Api\ContentGenerationController::class, 'generateBlogOutline']);
        $r->addRoute('POST', '/content/generate-blog-section', [\Brenzley\Controllers\Api\ContentGenerationController::class, 'generateBlogSection']);
        $r->addRoute('POST', '/content/generate-blog-post', [\Brenzley\Controllers\Api\ContentGenerationController::class, 'generateBlogPost']);
        $r->addRoute('POST', '/content/generate-seo-metadata', [\Brenzley\Controllers\Api\ContentGenerationController::class, 'generateSeoMetadata']);
    });
};