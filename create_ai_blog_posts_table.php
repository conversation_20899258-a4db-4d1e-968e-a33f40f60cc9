<?php

// Load configuration
require_once __DIR__ . '/config/config.php';

// Connect to the database
try {
    $dsn = sprintf(
        'mysql:host=%s;dbname=%s;charset=%s',
        DB_HOST, 
        DB_NAME, 
        DB_CHARSET
    );
    $pdo = new PDO($dsn, DB_USER, DB_PASS, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false,
    ]);

    echo "Database connection successful to " . DB_NAME . "\n";

    // Create the ai_blog_posts table
    $sql = "CREATE TABLE IF NOT EXISTS ai_blog_posts (
        id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
        user_id INT UNSIGNED NOT NULL,
        blog_idea_id INT UNSIGNED,
        outline_id INT UNSIGNED,
        title VARCHAR(255) NOT NULL,
        content_html LONGTEXT NULL,
        provider VARCHAR(50) NOT NULL,
        model VARCHAR(100) NOT NULL,
        parameters TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX (user_id),
        INDEX (blog_idea_id),
        INDEX (outline_id),
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (blog_idea_id) REFERENCES ai_blog_ideas(id) ON DELETE SET NULL,
        FOREIGN KEY (outline_id) REFERENCES ai_blog_outlines(id) ON DELETE SET NULL
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

    $pdo->exec($sql);
    echo "ai_blog_posts table created successfully.\n";

    // Create the ai_seo_metadata table if it doesn't exist
    $sql = "CREATE TABLE IF NOT EXISTS ai_seo_metadata (
        id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
        user_id INT UNSIGNED NOT NULL,
        content_id INT UNSIGNED NOT NULL,
        content_type ENUM('post', 'ai_blog_post') NOT NULL,
        meta_title VARCHAR(255) NOT NULL,
        meta_description TEXT NOT NULL,
        focus_keywords TEXT NOT NULL,
        slug VARCHAR(255) NOT NULL,
        provider VARCHAR(50) NOT NULL,
        model VARCHAR(100) NOT NULL,
        parameters TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX (user_id),
        INDEX (content_id, content_type),
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

    $pdo->exec($sql);
    echo "ai_seo_metadata table created successfully.\n";

    // Create the ai_image_metadata table if it doesn't exist
    $sql = "CREATE TABLE IF NOT EXISTS ai_image_metadata (
        id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
        user_id INT UNSIGNED NOT NULL,
        media_id INT UNSIGNED NOT NULL,
        caption TEXT,
        alt_text TEXT,
        provider VARCHAR(50) NOT NULL,
        model VARCHAR(100) NOT NULL,
        parameters TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX (user_id),
        INDEX (media_id),
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (media_id) REFERENCES media(id) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

    $pdo->exec($sql);
    echo "ai_image_metadata table created successfully.\n";

    // Create the ai_usage_logs table if it doesn't exist
    $sql = "CREATE TABLE IF NOT EXISTS ai_usage_logs (
        id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
        user_id INT UNSIGNED NOT NULL,
        provider VARCHAR(50) NOT NULL,
        model VARCHAR(100) NOT NULL,
        content_type VARCHAR(50) NOT NULL,
        prompt_tokens INT NOT NULL DEFAULT 0,
        completion_tokens INT NOT NULL DEFAULT 0,
        total_tokens INT NOT NULL DEFAULT 0,
        cost DECIMAL(10, 6) DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX (user_id),
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

    $pdo->exec($sql);
    echo "ai_usage_logs table created successfully.\n";

    // Create the ai_content_sessions table if it doesn't exist
    $sql = "CREATE TABLE IF NOT EXISTS ai_content_sessions (
        id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
        user_id INT UNSIGNED NOT NULL,
        session_type ENUM('idea', 'outline', 'section', 'post', 'seo') NOT NULL,
        status ENUM('in_progress', 'completed', 'abandoned') NOT NULL DEFAULT 'in_progress',
        idea_id INT UNSIGNED,
        outline_id INT UNSIGNED,
        post_id INT UNSIGNED,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX (user_id),
        INDEX (idea_id),
        INDEX (outline_id),
        INDEX (post_id),
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (idea_id) REFERENCES ai_blog_ideas(id) ON DELETE SET NULL,
        FOREIGN KEY (outline_id) REFERENCES ai_blog_outlines(id) ON DELETE SET NULL,
        FOREIGN KEY (post_id) REFERENCES ai_blog_posts(id) ON DELETE SET NULL
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

    $pdo->exec($sql);
    echo "ai_content_sessions table created successfully.\n";

    echo "\nAll AI content tables created successfully.\n";

} catch (PDOException $e) {
    echo "Database Error: " . $e->getMessage() . "\n";
}
