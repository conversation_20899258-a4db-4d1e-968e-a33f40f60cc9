/**
 * Manages the chat feature of the AI Article Creator.
 */
export class ChatFeature {
    constructor(uiManager, stateManager) {
        this.uiManager = uiManager;
        this.stateManager = stateManager;
        this.elements = uiManager.elements;
    }

    /**
     * Initializes chat event listeners and renders initial messages.
     */
    init() {
        if (this.elements.chatInput) {
            this.elements.chatInput.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' && this.elements.chatInput.value.trim()) {
                    this.sendChatMessage(this.elements.chatInput.value.trim());
                    e.preventDefault();
                }
            });
        }

        if (this.elements.chatSendBtn) {
            this.elements.chatSendBtn.addEventListener('click', () => {
                if (this.elements.chatInput && this.elements.chatInput.value.trim()) {
                    this.sendChatMessage(this.elements.chatInput.value.trim());
                }
            });
        }

        // chatToggle is now the close button inside the panel
        if (this.elements.chatToggle) {
            this.elements.chatToggle.addEventListener('click', () => {
                this.uiManager.toggleChat(); // This will toggle the 'active' class on the panel
            });
        }

        // New FAB to open/toggle the chat panel
        if (this.elements.chatFab) {
            this.elements.chatFab.addEventListener('click', () => {
                this.uiManager.toggleChat();
            });
        }

        this.uiManager.renderChatMessages(); // Render initial messages
    }

    /**
     * Sends a user message and simulates an AI response.
     * @param {string} message - The user's message.
     */
    sendChatMessage(message) {
        this.stateManager.addChatMessage('user', message);
        this.elements.chatInput.value = '';
        this.uiManager.renderChatMessages();

        // Simulate AI response
        setTimeout(() => {
            this.addAssistantMessage(this.getSimulatedChatResponse(message));
        }, 1000);
    }

    /**
     * Adds an assistant message to the chat.
     * @param {string} message - The assistant's message.
     */
    addAssistantMessage(message) {
        this.stateManager.addChatMessage('assistant', message);
        this.uiManager.renderChatMessages();
    }

    /**
     * Generates a simulated AI response based on the user's message.
     * This is a placeholder for actual AI interaction.
     * @param {string} message - The user's message.
     * @returns {string} A simulated AI response.
     */
    getSimulatedChatResponse(message) {
        const lowerMessage = message.toLowerCase();

        if (lowerMessage.includes('hello') || lowerMessage.includes('hi')) {
            return 'Hello! How can I help you with your article today?';
        }
        if (lowerMessage.includes('thank')) {
            return 'You\'re welcome! Is there anything else you\'d like help with?';
        }
        if (lowerMessage.includes('how') && lowerMessage.includes('seo')) {
            return 'SEO optimization functionality is currently unavailable in this editor.';
        }
        if (lowerMessage.includes('keyword')) {
            return 'Keyword optimization features are currently unavailable in this editor.';
        }
        if (lowerMessage.includes('outline')) {
            return 'A good article outline typically includes an introduction, 5-7 main sections with descriptive headings, and a conclusion. Each section should cover a specific aspect of your topic. I can help you generate or refine your outline if you\'d like.';
        }
        if (lowerMessage.includes('length') || lowerMessage.includes('word count')) {
            return 'For SEO purposes, articles should generally be at least 1,000 words, with comprehensive pieces often reaching 1,500-2,500 words. However, quality always trumps quantity - focus on providing value rather than hitting a specific word count.';
        }
        if (lowerMessage.includes('image') || lowerMessage.includes('picture')) {
            return 'I can help you with images. You can use the image placeholders to add or generate images. If you need specific image suggestions, let me know!';
        }

        const defaultResponses = [
            'I understand. How can I assist you further with that?',
            'That\'s an interesting point. What are your thoughts on how to proceed?',
            'I can help with that. What specific action would you like to take?',
            'Noted. Is there anything specific you\'d like to change or add?',
            'Thanks for the input. Let me know how I can help you implement that.'
        ];
        return defaultResponses[Math.floor(Math.random() * defaultResponses.length)];
    }

    /**
     * Gets a welcome message for a specific step.
     * @param {string} step - The current step.
     * @returns {string} The welcome message.
     */
    getStepWelcomeMessage(step) {
        switch (step) {
            case 'idea':
                return 'Let\'s start by generating some topic ideas. Please fill in the form above and click "Generate Ideas".';
            case 'outline':
                return 'Great choice! Now let\'s create an outline for your article. I\'ll generate a structured outline based on your selected idea.';
            case 'content':
                return 'Outline approved! I\'ll now generate the full article content based on the outline. Feel free to edit the content as needed.';
            case 'seo':
                return 'SEO optimization functionality is currently unavailable.';
            default:
                return 'What would you like to do next?';
        }
    }
}