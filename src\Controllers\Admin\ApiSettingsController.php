<?php

declare(strict_types=1);

namespace Brenzley\Controllers\Admin;

use <PERSON><PERSON><PERSON>y\Core\View;
use <PERSON><PERSON><PERSON>y\Core\Session;
use B<PERSON>zley\Models\ApiSettingsModel;
use Brenzley\Services\ApiProviders\ApiProviderFactory;

class ApiSettingsController
{
    private ApiSettingsModel $apiSettingsModel;

    public function __construct()
    {
        $this->apiSettingsModel = new ApiSettingsModel();
    }

    /**
     * Display the API settings page
     */
    public function index(): void
    {
        $apiSettings = $this->apiSettingsModel->findAll();
        
        // Filter out Google AI and Groq providers
        $apiSettings = array_filter($apiSettings, function($setting) {
            return !in_array($setting['provider'], ['google_ai', 'groq']);
        });
        
        // Re-index the array to avoid gaps in indices
        $apiSettings = array_values($apiSettings);

        // Capture content in buffer to use with layout
        ob_start();
        View::render('admin/api-settings/index', [
            'title' => 'API Settings',
            'apiSettings' => $apiSettings
        ], null);
        $content = ob_get_clean();

        // Render with admin layout
        View::renderLayout('admin', [
            'title' => 'API Settings',
            'content' => $content,
            'userRole' => Session::get('user_role', '')
        ], ['admin-api-settings']);
    }

    /**
     * Update API settings
     */
    public function update(): void
    {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            http_response_code(405);
            echo "Invalid request method.";
            return;
        }

        if (!Session::verifyCsrfToken($_POST['csrf_token'] ?? '')) {
            http_response_code(403);
            echo "Invalid security token.";
            return;
        }

        $provider = $_POST['provider'] ?? '';
        if (empty($provider)) {
            Session::set('flash_message', 'Provider is required.');
            Session::set('flash_type', 'error');
            header('Location: /admin/api-settings');
            exit;
        }

        // Prepare data for update
        $data = [
            'api_key' => $_POST['api_key'] ?? '',
            'selected_model' => $_POST['selected_model'] ?? '',
            'is_verified' => 0 // Reset verification status on update (use 0 for false)
        ];

        // Handle context window
        $contextWindow = (int)($_POST['context_window'] ?? 8096);

        // For OpenAI Compatible, validate the context window range
        if ($provider === 'openai_compatible') {
            if ($contextWindow < 4096) {
                $contextWindow = 4096;
            } elseif ($contextWindow > 1005000) {
                $contextWindow = 1005000;
            }

            $data['base_url'] = $_POST['base_url'] ?? '';
        }

        $data['context_window'] = $contextWindow;

        // Get existing settings to preserve any settings not in the form
        $existingSettings = [];
        $providerData = $this->apiSettingsModel->findByProvider($provider);
        if ($providerData && isset($providerData['settings']) && !empty($providerData['settings'])) {
            $existingSettings = json_decode($providerData['settings'], true) ?? [];
        }

        // Handle additional settings
        $settings = $existingSettings;

        // Debug: Log the POST data for settings
        error_log('Settings POST data: ' . json_encode($_POST['settings'] ?? []));
        error_log('Existing settings: ' . json_encode($existingSettings));

        // Rate limiting settings - use checkbox value if present, otherwise use hidden field
        if (isset($_POST['settings']['enable_rate_limiting'])) {
            // Checkbox is checked
            $settings['enable_rate_limiting'] = true;
            error_log('Rate limiting checkbox is checked');
        } else {
            // Checkbox is not checked, use the hidden field value
            $settings['enable_rate_limiting'] = false;
            error_log('Rate limiting checkbox is NOT checked');
        }

        // Validate and set requests per minute
        $requestsPerMinute = (int)($_POST['settings']['requests_per_minute'] ?? 60);
        if ($requestsPerMinute < 1) {
            $requestsPerMinute = 1;
        } elseif ($requestsPerMinute > 1000) {
            $requestsPerMinute = 1000;
        }
        $settings['requests_per_minute'] = $requestsPerMinute;

        // Validate and set tokens per minute
        $tokensPerMinute = (int)($_POST['settings']['tokens_per_minute'] ?? 100000);
        if ($tokensPerMinute < 1000) {
            $tokensPerMinute = 1000;
        } elseif ($tokensPerMinute > 1000000) {
            $tokensPerMinute = 1000000;
        }
        $settings['tokens_per_minute'] = $tokensPerMinute;

        // Usage tracking settings - use checkbox value if present, otherwise use hidden field
        if (isset($_POST['settings']['enable_usage_tracking'])) {
            // Checkbox is checked
            $settings['enable_usage_tracking'] = true;
            error_log('Usage tracking checkbox is checked');
        } else {
            // Checkbox is not checked, use the hidden field value
            $settings['enable_usage_tracking'] = false;
            error_log('Usage tracking checkbox is NOT checked');
        }
        error_log('Usage tracking value converted to: ' . json_encode($settings['enable_usage_tracking']));

        // Debug: Log the final settings
        error_log('Final settings to save: ' . json_encode($settings));

        // Add settings to data
        $data['settings'] = $settings;

        // Update settings
        if ($this->apiSettingsModel->update($provider, $data)) {
            // Test connection after update
            $testResult = $this->testConnection($provider);

            if ($testResult['success']) {
                // Mark as verified if test was successful
                $this->apiSettingsModel->update($provider, ['is_verified' => 1]); // Use 1 for true
                Session::set('flash_message', 'API settings updated and connection verified successfully.');
                Session::set('flash_type', 'success');
            } else {
                Session::set('flash_message', 'API settings updated but connection test failed: ' . $testResult['message']);
                Session::set('flash_type', 'warning');
            }
        } else {
            Session::set('flash_message', 'Failed to update API settings.');
            Session::set('flash_type', 'error');
        }

        header('Location: /admin/api-settings');
        exit;
    }

    /**
     * Set active provider
     */
    public function setActive(): void
    {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            http_response_code(405);
            echo "Invalid request method.";
            return;
        }

        if (!Session::verifyCsrfToken($_POST['csrf_token'] ?? '')) {
            http_response_code(403);
            echo "Invalid security token.";
            return;
        }

        $provider = $_POST['provider'] ?? '';
        if (empty($provider)) {
            Session::set('flash_message', 'Provider is required.');
            Session::set('flash_type', 'error');
            header('Location: /admin/api-settings');
            exit;
        }

        if ($this->apiSettingsModel->setActiveProvider($provider)) {
            Session::set('flash_message', 'Active provider updated successfully.');
            Session::set('flash_type', 'success');
        } else {
            Session::set('flash_message', 'Failed to update active provider. Make sure the provider is verified.');
            Session::set('flash_type', 'error');
        }

        header('Location: /admin/api-settings');
        exit;
    }

    /**
     * Test API connection
     *
     * @param string $provider
     * @param string|null $baseUrl
     * @param string|null $apiKey
     * @return array
     */
    private function testConnection(string $provider, ?string $baseUrl = null, ?string $apiKey = null): array
    {
        // Skip test for 'none' provider
        if ($provider === 'none') {
            return ['success' => true, 'message' => 'No API to test.'];
        }

        // Create API provider instance
        // If API key is provided, use it directly instead of from database
        if (!empty($apiKey)) {
            $createSettings = [
                'api_key' => $apiKey,
                'selected_model' => null,
                'context_window' => 8096,
                'settings' => []
            ];
            if ($provider === 'openai_compatible') {
                $createSettings['base_url'] = $baseUrl;
            }
            $apiProvider = ApiProviderFactory::create($provider, $createSettings);
        } else {
            // Use existing database configuration
            $apiProvider = ApiProviderFactory::createFromDatabase($provider, $baseUrl);
        }

        if (!$apiProvider) {
            return ['success' => false, 'message' => 'Provider not found or configuration is invalid.'];
        }

        // Test connection using the provider service
        $result = $apiProvider->testConnection();

        // Add more detailed information to the response
        if (isset($result['details'])) {
            // Log detailed error information for debugging
            error_log('API Test Connection Details: ' . json_encode($result['details']));
        }

        return [
            'success' => $result['success'],
            'message' => $result['message']
        ];
    }



    /**
     * API endpoint to fetch models for a provider
     */
    public function getModelsApi(): void
    {
        // Auth check should happen in router

        $provider = $_GET['provider'] ?? '';
        if (empty($provider)) {
            $this->jsonResponse(['success' => false, 'message' => 'Provider is required.'], 400);
            return;
        }

        // Skip for 'none' provider
        if ($provider === 'none') {
            $this->jsonResponse(['success' => true, 'models' => []]);
            return;
        }

        $baseUrl = $_GET['base_url'] ?? null;
        $apiKey = $_GET['api_key'] ?? null;

        // For OpenAI Compatible provider, we need both API key and base URL
        if ($provider === 'openai_compatible') {
            if (empty($apiKey)) {
                $this->jsonResponse(['success' => false, 'message' => 'API key is required for OpenAI Compatible provider.'], 400);
                return;
            }
            if (empty($baseUrl)) {
                $this->jsonResponse(['success' => false, 'message' => 'Base URL is required for OpenAI Compatible provider.'], 400);
                return;
            }
        }

        // Create API provider instance
        // If API key is provided, use it directly instead of from database
        if (!empty($apiKey)) {
            // Handle special case where frontend sends 'USE_STORED_KEY' (use stored DB configuration)
            if ($apiKey === 'USE_STORED_KEY') {
                $apiProvider = ApiProviderFactory::createFromDatabase($provider, $baseUrl);
                if (!$apiProvider) {
                    $this->jsonResponse(['success' => false, 'message' => 'No stored configuration found for provider.'], 400);
                    return;
                }
            } else {
                // Build settings for direct instantiation
                $createSettings = [
                    'api_key' => $apiKey,
                    'selected_model' => null,
                    'context_window' => 8096,
                    'settings' => []
                ];
                if ($provider === 'openai_compatible') {
                    $createSettings['base_url'] = $baseUrl;
                }
                $apiProvider = ApiProviderFactory::create($provider, $createSettings);
            }
        } else {
            // Use existing database configuration
            $apiProvider = ApiProviderFactory::createFromDatabase($provider, $baseUrl);
        }

        if (!$apiProvider) {
            $this->jsonResponse(['success' => false, 'message' => 'Provider not found or configuration is invalid.'], 404);
            return;
        }

        // Fetch models using the provider service
        $result = $apiProvider->getModels();

        if (!$result['success']) {
            // Log detailed error information for debugging if available
            if (isset($result['details'])) {
                error_log('API Get Models Error Details: ' . json_encode($result['details']));
            }

            $this->jsonResponse(['success' => false, 'message' => $result['message']], 500);
            return;
        }

        $this->jsonResponse(['success' => true, 'models' => $result['models']]);
    }

    /**
     * API endpoint to test connection
     */
    public function testConnectionApi(): void
    {
        // Auth check should happen in router

        $provider = $_GET['provider'] ?? '';
        if (empty($provider)) {
            $this->jsonResponse(['success' => false, 'message' => 'Provider is required.'], 400);
            return;
        }

        $baseUrl = $_GET['base_url'] ?? null;
        $apiKey = $_GET['api_key'] ?? null;

        // For OpenAI Compatible provider, we need both API key and base URL
        if ($provider === 'openai_compatible') {
            if (empty($apiKey)) {
                $this->jsonResponse(['success' => false, 'message' => 'API key is required for OpenAI Compatible provider.'], 400);
                return;
            }
            if (empty($baseUrl)) {
                $this->jsonResponse(['success' => false, 'message' => 'Base URL is required for OpenAI Compatible provider.'], 400);
                return;
            }
        }

        // Handle special case where frontend sends 'USE_STORED_KEY'
        $actualApiKey = $apiKey;
        if ($apiKey === 'USE_STORED_KEY') {
            $actualApiKey = null; // Let testConnection use database config
        }

        $result = $this->testConnection($provider, $baseUrl, $actualApiKey);

        if ($result['success']) {
            // Mark as verified if test was successful
            $this->apiSettingsModel->update($provider, ['is_verified' => true]);
        }

        $this->jsonResponse($result);
    }

    /**
     * Handle JSON response helper.
     */
    private function jsonResponse(array $data, int $statusCode = 200): void
    {
        header('Content-Type: application/json');
        http_response_code($statusCode);
        echo json_encode($data);
        exit;
    }
}
