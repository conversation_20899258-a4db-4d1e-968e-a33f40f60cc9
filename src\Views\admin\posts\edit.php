<?php
// View data: $title, $post, $errors (optional), $old_input (optional), $categories
$errors = $errors ?? [];
$old_input = $old_input ?? ($post ? $post : []);
$categories = $categories ?? [];
?>

<div class="admin-form-container">
    <div class="admin-card">
        <div class="admin-card-header">
            <h2 class="admin-card-title">Edit Post: <?= htmlspecialchars($post['title']) ?></h2>
            <div class="admin-card-actions">
                <a href="/admin/posts/version-history/<?= (int)$post['id'] ?>" class="admin-btn admin-btn-secondary">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M3 3v5h5"></path>
                        <path d="M3 3l7 7"></path>
                        <path d="M21 21v-5h-5"></path>
                        <path d="M21 21l-7-7"></path>
                    </svg>
                    Version History
                </a>
                <a href="/admin/posts/collaborative-edit/<?= (int)$post['id'] ?>" class="admin-btn admin-btn-secondary">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M17 3a2.85 2.85 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5L17 3z"></path>
                    </svg>
                    Collaborative Edit
                </a>
            </div>
        </div>
        
        <div class="admin-card-body">
            <form action="/admin/posts/update/<?= (int)$post['id'] ?>" method="POST" id="post-form">
                <input type="hidden" name="csrf_token" value="<?= Brenzley\Core\Session::getCsrfToken() ?>">
                
                <div class="admin-form-group">
                    <label for="title" class="admin-form-label">Title</label>
                    <input type="text" id="title" name="title" class="admin-form-input" 
                           value="<?= htmlspecialchars($old_input['title'] ?? '') ?>" required>
                    <?php if (isset($errors['title'])): ?>
                        <div class="admin-form-error"><?= htmlspecialchars($errors['title']) ?></div>
                    <?php endif; ?>
                </div>
                
                <div class="admin-form-group">
                    <label for="slug" class="admin-form-label">Slug</label>
                    <input type="text" id="slug" name="slug" class="admin-form-input" 
                           value="<?= htmlspecialchars($old_input['slug'] ?? '') ?>" required>
                    <div class="admin-form-hint">URL-friendly version of the title (e.g., my-first-post)</div>
                    <?php if (isset($errors['slug'])): ?>
                        <div class="admin-form-error"><?= htmlspecialchars($errors['slug']) ?></div>
                    <?php endif; ?>
                </div>
                
                <div class="admin-form-group">
                    <label for="content" class="admin-form-label">Content</label>
                    <textarea id="content" name="content" class="admin-form-textarea" rows="15" required><?= htmlspecialchars($old_input['content'] ?? '') ?></textarea>
                    <?php if (isset($errors['content'])): ?>
                        <div class="admin-form-error"><?= htmlspecialchars($errors['content']) ?></div>
                    <?php endif; ?>
                </div>
                
                <div class="admin-form-row">
                    <div class="admin-form-group admin-form-col">
                        <label for="category_id" class="admin-form-label">Category</label>
                        <select id="category_id" name="category_id" class="admin-form-select">
                            <?php foreach ($categories as $category): ?>
                                <option value="<?= (int)$category['id'] ?>" <?= (isset($old_input['category_id']) && $old_input['category_id'] == $category['id']) ? 'selected' : '' ?>>
                                    <?= htmlspecialchars($category['name']) ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                        <?php if (isset($errors['category_id'])): ?>
                            <div class="admin-form-error"><?= htmlspecialchars($errors['category_id']) ?></div>
                        <?php endif; ?>
                    </div>
                    
                    <div class="admin-form-group admin-form-col">
                        <label for="status" class="admin-form-label">Status</label>
                        <select id="status" name="status" class="admin-form-select">
                            <option value="draft" <?= (isset($old_input['status']) && $old_input['status'] == 'draft') ? 'selected' : '' ?>>Draft</option>
                            <option value="published" disabled>Published (functionality to be implemented)</option>
                        </select>
                        <?php if (isset($errors['status'])): ?>
                            <div class="admin-form-error"><?= htmlspecialchars($errors['status']) ?></div>
                        <?php endif; ?>
                    </div>
                </div>
                
                <div class="admin-form-group">
                    <label for="meta_title" class="admin-form-label">Meta Title</label>
                    <input type="text" id="meta_title" name="meta_title" class="admin-form-input" 
                           value="<?= htmlspecialchars($old_input['meta_title'] ?? '') ?>">
                    <div class="admin-form-hint">Leave empty to use the post title</div>
                    <?php if (isset($errors['meta_title'])): ?>
                        <div class="admin-form-error"><?= htmlspecialchars($errors['meta_title']) ?></div>
                    <?php endif; ?>
                </div>
                
                <div class="admin-form-group">
                    <label for="meta_description" class="admin-form-label">Meta Description</label>
                    <textarea id="meta_description" name="meta_description" class="admin-form-textarea" rows="3"><?= htmlspecialchars($old_input['meta_description'] ?? '') ?></textarea>
                    <div class="admin-form-hint">Brief description for search engines (recommended: 150-160 characters)</div>
                    <?php if (isset($errors['meta_description'])): ?>
                        <div class="admin-form-error"><?= htmlspecialchars($errors['meta_description']) ?></div>
                    <?php endif; ?>
                </div>
                
                <div class="admin-form-group">
                    <label for="focus_keywords" class="admin-form-label">Focus Keywords</label>
                    <input type="text" id="focus_keywords" name="focus_keywords" class="admin-form-input" 
                           value="<?= htmlspecialchars($old_input['focus_keywords'] ?? '') ?>">
                    <div class="admin-form-hint">Comma-separated keywords for SEO</div>
                    <?php if (isset($errors['focus_keywords'])): ?>
                        <div class="admin-form-error"><?= htmlspecialchars($errors['focus_keywords']) ?></div>
                    <?php endif; ?>
                </div>
                
                <div class="admin-form-actions">
                    <button type="submit" class="admin-btn admin-btn-primary">Update Post</button>
                    <a href="/admin/posts" class="admin-btn admin-btn-secondary">Cancel</a>
                </div>
            </form>
        </div>
    </div>
</div>

<script src="/assets/js/pages/admin-post-editor.js" defer></script>
