<?php
// View data: $title, $post, $errors (optional), $old_input (optional), $categories, $currentUser
$errors = $errors ?? [];
$old_input = $old_input ?? ($post ? $post : []);
$categories = $categories ?? [];
$currentUser = $currentUser ?? [];
?>

<div class="admin-form-container">
    <div class="admin-card">
        <div class="admin-card-header">
            <h2 class="admin-card-title">Collaborative Editing: <?= htmlspecialchars($post['title'] ?? 'New Post') ?></h2>
        </div>
        
        <div class="admin-card-body">
            <!-- Collaboration Toolbar -->
            <div class="collaboration-toolbar">
                <div class="collaboration-status">
                    <span class="status-indicator connecting"></span>
                    <span id="connection-status">Connecting...</span>
                </div>
                
                <div class="collaboration-actions">
                    <button id="toggle-sidebar-btn" class="collaboration-button">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M17 3a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h10z"></path>
                            <line x1="12" y1="3" x2="12" y2="21"></line>
                        </svg>
                        Toggle Sidebar
                    </button>
                    
                    <button id="save-draft-btn" class="collaboration-button">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z"></path>
                            <polyline points="17 21 17 13 7 13 7 21"></polyline>
                            <polyline points="7 3 7 8 15 8"></polyline>
                        </svg>
                        Save Draft
                    </button>
                    
                    <button id="publish-btn" class="collaboration-button primary" disabled style="opacity: 0.6; cursor: not-allowed; pointer-events: none;">
                        Publish functionality to be implemented
                    </button>
                </div>
            </div>
            
            <!-- Multi-User Editor Container -->
            <div id="multi-user-editor" class="multi-user-editor-container">
                <div class="multi-user-editor-layout">
                    <div class="editor-main">
                        <!-- Editor Toolbar -->
                        <div class="editor-toolbar">
                            <div class="editor-toolbar-group">
                                <button class="editor-toolbar-button" data-format="heading" title="Heading">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <path d="M6 12h12"></path>
                                        <path d="M6 4h12"></path>
                                        <path d="M9 20h6"></path>
                                    </svg>
                                </button>
                                
                                <button class="editor-toolbar-button" data-format="bold" title="Bold">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <path d="M6 4h8a4 4 0 0 1 4 4 4 4 0 0 1-4 4H6z"></path>
                                        <path d="M6 12h9a4 4 0 0 1 4 4 4 4 0 0 1-4 4H6z"></path>
                                    </svg>
                                </button>
                                
                                <button class="editor-toolbar-button" data-format="italic" title="Italic">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <line x1="19" y1="4" x2="10" y2="4"></line>
                                        <line x1="14" y1="20" x2="5" y2="20"></line>
                                        <line x1="15" y1="4" x2="9" y2="20"></line>
                                    </svg>
                                </button>
                                
                                <button class="editor-toolbar-button" data-format="underline" title="Underline">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <path d="M6 3v7a6 6 0 0 0 6 6 6 6 0 0 0 6-6V3"></path>
                                        <line x1="4" y1="21" x2="20" y2="21"></line>
                                    </svg>
                                </button>
                            </div>
                            
                            <div class="editor-toolbar-separator"></div>
                            
                            <div class="editor-toolbar-group">
                                <button class="editor-toolbar-button" data-format="list-ul" title="Bullet List">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <line x1="8" y1="6" x2="21" y2="6"></line>
                                        <line x1="8" y1="12" x2="21" y2="12"></line>
                                        <line x1="8" y1="18" x2="21" y2="18"></line>
                                        <line x1="3" y1="6" x2="3.01" y2="6"></line>
                                        <line x1="3" y1="12" x2="3.01" y2="12"></line>
                                        <line x1="3" y1="18" x2="3.01" y2="18"></line>
                                    </svg>
                                </button>
                                
                                <button class="editor-toolbar-button" data-format="list-ol" title="Numbered List">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <line x1="10" y1="6" x2="21" y2="6"></line>
                                        <line x1="10" y1="12" x2="21" y2="12"></line>
                                        <line x1="10" y1="18" x2="21" y2="18"></line>
                                        <path d="M4 6h1v4"></path>
                                        <path d="M4 10h2"></path>
                                        <path d="M6 18H4c0-1 2-2 2-3s-1-1.5-2-1"></path>
                                    </svg>
                                </button>
                                
                                <button class="editor-toolbar-button" data-format="link" title="Link">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <path d="M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71"></path>
                                        <path d="M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71"></path>
                                    </svg>
                                </button>
                            </div>
                        </div>
                        
                        <!-- Editor Content -->
                        <div class="editor-content-wrapper">
                            <div id="editor-title" class="editor-title" contenteditable="true" placeholder="Enter title..."><?= htmlspecialchars($post['title'] ?? '') ?></div>
                            <div id="editor-content" class="editor-content" contenteditable="true" placeholder="Start writing your content..."><?= $post['content'] ?? '' ?></div>
                        </div>
                    </div>
                    
                    <!-- Editor Sidebar -->
                    <div class="editor-sidebar">
                        <!-- User Presence Panel -->
                        <div id="user-presence-panel" class="user-presence-panel"></div>
                        
                        <!-- Post Settings -->
                        <div class="post-settings-panel">
                            <div class="panel-header">
                                <h3>Post Settings</h3>
                            </div>
                            
                            <div class="panel-content">
                                <div class="form-group">
                                    <label for="category">Category</label>
                                    <select id="category" name="category_id" class="admin-form-select">
                                        <?php foreach ($categories as $category): ?>
                                            <option value="<?= (int)$category['id'] ?>" <?= (isset($post['category_id']) && $post['category_id'] == $category['id']) ? 'selected' : '' ?>>
                                                <?= htmlspecialchars($category['name']) ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                
                                <div class="form-group">
                                    <label for="status">Status</label>
                                    <select id="status" name="status" class="admin-form-select">
                                        <option value="draft" <?= (isset($post['status']) && $post['status'] == 'draft') ? 'selected' : '' ?>>Draft</option>
                                        <option value="published" disabled>Published (functionality to be implemented)</option>
                                    </select>
                                </div>
                                
                                <div class="form-group">
                                    <label for="featured-image">Featured Image</label>
                                    <div id="featured-image-preview" class="featured-image-preview-container">
                                        <?php if (isset($post['featured_image']) && $post['featured_image']): ?>
                                            <div class="featured-image-preview">
                                                <img src="<?= htmlspecialchars($post['featured_image']['url']) ?>" alt="Featured image">
                                                <div class="featured-image-info">
                                                    <span class="featured-image-name"><?= htmlspecialchars($post['featured_image']['filename']) ?></span>
                                                    <button class="remove-featured-image-btn" title="Remove featured image">×</button>
                                                </div>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                    <button id="select-featured-image-btn" class="admin-btn admin-btn-secondary">Select Featured Image</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Hidden form for submission -->
            <form id="post-form" action="<?= isset($post['id']) ? '/admin/posts/update/' . (int)$post['id'] : '/admin/posts/store' ?>" method="POST" style="display: none;">
                <input type="hidden" name="csrf_token" value="<?= \Brenzley\Core\Session::getCsrfToken() ?>">
                <input type="hidden" name="title" id="hidden-title" value="<?= htmlspecialchars($post['title'] ?? '') ?>">
                <input type="hidden" name="content" id="hidden-content" value="<?= htmlspecialchars($post['content'] ?? '') ?>">
                <input type="hidden" name="status" id="hidden-status" value="<?= htmlspecialchars($post['status'] ?? 'draft') ?>">
                <input type="hidden" name="category_id" id="hidden-category-id" value="<?= (int)($post['category_id'] ?? 1) ?>">
                <input type="hidden" name="featured_image_id" id="hidden-featured-image" value="<?= (int)($post['featured_image_id'] ?? 0) ?>">
            </form>
        </div>
    </div>
</div>

<!-- Include the Multi-User Editor CSS and JavaScript -->
<link rel="stylesheet" href="/assets/css/components/multi-user-editor.css">
<script src="/assets/js/components/multi-user-editor.js" defer></script>

<!-- Include the Collaborative Editor JavaScript -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize the multi-user editor
        const editor = new MultiUserEditor({
            containerSelector: '#multi-user-editor',
            editorSelector: '#editor-content',
            presenceSelector: '#user-presence-panel',
            documentId: <?= (int)($post['id'] ?? 0) ?>,
            currentUser: {
                id: <?= (int)($currentUser['id'] ?? 0) ?>,
                username: '<?= htmlspecialchars($currentUser['username'] ?? 'Anonymous') ?>',
                avatar: '<?= htmlspecialchars($currentUser['avatar'] ?? '') ?>'
            },
            apiEndpoints: {
                connect: '/api/admin/collaboration/connect',
                sync: '/api/admin/collaboration/sync',
                presence: '/api/admin/collaboration/presence'
            },
            onUserJoin: function(user) {
                console.log('User joined:', user);
                showNotification(`${user.username} joined the document`, 'info');
            },
            onUserLeave: function(user) {
                console.log('User left:', user);
                showNotification(`${user.username} left the document`, 'info');
            },
            onContentChange: function(change, userId) {
                console.log('Content changed by user:', userId, change);
            }
        });
        
        // Connection status updates
        const connectionStatus = document.getElementById('connection-status');
        const statusIndicator = document.querySelector('.status-indicator');
        
        function updateConnectionStatus(status) {
            if (status === 'connected') {
                connectionStatus.textContent = 'Connected';
                statusIndicator.className = 'status-indicator connected';
            } else if (status === 'connecting') {
                connectionStatus.textContent = 'Connecting...';
                statusIndicator.className = 'status-indicator connecting';
            } else {
                connectionStatus.textContent = 'Disconnected';
                statusIndicator.className = 'status-indicator disconnected';
            }
        }
        
        // Toggle sidebar
        const toggleSidebarBtn = document.getElementById('toggle-sidebar-btn');
        const editorSidebar = document.querySelector('.editor-sidebar');
        
        toggleSidebarBtn.addEventListener('click', function() {
            editorSidebar.classList.toggle('collapsed');
        });
        
        // Save and publish buttons
        const saveDraftBtn = document.getElementById('save-draft-btn');
        const publishBtn = document.getElementById('publish-btn');
        const postForm = document.getElementById('post-form');
        const hiddenTitle = document.getElementById('hidden-title');
        const hiddenContent = document.getElementById('hidden-content');
        const hiddenStatus = document.getElementById('hidden-status');
        const hiddenCategoryId = document.getElementById('hidden-category-id');
        
        saveDraftBtn.addEventListener('click', function() {
            savePost('draft');
        });
        
        // Publish button is completely disabled - no click handler needed
        
        function savePost(status) {
            // Only allow draft saving
            if (status !== 'draft') {
                console.warn('Publish functionality has been disabled');
                return;
            }
            
            // Update hidden fields
            hiddenTitle.value = document.getElementById('editor-title').textContent;
            hiddenContent.value = document.getElementById('editor-content').innerHTML;
            hiddenStatus.value = 'draft'; // Force draft status
            hiddenCategoryId.value = document.getElementById('category').value;
            
            // Submit form
            postForm.submit();
        }
        
        // Notification function
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `notification notification-${type}`;
            notification.textContent = message;
            
            document.body.appendChild(notification);
            
            // Show notification
            setTimeout(() => {
                notification.classList.add('show');
            }, 10);
            
            // Hide after 3 seconds
            setTimeout(() => {
                notification.classList.remove('show');
                
                // Remove from DOM after animation
                setTimeout(() => {
                    notification.remove();
                }, 300);
            }, 3000);
        }
    });
</script>

<style>
    /* Notification Styles */
    .notification {
        position: fixed;
        bottom: 20px;
        right: 20px;
        padding: 12px 20px;
        border-radius: 4px;
        background-color: #333;
        color: white;
        font-size: 14px;
        z-index: 1000;
        opacity: 0;
        transform: translateY(20px);
        transition: opacity 0.3s, transform 0.3s;
        max-width: 300px;
    }
    
    .notification.show {
        opacity: 1;
        transform: translateY(0);
    }
    
    .notification-info {
        background-color: #2196F3;
    }
    
    .notification-success {
        background-color: #4CAF50;
    }
    
    .notification-warning {
        background-color: #FF9800;
    }
    
    .notification-error {
        background-color: #F44336;
    }
    
    /* Editor Title Styles */
    .editor-title {
        font-size: 24px;
        font-weight: 600;
        padding: 1rem;
        border-bottom: 1px solid var(--admin-border);
        outline: none;
    }
    
    .editor-title:empty:before {
        content: attr(placeholder);
        color: var(--admin-text-tertiary);
    }
    
    .editor-content:empty:before {
        content: attr(placeholder);
        color: var(--admin-text-tertiary);
    }
    
    /* Sidebar Collapsed State */
    .editor-sidebar.collapsed {
        width: 0;
        padding: 0;
        overflow: hidden;
        border-left: none;
    }
    
    /* Post Settings Panel */
    .post-settings-panel {
        border-top: 1px solid var(--admin-border);
        padding: 0.75rem;
    }
    
    .panel-header {
        margin-bottom: 0.75rem;
    }
    
    .panel-header h3 {
        font-size: 0.875rem;
        font-weight: 600;
        color: var(--admin-text-secondary);
        margin: 0;
    }
    
    .panel-content {
        display: flex;
        flex-direction: column;
        gap: 0.75rem;
    }
    
    .form-group {
        display: flex;
        flex-direction: column;
        gap: 0.25rem;
    }
    
    .form-group label {
        font-size: 0.75rem;
        font-weight: 500;
        color: var(--admin-text-secondary);
    }
</style>
