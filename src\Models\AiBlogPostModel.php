<?php

declare(strict_types=1);

namespace Brenzley\Models;

use <PERSON><PERSON>zley\Core\Database;
use PDO;

/**
 * Model for AI-generated blog posts (full content)
 */
class AiBlogPostModel
{
    private PDO $db;

    public function __construct()
    {
        $this->db = Database::getInstance();
    }

    /**
     * Create a new AI-generated blog post
     *
     * @param array $data Blog post data
     * @return int|false The ID of the created blog post or false on failure
     */
    public function create(array $data): int|false
    {
        // Convert parameters array to JSON if needed
        if (isset($data['parameters']) && is_array($data['parameters'])) {
            $data['parameters'] = json_encode($data['parameters']);
        } elseif (!isset($data['parameters'])) {
            $data['parameters'] = null; // Ensure it's null if not provided
        }

        // Discover available columns to build a compatible INSERT for any existing schema
        $availableCols = $this->getTableColumns('ai_blog_posts');
        if (empty($availableCols)) {
            // Fallback to previous behavior if schema discovery fails
            $availableCols = ['user_id','blog_idea_id','outline_id','title','content_html','provider','model','parameters'];
        }

        // Map our canonical fields to potential legacy names
        $fieldMap = [];
        $params = [];

        // user_id
        if (in_array('user_id', $availableCols, true)) {
            $fieldMap['user_id'] = ':user_id';
            $params[':user_id'] = $data['user_id'];
        }
        // outline id may be 'outline_id' or legacy 'blog_outline_id'
        if (in_array('outline_id', $availableCols, true)) {
            $fieldMap['outline_id'] = ':outline_id';
            $params[':outline_id'] = $data['outline_id'] ?? ($data['blog_outline_id'] ?? null);
        } elseif (in_array('blog_outline_id', $availableCols, true)) {
            $fieldMap['blog_outline_id'] = ':outline_id';
            $params[':outline_id'] = $data['outline_id'] ?? ($data['blog_outline_id'] ?? null);
        }
        // idea id (optional)
        if (in_array('blog_idea_id', $availableCols, true)) {
            $fieldMap['blog_idea_id'] = ':blog_idea_id';
            $params[':blog_idea_id'] = $data['blog_idea_id'] ?? null;
        }
        // title
        if (in_array('title', $availableCols, true)) {
            $fieldMap['title'] = ':title';
            $params[':title'] = $data['title'];
        }
        // content: handle both content and content_html fields
        if (in_array('content', $availableCols, true)) {
            $fieldMap['content'] = ':content';
            $params[':content'] = $data['content'] ?? ($data['content_html'] ?? null);
        }
        if (in_array('content_html', $availableCols, true)) {
            $fieldMap['content_html'] = ':content_html';
            $params[':content_html'] = $data['content_html'] ?? ($data['content'] ?? null);
        }
        // provider
        if (in_array('provider', $availableCols, true)) {
            $fieldMap['provider'] = ':provider';
            $params[':provider'] = $data['provider'] ?? null;
        }
        // model
        if (in_array('model', $availableCols, true)) {
            $fieldMap['model'] = ':model';
            $params[':model'] = $data['model'] ?? null;
        }
        // parameters (json/text)
        if (in_array('parameters', $availableCols, true)) {
            $fieldMap['parameters'] = ':parameters';
            $params[':parameters'] = $data['parameters'];
        }

        if (empty($fieldMap)) {
            return false; // No columns to insert
        }

        $columnsSql = implode(', ', array_keys($fieldMap));
        $placeholdersSql = implode(', ', array_values($fieldMap));
        $sql = "INSERT INTO ai_blog_posts ($columnsSql) VALUES ($placeholdersSql)";

        $stmt = $this->db->prepare($sql);
        $success = $stmt->execute($params);

        if ($success) {
            return (int)$this->db->lastInsertId();
        }

        return false;
    }

    private function getTableColumns(string $table): array
    {
        static $cache = [];
        if (isset($cache[$table])) {
            return $cache[$table];
        }
        try {
            $stmt = $this->db->prepare("SHOW COLUMNS FROM `{$table}`");
            $stmt->execute();
            $cols = $stmt->fetchAll(PDO::FETCH_COLUMN, 0);
            if (is_array($cols)) {
                $cache[$table] = $cols;
                return $cols;
            }
        } catch (\Throwable $e) {
            // Ignore and fallback
        }
        return [];
    }

    /**
     * Find a blog post by ID
     *
     * @param int $id Blog post ID
     * @return array|false Blog post data or false if not found
     */
    public function findById(int $id): array|false
    {
        $sql = "SELECT * FROM ai_blog_posts WHERE id = :id";
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':id', $id, PDO::PARAM_INT);
        $stmt->execute();

        $post = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($post && isset($post['parameters']) && !empty($post['parameters'])) {
            $post['parameters'] = json_decode($post['parameters'], true);
        }
        
        return $post;
    }

    /**
     * Find all blog posts for a user
     *
     * @param int $userId User ID
     * @param int $limit Maximum number of results to return
     * @param int $offset Offset for pagination
     * @return array Blog posts
     */
    public function findByUserId(int $userId, int $limit = 10, int $offset = 0): array
    {
        $sql = "SELECT id, title, provider, model, created_at FROM ai_blog_posts 
                WHERE user_id = :user_id 
                ORDER BY created_at DESC 
                LIMIT :limit OFFSET :offset";
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Update a blog post
     *
     * @param int $id Blog post ID
     * @param array $data Updated blog post data
     * @return bool Success status
     */
    public function update(int $id, array $data): bool
    {
        $sql = "UPDATE ai_blog_posts SET ";
        $params = [];
        
        foreach ($data as $key => $value) {
            if (in_array($key, ['id', 'user_id', 'created_at', 'updated_at'])) { // Non-updatable fields
                continue;
            }
            
            if ($key === 'parameters' && is_array($value)) {
                $value = json_encode($value);
            }
            
            $sql .= "$key = :$key, ";
            $params[":$key"] = $value;
        }
        
        $sql = rtrim($sql, ', '); // Remove trailing comma
        $sql .= ", updated_at = NOW() WHERE id = :id"; // Add updated_at and WHERE clause
        $params[':id'] = $id;
        
        if (count($params) === 1) { // Only :id, means no actual fields to update
            return true; // Or false, depending on desired behavior for no-op updates
        }

        $stmt = $this->db->prepare($sql);
        return $stmt->execute($params);
    }

    /**
     * Delete a blog post
     *
     * @param int $id Blog post ID
     * @return bool Success status
     */
    public function delete(int $id): bool
    {
        $sql = "DELETE FROM ai_blog_posts WHERE id = :id";
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':id', $id, PDO::PARAM_INT);
        return $stmt->execute();
    }

    /**
     * Count total blog posts for a user
     *
     * @param int $userId User ID
     * @return int Total count
     */
    public function countByUserId(int $userId): int
    {
        $sql = "SELECT COUNT(*) FROM ai_blog_posts WHERE user_id = :user_id";
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
        $stmt->execute();
        return (int)$stmt->fetchColumn();
    }
}
