<?php

use Phinx\Migration\AbstractMigration;

class AddAiGenerationMetadataToPosts extends AbstractMigration
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-change-method
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function change(): void
    {
        $table = $this->table('posts');
        
        // Add ai_generation_metadata column if it doesn't exist
        if (!$table->hasColumn('ai_generation_metadata')) {
            $table->addColumn('ai_generation_metadata', 'text', [
                'null' => true,
                'comment' => 'JSON metadata about AI generation process'
            ]);
        }
        
        $table->update();
    }
}