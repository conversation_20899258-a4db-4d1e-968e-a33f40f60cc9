/**
 * Manages the state for the AI Article Creator.
 */
export class StateManager {
    constructor() {
        this.state = {
            // Process state
            currentStep: 'idea', // idea, outline, content, seo
            isDirty: false,

            // Content state
            title: '',
            content: '',
            aiBlogPostId: null,
            metaTitle: '',
            metaDescription: '',
            focusKeyword: '',
            secondaryKeywords: [],
            featuredImage: null,

            // Idea state
            selectedIdea: null,
            ideaPreset: null,

            // Outline state
            outline: [],
            outlineId: null,

            // SEO state
            seoScore: 0,
            readabilityScore: 0,
            structureScore: 0,
            seoCompleted: false,

            // AI Provider state
            aiProvider: {
                active: 'openai',
                status: 'active', // active, inactive, error
                providers: {
                    openai: {
                        status: 'active',
                        models: [
                            { id: 'gpt-4o', name: 'GPT-4o', contextWindow: 128000 },
                            { id: 'gpt-4-turbo', name: 'GPT-4 Turbo', contextWindow: 128000 },
                            { id: 'gpt-3.5-turbo', name: 'GPT-3.5 Turbo', contextWindow: 16000 }
                        ],
                        activeModel: 'gpt-4o'
                    },
                    anthropic: {
                        status: 'active',
                        models: [
                            { id: 'claude-3-opus', name: 'Claude 3 Opus', contextWindow: 200000 },
                            { id: 'claude-3-sonnet', name: 'Claude 3 Sonnet', contextWindow: 180000 },
                            { id: 'claude-3-haiku', name: 'Claude 3 Haiku', contextWindow: 150000 }
                        ],
                        activeModel: 'claude-3-opus'
                    }
                }
            },

            // Chat state
            chatMessages: [
                {
                    role: 'assistant',
                    content: 'Hello! I\'m your AI writing assistant. I\'ll help you create a high-quality article step by step. Let\'s start by generating some topic ideas. Please fill in the form above and click "Generate Ideas".'
                }
            ]
        };
    }

    /**
     * Get the current state.
     * @returns {Object} The current state.
     */
    getState() {
        return this.state;
    }

    /**
     * Update a specific part of the state.
     * @param {string} key - The key of the state property to update.
     * @param {*} value - The new value.
     */
    updateState(key, value) {
        if (this.state.hasOwnProperty(key)) {
            this.state[key] = value;
            if (key !== 'isDirty' && key !== 'currentStep' && key !== 'chatMessages' && key !== 'seoScore' && key !== 'readabilityScore' && key !== 'structureScore') {
                this.state.isDirty = true; // Mark as dirty if a relevant field changes
            }
        } else {
            console.warn(`StateManager: Key "${key}" not found in state.`);
        }
    }

    /**
     * Get a specific value from the state.
     * @param {string} key - The key of the state property to get.
     * @returns {*} The value of the state property, or undefined if not found.
     */
    getStateValue(key) {
        return this.state[key];
    }

    /**
     * Set the 'isDirty' flag.
     * @param {boolean} dirty - The new value for isDirty.
     */
    setDirty(dirty) {
        this.state.isDirty = dirty;
    }

    /**
     * Add a chat message to the state.
     * @param {string} role - 'user' or 'assistant'.
     * @param {string} content - The message content.
     */
    addChatMessage(role, content) {
        this.state.chatMessages.push({ role, content });
    }

    /**
     * Get all chat messages.
     * @returns {Array} The chat messages.
     */
    getChatMessages() {
        return this.state.chatMessages;
    }

    /**
     * Set the selected idea and its preset.
     * @param {Object} idea - The selected idea object.
     */
    setSelectedIdea(idea) {
        this.state.selectedIdea = idea;
        this.state.ideaPreset = idea ? idea.preset : null;
        this.state.isDirty = true;
    }

    /**
     * Set the article outline.
     * @param {Array} outline - The article outline.
     */
    setOutline(outline) {
        this.state.outline = outline;
        this.state.isDirty = true;
    }

    /**
     * Set the current step.
     * @param {string} step - The new current step.
     */
    setCurrentStep(step) {
        this.state.currentStep = step;
    }
}