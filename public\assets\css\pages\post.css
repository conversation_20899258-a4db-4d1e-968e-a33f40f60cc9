/* Enhanced Post Display Styles */
/* Clearfix to ensure floated images don't overflow parent */
.post-content-inner::after { content: ""; display: table; clear: both; }


/* Import image layout styles from AI editor */
@import url('./admin-ai-editor-css/image-layouts.css');

.post-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 2rem 1rem;
    background: var(--color-surface, #ffffff);
    min-height: 100vh;
}

/* Post Header */
.post-header {
    margin-bottom: 3rem;
    padding-bottom: 2rem;
    border-bottom: 1px solid var(--color-border, #e5e7eb);
}

.post-header-content {
    text-align: center;
}

.post-title {
    font-size: 2.5rem;
    font-weight: 800;
    line-height: 1.2;
    color: var(--color-text-primary, #111827);
    margin: 0 0 1.5rem 0;
    letter-spacing: -0.025em;
}

.post-meta {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
}

.post-meta-primary {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    flex-wrap: wrap;
    justify-content: center;
}

.post-meta-primary > span {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--color-text-secondary, #6b7280);
    font-size: 0.9rem;
    font-weight: 500;
}

.post-meta-primary .icon {
    color: var(--color-primary, #3b82f6);
    flex-shrink: 0;
}

.post-category a {
    color: var(--color-primary, #3b82f6);
    text-decoration: none;
    font-weight: 600;
    transition: color 0.2s ease;
}

.post-category a:hover {
    color: var(--color-primary-dark, #2563eb);
}

.post-actions {
    display: flex;
    gap: 0.75rem;
}

.btn-action {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background: var(--color-surface, #ffffff);
    border: 1px solid var(--color-border, #e5e7eb);
    border-radius: 0.5rem;
    color: var(--color-text-secondary, #6b7280);
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-action:hover {
    background: var(--color-background, #f9fafb);
    border-color: var(--color-primary, #3b82f6);
    color: var(--color-primary, #3b82f6);
}

.btn-action.saved {
    background: var(--color-primary, #3b82f6);
    border-color: var(--color-primary, #3b82f6);
    color: white;
}

.btn-action:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* Post Content */
.post-content {
    margin-bottom: 3rem;
}

.post-content-inner {
    font-size: 1.125rem;
    line-height: 1.8;
    color: var(--color-text-primary, #111827);
}

/* Clear floated images to prevent content overflow */
.post-content-inner::after {
    content: "";
    display: table;
    clear: both;
}

.post-content-inner h1,
.post-content-inner h2,
.post-content-inner h3,
.post-content-inner h4,
.post-content-inner h5,
.post-content-inner h6 {
    font-weight: 700;
    line-height: 1.3;
    margin: 2rem 0 1rem 0;
    color: var(--color-text-primary, #111827);
}

.post-content-inner h1 { font-size: 2rem; }
.post-content-inner h2 { font-size: 1.75rem; }
.post-content-inner h3 { font-size: 1.5rem; }
.post-content-inner h4 { font-size: 1.25rem; }
.post-content-inner h5 { font-size: 1.125rem; }
.post-content-inner h6 { font-size: 1rem; }

.post-content-inner p {
    margin: 1.5rem 0;
}

.post-content-inner ul,
.post-content-inner ol {
    margin: 1.5rem 0;
    padding-left: 2rem;
}

.post-content-inner li {
    margin: 0.5rem 0;
}

.post-content-inner blockquote {
    margin: 2rem 0;
    padding: 1.5rem 2rem;
    background: var(--color-background, #f9fafb);
    border-left: 4px solid var(--color-primary, #3b82f6);
    border-radius: 0 0.5rem 0.5rem 0;
    font-style: italic;
    color: var(--color-text-secondary, #6b7280);
}

.post-content-inner code {
    background: var(--color-background, #f9fafb);
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.875em;
    color: var(--color-primary, #3b82f6);
}

.post-content-inner pre {
    background: var(--color-surface-dark, #1f2937);
    color: #f9fafb;
    padding: 1.5rem;
    border-radius: 0.5rem;
    overflow-x: auto;
    margin: 2rem 0;
}

.post-content-inner pre code {
    background: none;
    padding: 0;
    color: inherit;
}

/* Enhanced Image Styles */
.post-image {
    margin: 2.5rem 0;
    text-align: center;
}

.post-image img {
    max-width: 100%;
    height: auto;
    border-radius: 0.75rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.post-image img:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

/* Image Layout Variants */
.post-image-full {
    margin: 3rem -2rem;
}

.post-image-wide {
    margin: 2.5rem -1rem;
}

.post-image-left {
    float: left;
    margin: 0 2rem 1rem 0;
    max-width: 50%;
}

.post-image-right {
    float: right;
    margin: 0 0 1rem 2rem;
    max-width: 50%;
}

.post-image-center {
    text-align: center;
    margin: 2.5rem auto;
    max-width: 80%;
}

/* Image Placeholder */
/* AI editor placeholder base with improved public styling */
.post-content .ai-image-placeholder {
  position: relative;
  border: 2px dashed rgba(107,114,128,0.35);
  border-radius: 0.75rem;
  background: rgba(249,250,251,0.7);
  margin: 1.5rem auto;
  max-width: 100%;
  overflow: hidden;
}

.post-content .ai-image-placeholder .ai-image-placeholder-inner {
  position: relative;
  min-height: 220px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
}

.post-content .ai-image-placeholder .placeholder-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  color: #6b7280;
}

.post-content .ai-image-placeholder .placeholder-icon {
  font-size: 2rem;
}

.post-content .ai-image-placeholder .placeholder-text {
  font-weight: 600;
}

.post-content .ai-image-placeholder .placeholder-alt {
  font-style: italic;
  color: #9ca3af;
}

/* Better placement for image not found CTA */
.post-content .ai-image-placeholder .ai-upload-button,
.post-content .ai-image-placeholder .ai-show-description-cta,
.post-content .ai-image-placeholder .ai-image-description-container,
.post-content .ai-image-placeholder .ai-image-controls {
  display: none !important; /* hide editor-only UI */
}
.post-image-placeholder {
    margin: 2.5rem 0;
    padding: 3rem 2rem;
    background: var(--color-background, #f9fafb);
    border: 2px dashed var(--color-border, #e5e7eb);
    border-radius: 0.75rem;
    text-align: center;
}

.placeholder-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
    color: var(--color-text-secondary, #6b7280);
}

.placeholder-icon {
    font-size: 3rem;
    opacity: 0.5;
}

.placeholder-icon::before {
    content: '🖼️';
}

.placeholder-text {
    font-size: 1rem;
    font-weight: 500;
}

.placeholder-alt {
    font-size: 0.9rem;
    font-style: italic;
    color: var(--color-text-secondary, #6b7280);
    background: var(--color-surface, #ffffff);
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    border: 1px solid var(--color-border, #e5e7eb);
    margin-top: 0.5rem;
    max-width: 300px;
    line-height: 1.4;
}

/* Post Image Layout Styles */
/* Reuse AI editor layout classes for public view layout fidelity */
.post-content .ai-image-layout-left {
  float: left;
  width: 45%;
  max-width: 320px;
  margin: 0.25rem 1.5rem 1rem 0;
}
.post-content .ai-image-layout-right {
  float: right;
  width: 45%;
  max-width: 320px;
  margin: 0.25rem 0 1rem 1.5rem;
}
.post-content .ai-image-layout-wide {
  width: calc(100% + 4rem);
  margin-left: -2rem;
  margin-right: -2rem;
}
.post-content .ai-image-layout-full {
  width: 100vw;
  margin-left: calc(-50vw + 50%);
  margin-right: calc(-50vw + 50%);
}
.post-content .ai-image-layout-square {
  width: 60%;
  margin: 1.5rem auto;
}
.post-content .ai-image-layout-standard,
.post-content .ai-image-layout-center {
  margin: 2rem auto;
  text-align: center;
}
.post-content .ai-image-layout-text_overlay .image-text-overlay-content {
  position: absolute;
  left: 0; right: 0; bottom: 0;
  padding: 1.25rem 1rem;
  background: linear-gradient(transparent, rgba(0,0,0,0.7));
  color: #fff;
}

/* Gallery reuse */
.post-content .ai-image-gallery { display: grid; gap: 1rem; margin: 1.5rem 0; }
.post-content .ai-image-layout-gallery-2-item .ai-gallery-grid { grid-template-columns: repeat(2,1fr); }
.post-content .ai-image-layout-gallery-3-item .ai-gallery-grid { grid-template-columns: repeat(3,1fr); }
.post-content .ai-gallery-grid .ai-image-placeholder { aspect-ratio: 1/1; }
.post-image-full {
    width: 100vw;
    margin-left: 50%;
    transform: translateX(-50%);
    max-width: none;
}

.post-image-wide {
    width: calc(100% + 4rem);
    margin-left: -2rem;
    margin-right: -2rem;
}

.post-image-left {
    float: left;
    margin: 0 2rem 1rem 0;
    max-width: 50%;
    clear: left;
}

.post-image-right {
    float: right;
    margin: 0 0 1rem 2rem;
    max-width: 50%;
    clear: right;
}

.post-image-center {
    margin: 2rem auto;
    text-align: center;
    max-width: 80%;
}

.post-image-square {
    margin: 2rem auto;
    text-align: center;
    max-width: 400px;
}

.post-image-square img {
    width: 400px;
    height: 400px;
    object-fit: cover;
}

/* Text Overlay Layouts */
.post-image-overlay {
    position: relative;
    margin: 2rem 0;
    border-radius: 0.75rem;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.post-image-overlay .post-image {
    margin: 0;
}

.post-image-overlay img {
    width: 100%;
    height: auto;
    display: block;
}

.image-overlay-content {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
    color: white;
    padding: 2rem 1.5rem 1.5rem;
    font-size: 1rem;
    line-height: 1.6;
}

.image-overlay-content h1,
.image-overlay-content h2,
.image-overlay-content h3,
.image-overlay-content h4,
.image-overlay-content h5,
.image-overlay-content h6 {
    color: white;
    margin: 0 0 0.5rem 0;
}

.image-overlay-content p {
    margin: 0 0 0.75rem 0;
}

.image-overlay-content p:last-child {
    margin-bottom: 0;
}

/* Floated Image Layouts */
.post-image-float-wrapper {
    margin: 2rem 0;
    overflow: hidden; /* Contains floats */
}

.post-image-float-wrapper .post-image {
    margin: 0 0 1rem 0;
}

.post-image-float-left .post-image {
    float: left;
    margin-right: 2rem;
    max-width: 50%;
}

.post-image-float-right .post-image {
    float: right;
    margin-left: 2rem;
    max-width: 50%;
}

.post-image-adjacent-content {
    overflow: hidden; /* Creates new block formatting context */
    line-height: 1.6;
}

.post-image-adjacent-content h1,
.post-image-adjacent-content h2,
.post-image-adjacent-content h3,
.post-image-adjacent-content h4,
.post-image-adjacent-content h5,
.post-image-adjacent-content h6 {
    margin: 0 0 1rem 0;
}

.post-image-adjacent-content p {
    margin: 0 0 1rem 0;
}

.post-image-adjacent-content p:last-child {
    margin-bottom: 0;
}

.clearfix {
    clear: both;
    height: 0;
    font-size: 0;
    line-height: 0;
}

/* Gallery Layouts */
.post-image-gallery {
    display: grid;
    gap: 1rem;
    margin: 2rem 0;
    border-radius: var(--border-radius-lg, 0.5rem);
    overflow: hidden;
}

/* Fix for gallery items */
.post-image-gallery .gallery-item {
    margin: 0;
    padding: 0;
    border: none;
    background: none;
}

.post-image-gallery .gallery-placeholder {
    width: 100%;
    height: 200px;
    background: var(--color-background, #f9fafb);
    border: 2px dashed var(--color-border, #e5e7eb);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.post-image-gallery .gallery-placeholder:hover {
    border-color: var(--color-primary, #3b82f6);
    background: var(--color-primary-light, #eff6ff);
}

.post-image-gallery .placeholder-content {
    text-align: center;
    color: var(--color-text-secondary, #6b7280);
}

.post-image-gallery .placeholder-icon {
    font-size: 2rem;
    display: block;
    margin-bottom: 0.5rem;
}

.post-image-gallery .placeholder-text {
    font-size: 0.875rem;
    font-weight: 500;
}

.post-image-gallery-2-item {
    grid-template-columns: 1fr 1fr;
}

.post-image-gallery-3-item {
    grid-template-columns: 1fr 1fr 1fr;
}

.post-image-gallery-4-item {
    grid-template-columns: 1fr 1fr;
    grid-template-rows: 1fr 1fr;
}

.post-image-overlay {
    position: relative;
    display: grid;
    grid-template-columns: 1fr;
    margin: 2rem 0;
}

.gallery-item {
    margin: 0;
    position: relative;
    overflow: hidden;
    border-radius: var(--border-radius-md, 0.375rem);
    background: var(--color-surface, #ffffff);
}

.gallery-item img {
    width: 100%;
    height: 200px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.gallery-item:hover img {
    transform: scale(1.05);
}

.gallery-placeholder {
    background: var(--color-background, #f9fafb);
    border: 2px dashed var(--color-border, #e5e7eb);
    border-radius: var(--border-radius-md, 0.375rem);
    height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Placeholder Layout Styles */
.post-image-placeholder.post-image-full {
    width: 100vw;
    margin-left: 50%;
    transform: translateX(-50%);
}

.post-image-placeholder.post-image-wide {
    width: calc(100% + 4rem);
    margin-left: -2rem;
    margin-right: -2rem;
}

.post-image-placeholder.post-image-left {
    float: left;
    margin: 0 2rem 1rem 0;
    max-width: 50%;
    width: 300px;
}

.post-image-placeholder.post-image-right {
    float: right;
    margin: 0 0 1rem 2rem;
    max-width: 50%;
    width: 300px;
}

.post-image-placeholder.post-image-center {
    margin: 2rem auto;
    max-width: 400px;
}

/* Post Footer */
.post-footer {
    padding: 2rem 0;
    border-top: 1px solid var(--color-border, #e5e7eb);
    margin-bottom: 3rem;
}

.post-keywords {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.keywords-label {
    font-weight: 600;
    color: var(--color-text-primary, #111827);
    font-size: 0.9rem;
}

.keywords-list {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.keyword-tag {
    display: inline-block;
    padding: 0.375rem 0.75rem;
    background: var(--color-primary, #3b82f6);
    color: white;
    border-radius: 1rem;
    font-size: 0.8rem;
    font-weight: 500;
    text-decoration: none;
    transition: background-color 0.2s ease;
}

.keyword-tag:hover {
    background: var(--color-primary-dark, #2563eb);
}

/* Comments Section */
.comments-section {
    background: var(--color-background, #f9fafb);
    border-radius: 1rem;
    padding: 2rem;
    margin-top: 3rem;
}

.comments-header {
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--color-border, #e5e7eb);
}

.comments-title {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--color-text-primary, #111827);
    margin: 0;
}

.comments-title .icon {
    color: var(--color-primary, #3b82f6);
}

.no-comments {
    text-align: center;
    padding: 3rem 2rem;
    color: var(--color-text-secondary, #6b7280);
}

.no-comments .icon {
    margin-bottom: 1rem;
    opacity: 0.5;
}

.no-comments p {
    font-size: 1.1rem;
    margin: 0;
}

/* Individual Comments */
.comment {
    background: white;
    border-radius: 0.75rem;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    border: 1px solid var(--color-border, #e5e7eb);
    transition: box-shadow 0.2s ease;
}

.comment:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.comment-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.comment-author {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--color-text-primary, #111827);
    font-weight: 600;
}

.comment-author .icon {
    color: var(--color-primary, #3b82f6);
}

.comment-date {
    color: var(--color-text-secondary, #6b7280);
    font-size: 0.875rem;
}

.comment-content {
    color: var(--color-text-primary, #111827);
    line-height: 1.6;
}

/* Comment Form */
.comment-form-section {
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid var(--color-border, #e5e7eb);
}

.comment-form-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--color-text-primary, #111827);
    margin: 0 0 1.5rem 0;
}

.comment-form {
    background: white;
    border-radius: 0.75rem;
    padding: 2rem;
    border: 1px solid var(--color-border, #e5e7eb);
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-bottom: 1rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    font-weight: 600;
    color: var(--color-text-primary, #111827);
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.required {
    color: var(--color-danger, #ef4444);
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 1px solid var(--color-border, #e5e7eb);
    border-radius: 0.5rem;
    font-size: 1rem;
    transition: all 0.2s ease;
    background: var(--color-surface, #ffffff);
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--color-primary, #3b82f6);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
    line-height: 1.6;
}

.form-group small {
    display: block;
    margin-top: 0.25rem;
    color: var(--color-text-secondary, #6b7280);
    font-size: 0.8rem;
}

.error {
    color: var(--color-danger, #ef4444);
    font-size: 0.875rem;
    margin-top: 0.5rem;
}

.form-actions {
    margin-top: 1.5rem;
}

.btn-submit {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.875rem 2rem;
    background: var(--color-primary, #3b82f6);
    color: white;
    border: none;
    border-radius: 0.5rem;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-submit:hover {
    background: var(--color-primary-dark, #2563eb);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.btn-submit:active {
    transform: translateY(0);
}

.btn-submit .icon {
    flex-shrink: 0;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
    .post-container {
        padding: 1rem 0.75rem;
    }
    
    .post-title {
        font-size: 2rem;
    }
    
    .post-meta-primary {
        flex-direction: column;
        gap: 0.75rem;
    }
    
    .post-content-inner {
        font-size: 1rem;
    }
    
    /* Mobile Image Layout Adjustments */
    .post-image-full,
    .post-content .ai-image-layout-full {
        width: calc(100vw - 1.5rem);
        margin-left: -0.75rem;
        margin-right: -0.75rem;
        transform: none;
    }
    
    .post-image-wide,
    .post-content .ai-image-layout-wide {
        width: calc(100% + 1.5rem);
        margin-left: -0.75rem;
        margin-right: -0.75rem;
    }
    
    .post-image-left,
    .post-image-right,
    .post-content .ai-image-layout-left,
    .post-content .ai-image-layout-right {
        float: none;
        margin: 2rem 0;
        max-width: 100%;
        width: 100%;
    }
    
    .post-image-center,
    .post-content .ai-image-layout-center,
    .post-content .ai-image-layout-standard {
        max-width: 100%;
        margin: 2rem 0;
    }
    
    /* Mobile AI Gallery Layouts */
    .post-content .ai-image-layout-gallery-2-item .ai-gallery-grid,
    .post-content .ai-image-layout-gallery-3-item .ai-gallery-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    /* Mobile Text Overlay Layouts */
    .post-image-overlay {
        margin: 1.5rem 0;
    }
    
    .image-overlay-content {
        position: relative;
        background: var(--color-surface, #ffffff);
        color: var(--color-text-primary, #111827);
        padding: 1rem;
        border-top: 3px solid var(--color-primary, #3b82f6);
    }
    
    .image-overlay-content h1,
    .image-overlay-content h2,
    .image-overlay-content h3,
    .image-overlay-content h4,
    .image-overlay-content h5,
    .image-overlay-content h6 {
        color: var(--color-text-primary, #111827);
    }
    
    /* Mobile Floated Image Layouts */
    .post-image-float-wrapper {
        margin: 1.5rem 0;
    }
    
    .post-image-float-left .post-image,
    .post-image-float-right .post-image {
        float: none;
        margin: 0 0 1rem 0;
        max-width: 100%;
        width: 100%;
    }
    
    .post-image-adjacent-content {
        overflow: visible;
        margin-top: 1rem;
    }

    /* Mobile Gallery Layouts */
    .post-image-gallery-2-item,
    .post-image-gallery-3-item,
    .post-image-gallery-4-item {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
}

/* Fix for placeholders with incomplete layout classes */
.post-image-placeholder.post-image- {
    /* Default styling when layout class is incomplete */
    max-width: 600px;
    margin: 2rem auto;
}

/* Ensure gallery structure is properly displayed */
.post-image-gallery .gallery-item {
    margin: 0 !important;
    padding: 0 !important;
}

.post-image-gallery .gallery-placeholder .placeholder-icon {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    opacity: 0.6;
}

/* Ensure AI editor classes work on post page */
.post-content .ai-image-placeholder,
.post-content .ai-image-gallery,
.post-content .image-text-wrapper {
    /* Inherit all styling from the imported image-layouts.css */
    /* This ensures AI editor layouts display correctly on post page */
}

/* Make sure AI editor images display properly */
.post-content .ai-image-placeholder img.generated-image {
    width: 100%;
    height: auto;
    display: block;
    border-radius: 0.75rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

/* Ensure AI editor placeholders show properly */
.post-content .ai-image-placeholder .placeholder-content {
    text-align: center;
    padding: 2rem;
}

.post-content .ai-image-placeholder .placeholder-icon {
    font-size: 3rem;
    display: block;
    margin-bottom: 1rem;
    opacity: 0.6;
}

.post-content .ai-image-placeholder .placeholder-text {
    font-size: 1rem;
    color: var(--color-text-secondary, #6b7280);
    font-weight: 500;
}

.post-content .ai-image-placeholder .placeholder-alt {
    display: block;
    font-size: 0.875rem;
    color: var(--color-text-tertiary, #9ca3af);
    margin-top: 0.5rem;
    font-style: italic;
}

/* Fix AI image layout specific styles for post page */
.post-content .ai-image-layout-left {
    float: left;
    margin: 0 2rem 1rem 0;
    max-width: 50%;
    clear: left;
}

.post-content .ai-image-layout-right {
    float: right;
    margin: 0 0 1rem 2rem;
    max-width: 50%;
    clear: right;
}

.post-content .ai-image-layout-center,
.post-content .ai-image-layout-standard {
    margin: 2rem auto;
    text-align: center;
    max-width: 100%;
    display: block;
}

.post-content .ai-image-layout-wide {
    width: calc(100% + 4rem);
    margin: 2.5rem -2rem;
    max-width: none;
}

.post-content .ai-image-layout-full {
    width: 100vw;
    margin-left: 50%;
    transform: translateX(-50%);
    max-width: none;
}

.post-content .ai-image-layout-square {
    margin: 2rem auto;
    text-align: center;
    max-width: 400px;
    aspect-ratio: 1/1;
}

.post-content .ai-image-layout-square img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* Text overlay layout for post page */
.post-content .ai-image-layout-text_overlay {
    position: relative;
    margin: 2rem 0;
    border-radius: 0.75rem;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.post-content .ai-image-layout-text_overlay img {
    width: 100%;
    height: auto;
    display: block;
}

.post-content .ai-image-layout-text_overlay .image-text-overlay-content {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
    color: white;
    padding: 2rem 1.5rem 1.5rem;
    font-size: 1rem;
    line-height: 1.6;
    text-align: center;
}

/* Gallery layouts for post page */
.post-content .ai-image-gallery {
    display: grid;
    gap: 1rem;
    margin: 2rem 0;
}

.post-content .ai-image-layout-gallery-2-item .ai-gallery-grid {
    grid-template-columns: repeat(2, 1fr);
}

.post-content .ai-image-layout-gallery-3-item .ai-gallery-grid {
    grid-template-columns: repeat(3, 1fr);
}

.post-content .ai-gallery-grid .ai-image-placeholder {
    margin: 0;
    aspect-ratio: 1/1;
}

/* Image text wrapper for floated layouts */
.post-content .image-text-wrapper {
    margin: 2rem 0;
    overflow: hidden;
    display: flow-root;
}

.post-content .image-text-wrapper .adjacent-text-content {
    overflow: hidden;
    line-height: 1.6;
    color: var(--color-text-primary, #111827);
}
    .post-image-square {
        max-width: 100%;
    }
    
    .post-image-square img {
        width: 100%;
        height: 300px;
        max-width: 100%;
    }
    
    .gallery-item img {
        height: 150px;
    }
    
    /* Mobile Placeholder Layouts */
    .post-image-placeholder.post-image-full {
        width: calc(100vw - 1.5rem);
        margin-left: -0.75rem;
        margin-right: -0.75rem;
        transform: none;
    }
    
    .post-image-placeholder.post-image-wide {
        width: calc(100% + 1.5rem);
        margin-left: -0.75rem;
        margin-right: -0.75rem;
    }
    
    .post-image-placeholder.post-image-left,
    .post-image-placeholder.post-image-right {
        float: none;
        margin: 2rem 0;
        max-width: 100%;
        width: 100%;
    }
    
    .post-image-placeholder.post-image-center {
        max-width: 100%;
        margin: 2rem 0;
    }
    
    .comments-section {
        padding: 1.5rem;
        margin-top: 2rem;
    }
    
    .comment-form {
        padding: 1.5rem;
    }
    
    .form-row {
        grid-template-columns: 1fr;
        gap: 0;
    }
    
    .comment-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.25rem;
    }
}

@media (max-width: 480px) {
    .post-container {
        padding: 0.5rem;
    }
    
    .post-title {
        font-size: 1.75rem;
    }
    
    .post-content-inner h1 { font-size: 1.75rem; }
    .post-content-inner h2 { font-size: 1.5rem; }
    .post-content-inner h3 { font-size: 1.25rem; }
    
    .comments-section {
        padding: 1rem;
    }
    
    .comment {
        padding: 1rem;
    }
    
    .comment-form {
        padding: 1rem;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .post-container {
        background: var(--color-surface-dark, #1f2937);
        color: var(--color-text-primary-dark, #f9fafb);
    }
    
    .post-title,
    .comments-title,
    .comment-form-title {
        color: var(--color-text-primary-dark, #f9fafb);
    }
    
    .comment {
        background: var(--color-surface-dark, #374151);
        border-color: var(--color-border-dark, #4b5563);
    }
    
    .comment-form {
        background: var(--color-surface-dark, #374151);
        border-color: var(--color-border-dark, #4b5563);
    }
    
    .form-group input,
    .form-group textarea {
        background: var(--color-surface-dark, #1f2937);
        border-color: var(--color-border-dark, #4b5563);
        color: var(--color-text-primary-dark, #f9fafb);
    }
}